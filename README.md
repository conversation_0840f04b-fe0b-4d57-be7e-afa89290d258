# MSX Super Team - Portal de Gestión de Empleados

Un portal moderno y visual para la gestión de empleados y horarios del equipo MSX International.

## 🚀 Características

- **Dashboard Interactivo**: Vista general con estadísticas en tiempo real
- **Gestión de Empleados**: Información detallada de cada empleado
- **Calendario de Horarios**: Vista mensual de horarios y ausencias
- **Filtros por Departamento**: Organización por departamentos (IBERIA, ITALIA, FRANCIA, etc.)
- **Estadísticas Visuales**: Gráficos y métricas de presencia y ausencias
- **Diseño Responsive**: Optimizado para desktop y móvil
- **Persistencia en Supabase**: Base de datos en la nube

## 🏢 Departamentos

- **IBERIA** - Operaciones España
- **ITALIA** - Operaciones Italia  
- **FRANCIA** - Operaciones Francia
- **CROSS OPERACIONES** - Operaciones transversales
- **IT SUPPORT** - Soporte técnico
- **DATA ANALYST** - Análisis de datos
- **KB** - Knowledge Base
- **TRAINERS** - Formadores
- **HR** - Recursos Humanos
- **ACTIVE CALL** - Llamadas activas

## 🛠️ Tecnologías

- **Frontend**: React 18 + Vite
- **Styling**: Tailwind CSS
- **Base de Datos**: Supabase
- **Iconos**: Lucide React
- **Gráficos**: Recharts
- **Routing**: React Router DOM

## 📦 Instalación

1. **Clonar el repositorio**
   ```bash
   git clone <repository-url>
   cd msxteam
   ```

2. **Instalar dependencias**
   ```bash
   npm install
   ```

3. **Configurar Supabase**
   - Actualizar las credenciales en `src/lib/supabase.js`
   - Configurar las tablas necesarias en Supabase

4. **Ejecutar en desarrollo**
   ```bash
   npm run dev
   ```

5. **Construir para producción**
   ```bash
   npm run build
   ```

## 🗄️ Estructura de Base de Datos

### Tabla `departments`
- `id` (UUID) - Identificador único
- `name` (VARCHAR) - Nombre del departamento
- `description` (TEXT) - Descripción
- `active` (BOOLEAN) - Estado activo
- `color` (VARCHAR) - Color para la UI

### Tabla `employees`
- `id` (UUID) - Identificador único
- `employee_id` (VARCHAR) - ID del empleado
- `name` (VARCHAR) - Nombre corto
- `full_name` (VARCHAR) - Nombre completo
- `country_code` (VARCHAR) - Código de país
- `department` (VARCHAR) - Departamento
- `email` (VARCHAR) - Email
- `active` (BOOLEAN) - Estado activo

### Tabla `schedules`
- `id` (UUID) - Identificador único
- `employee_id` (UUID) - Referencia al empleado
- `schedule_date` (DATE) - Fecha del horario
- `day_of_week` (VARCHAR) - Día de la semana
- `schedule_text` (TEXT) - Texto del horario
- `status` (VARCHAR) - Estado (WORK, VACATION, SICK_LEAVE, etc.)
- `month` (VARCHAR) - Mes

## 🎨 Funcionalidades

### Dashboard Principal
- Estadísticas generales de empleados
- Contadores de activos, vacaciones y ausencias
- Gráficos por departamento
- Lista de empleados con filtros

### Vista de Calendario
- Calendario mensual interactivo
- Códigos de color por tipo de horario
- Filtros por departamento
- Navegación entre meses

### Perfil de Empleado
- Información detallada del empleado
- Historial de horarios
- Estado actual y contacto

## 🚀 Despliegue

El proyecto está configurado para desplegarse fácilmente en:
- **Netlify** (recomendado)
- **Vercel**
- **GitHub Pages**

## 📱 Responsive Design

El portal está optimizado para:
- **Desktop** (1024px+)
- **Tablet** (768px - 1023px)
- **Mobile** (320px - 767px)

## 🔧 Configuración

### Variables de Entorno
Crear un archivo `.env` con:
```
VITE_SUPABASE_URL=tu_supabase_url
VITE_SUPABASE_ANON_KEY=tu_supabase_anon_key
```

### Personalización
- Colores: Modificar `tailwind.config.js`
- Componentes: Archivos en `src/components/`
- Estilos: `src/index.css`

## 👥 Equipo MSX

Portal desarrollado para el equipo MSX International con 26 empleados distribuidos en 10 departamentos.

## 📄 Licencia

Proyecto interno de MSX International.

---

**MSX Super Team** - Portal de Gestión de Empleados v1.0.0
