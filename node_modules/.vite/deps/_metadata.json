{"hash": "37100ed1", "browserHash": "2da6fa4d", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "41a6fa81", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "f7e7f280", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "49adedc1", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "2aa553ea", "needsInterop": true}, "@supabase/supabase-js": {"src": "../../@supabase/supabase-js/dist/module/index.js", "file": "@supabase_supabase-js.js", "fileHash": "0da0c176", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "38e6239c", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "d7a60115", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "2fe41f7b", "needsInterop": false}, "recharts": {"src": "../../recharts/es6/index.js", "file": "recharts.js", "fileHash": "db8e2363", "needsInterop": false}}, "chunks": {"browser-ZONL5W77": {"file": "browser-ZONL5W77.js"}, "chunk-QH3POG6S": {"file": "chunk-QH3POG6S.js"}, "chunk-G52XTN3B": {"file": "chunk-G52XTN3B.js"}, "chunk-LXGCQ6UQ": {"file": "chunk-LXGCQ6UQ.js"}, "chunk-ROME4SDB": {"file": "chunk-ROME4SDB.js"}}}