/*! For license information please see Recharts.js.LICENSE.txt */
!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(require("react"),require("prop-types")):"function"==typeof define&&define.amd?define(["react","prop-types"],e):"object"==typeof exports?exports.Recharts=e(require("react"),require("prop-types")):t.Recharts=e(t.React,t.PropTypes)}(this,((t,e)=>(()=>{var r={7996:function(t,e,r){var n;!function(o){"use strict";var i,a=1e9,c={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},u=!0,l="[DecimalError] ",s=l+"Invalid argument: ",f=l+"Exponent out of range: ",p=Math.floor,h=Math.pow,y=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,d=1e7,v=7,m=9007199254740991,b=p(m/v),g={};function x(t,e){var r,n,o,i,a,c,l,s,f=t.constructor,p=f.precision;if(!t.s||!e.s)return e.s||(e=new f(t)),u?M(e,p):e;if(l=t.d,s=e.d,a=t.e,o=e.e,l=l.slice(),i=a-o){for(i<0?(n=l,i=-i,c=s.length):(n=s,o=a,c=l.length),i>(c=(a=Math.ceil(p/v))>c?a+1:c+1)&&(i=c,n.length=1),n.reverse();i--;)n.push(0);n.reverse()}for((c=l.length)-(i=s.length)<0&&(i=c,n=s,s=l,l=n),r=0;i;)r=(l[--i]=l[i]+s[i]+r)/d|0,l[i]%=d;for(r&&(l.unshift(r),++o),c=l.length;0==l[--c];)l.pop();return e.d=l,e.e=o,u?M(e,p):e}function w(t,e,r){if(t!==~~t||t<e||t>r)throw Error(s+t)}function O(t){var e,r,n,o=t.length-1,i="",a=t[0];if(o>0){for(i+=a,e=1;e<o;e++)n=t[e]+"",(r=v-n.length)&&(i+=E(r)),i+=n;a=t[e],(r=v-(n=a+"").length)&&(i+=E(r))}else if(0===a)return"0";for(;a%10==0;)a/=10;return i+a}g.absoluteValue=g.abs=function(){var t=new this.constructor(this);return t.s&&(t.s=1),t},g.comparedTo=g.cmp=function(t){var e,r,n,o,i=this;if(t=new i.constructor(t),i.s!==t.s)return i.s||-t.s;if(i.e!==t.e)return i.e>t.e^i.s<0?1:-1;for(e=0,r=(n=i.d.length)<(o=t.d.length)?n:o;e<r;++e)if(i.d[e]!==t.d[e])return i.d[e]>t.d[e]^i.s<0?1:-1;return n===o?0:n>o^i.s<0?1:-1},g.decimalPlaces=g.dp=function(){var t=this,e=t.d.length-1,r=(e-t.e)*v;if(e=t.d[e])for(;e%10==0;e/=10)r--;return r<0?0:r},g.dividedBy=g.div=function(t){return j(this,new this.constructor(t))},g.dividedToIntegerBy=g.idiv=function(t){var e=this.constructor;return M(j(this,new e(t),0,1),e.precision)},g.equals=g.eq=function(t){return!this.cmp(t)},g.exponent=function(){return A(this)},g.greaterThan=g.gt=function(t){return this.cmp(t)>0},g.greaterThanOrEqualTo=g.gte=function(t){return this.cmp(t)>=0},g.isInteger=g.isint=function(){return this.e>this.d.length-2},g.isNegative=g.isneg=function(){return this.s<0},g.isPositive=g.ispos=function(){return this.s>0},g.isZero=function(){return 0===this.s},g.lessThan=g.lt=function(t){return this.cmp(t)<0},g.lessThanOrEqualTo=g.lte=function(t){return this.cmp(t)<1},g.logarithm=g.log=function(t){var e,r=this,n=r.constructor,o=n.precision,a=o+5;if(void 0===t)t=new n(10);else if((t=new n(t)).s<1||t.eq(i))throw Error(l+"NaN");if(r.s<1)throw Error(l+(r.s?"NaN":"-Infinity"));return r.eq(i)?new n(0):(u=!1,e=j(k(r,a),k(t,a),a),u=!0,M(e,o))},g.minus=g.sub=function(t){var e=this;return t=new e.constructor(t),e.s==t.s?_(e,t):x(e,(t.s=-t.s,t))},g.modulo=g.mod=function(t){var e,r=this,n=r.constructor,o=n.precision;if(!(t=new n(t)).s)throw Error(l+"NaN");return r.s?(u=!1,e=j(r,t,0,1).times(t),u=!0,r.minus(e)):M(new n(r),o)},g.naturalExponential=g.exp=function(){return S(this)},g.naturalLogarithm=g.ln=function(){return k(this)},g.negated=g.neg=function(){var t=new this.constructor(this);return t.s=-t.s||0,t},g.plus=g.add=function(t){var e=this;return t=new e.constructor(t),e.s==t.s?x(e,t):_(e,(t.s=-t.s,t))},g.precision=g.sd=function(t){var e,r,n,o=this;if(void 0!==t&&t!==!!t&&1!==t&&0!==t)throw Error(s+t);if(e=A(o)+1,r=(n=o.d.length-1)*v+1,n=o.d[n]){for(;n%10==0;n/=10)r--;for(n=o.d[0];n>=10;n/=10)r++}return t&&e>r?e:r},g.squareRoot=g.sqrt=function(){var t,e,r,n,o,i,a,c=this,s=c.constructor;if(c.s<1){if(!c.s)return new s(0);throw Error(l+"NaN")}for(t=A(c),u=!1,0==(o=Math.sqrt(+c))||o==1/0?(((e=O(c.d)).length+t)%2==0&&(e+="0"),o=Math.sqrt(e),t=p((t+1)/2)-(t<0||t%2),n=new s(e=o==1/0?"5e"+t:(e=o.toExponential()).slice(0,e.indexOf("e")+1)+t)):n=new s(o.toString()),o=a=(r=s.precision)+3;;)if(n=(i=n).plus(j(c,i,a+2)).times(.5),O(i.d).slice(0,a)===(e=O(n.d)).slice(0,a)){if(e=e.slice(a-3,a+1),o==a&&"4999"==e){if(M(i,r+1,0),i.times(i).eq(c)){n=i;break}}else if("9999"!=e)break;a+=4}return u=!0,M(n,r)},g.times=g.mul=function(t){var e,r,n,o,i,a,c,l,s,f=this,p=f.constructor,h=f.d,y=(t=new p(t)).d;if(!f.s||!t.s)return new p(0);for(t.s*=f.s,r=f.e+t.e,(l=h.length)<(s=y.length)&&(i=h,h=y,y=i,a=l,l=s,s=a),i=[],n=a=l+s;n--;)i.push(0);for(n=s;--n>=0;){for(e=0,o=l+n;o>n;)c=i[o]+y[n]*h[o-n-1]+e,i[o--]=c%d|0,e=c/d|0;i[o]=(i[o]+e)%d|0}for(;!i[--a];)i.pop();return e?++r:i.shift(),t.d=i,t.e=r,u?M(t,p.precision):t},g.toDecimalPlaces=g.todp=function(t,e){var r=this,n=r.constructor;return r=new n(r),void 0===t?r:(w(t,0,a),void 0===e?e=n.rounding:w(e,0,8),M(r,t+A(r)+1,e))},g.toExponential=function(t,e){var r,n=this,o=n.constructor;return void 0===t?r=C(n,!0):(w(t,0,a),void 0===e?e=o.rounding:w(e,0,8),r=C(n=M(new o(n),t+1,e),!0,t+1)),r},g.toFixed=function(t,e){var r,n,o=this,i=o.constructor;return void 0===t?C(o):(w(t,0,a),void 0===e?e=i.rounding:w(e,0,8),r=C((n=M(new i(o),t+A(o)+1,e)).abs(),!1,t+A(n)+1),o.isneg()&&!o.isZero()?"-"+r:r)},g.toInteger=g.toint=function(){var t=this,e=t.constructor;return M(new e(t),A(t)+1,e.rounding)},g.toNumber=function(){return+this},g.toPower=g.pow=function(t){var e,r,n,o,a,c,s=this,f=s.constructor,h=+(t=new f(t));if(!t.s)return new f(i);if(!(s=new f(s)).s){if(t.s<1)throw Error(l+"Infinity");return s}if(s.eq(i))return s;if(n=f.precision,t.eq(i))return M(s,n);if(c=(e=t.e)>=(r=t.d.length-1),a=s.s,c){if((r=h<0?-h:h)<=m){for(o=new f(i),e=Math.ceil(n/v+4),u=!1;r%2&&D((o=o.times(s)).d,e),0!==(r=p(r/2));)D((s=s.times(s)).d,e);return u=!0,t.s<0?new f(i).div(o):M(o,n)}}else if(a<0)throw Error(l+"NaN");return a=a<0&&1&t.d[Math.max(e,r)]?-1:1,s.s=1,u=!1,o=t.times(k(s,n+12)),u=!0,(o=S(o)).s=a,o},g.toPrecision=function(t,e){var r,n,o=this,i=o.constructor;return void 0===t?n=C(o,(r=A(o))<=i.toExpNeg||r>=i.toExpPos):(w(t,1,a),void 0===e?e=i.rounding:w(e,0,8),n=C(o=M(new i(o),t,e),t<=(r=A(o))||r<=i.toExpNeg,t)),n},g.toSignificantDigits=g.tosd=function(t,e){var r=this.constructor;return void 0===t?(t=r.precision,e=r.rounding):(w(t,1,a),void 0===e?e=r.rounding:w(e,0,8)),M(new r(this),t,e)},g.toString=g.valueOf=g.val=g.toJSON=function(){var t=this,e=A(t),r=t.constructor;return C(t,e<=r.toExpNeg||e>=r.toExpPos)};var j=function(){function t(t,e){var r,n=0,o=t.length;for(t=t.slice();o--;)r=t[o]*e+n,t[o]=r%d|0,n=r/d|0;return n&&t.unshift(n),t}function e(t,e,r,n){var o,i;if(r!=n)i=r>n?1:-1;else for(o=i=0;o<r;o++)if(t[o]!=e[o]){i=t[o]>e[o]?1:-1;break}return i}function r(t,e,r){for(var n=0;r--;)t[r]-=n,n=t[r]<e[r]?1:0,t[r]=n*d+t[r]-e[r];for(;!t[0]&&t.length>1;)t.shift()}return function(n,o,i,a){var c,u,s,f,p,h,y,m,b,g,x,w,O,j,S,P,E,k,T=n.constructor,_=n.s==o.s?1:-1,C=n.d,D=o.d;if(!n.s)return new T(n);if(!o.s)throw Error(l+"Division by zero");for(u=n.e-o.e,E=D.length,S=C.length,m=(y=new T(_)).d=[],s=0;D[s]==(C[s]||0);)++s;if(D[s]>(C[s]||0)&&--u,(w=null==i?i=T.precision:a?i+(A(n)-A(o))+1:i)<0)return new T(0);if(w=w/v+2|0,s=0,1==E)for(f=0,D=D[0],w++;(s<S||f)&&w--;s++)O=f*d+(C[s]||0),m[s]=O/D|0,f=O%D|0;else{for((f=d/(D[0]+1)|0)>1&&(D=t(D,f),C=t(C,f),E=D.length,S=C.length),j=E,g=(b=C.slice(0,E)).length;g<E;)b[g++]=0;(k=D.slice()).unshift(0),P=D[0],D[1]>=d/2&&++P;do{f=0,(c=e(D,b,E,g))<0?(x=b[0],E!=g&&(x=x*d+(b[1]||0)),(f=x/P|0)>1?(f>=d&&(f=d-1),1==(c=e(p=t(D,f),b,h=p.length,g=b.length))&&(f--,r(p,E<h?k:D,h))):(0==f&&(c=f=1),p=D.slice()),(h=p.length)<g&&p.unshift(0),r(b,p,g),-1==c&&(c=e(D,b,E,g=b.length))<1&&(f++,r(b,E<g?k:D,g)),g=b.length):0===c&&(f++,b=[0]),m[s++]=f,c&&b[0]?b[g++]=C[j]||0:(b=[C[j]],g=1)}while((j++<S||void 0!==b[0])&&w--)}return m[0]||m.shift(),y.e=u,M(y,a?i+A(y)+1:i)}}();function S(t,e){var r,n,o,a,c,l=0,s=0,p=t.constructor,y=p.precision;if(A(t)>16)throw Error(f+A(t));if(!t.s)return new p(i);for(null==e?(u=!1,c=y):c=e,a=new p(.03125);t.abs().gte(.1);)t=t.times(a),s+=5;for(c+=Math.log(h(2,s))/Math.LN10*2+5|0,r=n=o=new p(i),p.precision=c;;){if(n=M(n.times(t),c),r=r.times(++l),O((a=o.plus(j(n,r,c))).d).slice(0,c)===O(o.d).slice(0,c)){for(;s--;)o=M(o.times(o),c);return p.precision=y,null==e?(u=!0,M(o,y)):o}o=a}}function A(t){for(var e=t.e*v,r=t.d[0];r>=10;r/=10)e++;return e}function P(t,e,r){if(e>t.LN10.sd())throw u=!0,r&&(t.precision=r),Error(l+"LN10 precision limit exceeded");return M(new t(t.LN10),e)}function E(t){for(var e="";t--;)e+="0";return e}function k(t,e){var r,n,o,a,c,s,f,p,h,y=1,d=t,v=d.d,m=d.constructor,b=m.precision;if(d.s<1)throw Error(l+(d.s?"NaN":"-Infinity"));if(d.eq(i))return new m(0);if(null==e?(u=!1,p=b):p=e,d.eq(10))return null==e&&(u=!0),P(m,p);if(p+=10,m.precision=p,n=(r=O(v)).charAt(0),a=A(d),!(Math.abs(a)<15e14))return f=P(m,p+2,b).times(a+""),d=k(new m(n+"."+r.slice(1)),p-10).plus(f),m.precision=b,null==e?(u=!0,M(d,b)):d;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=O((d=d.times(t)).d)).charAt(0),y++;for(a=A(d),n>1?(d=new m("0."+r),a++):d=new m(n+"."+r.slice(1)),s=c=d=j(d.minus(i),d.plus(i),p),h=M(d.times(d),p),o=3;;){if(c=M(c.times(h),p),O((f=s.plus(j(c,new m(o),p))).d).slice(0,p)===O(s.d).slice(0,p))return s=s.times(2),0!==a&&(s=s.plus(P(m,p+2,b).times(a+""))),s=j(s,new m(y),p),m.precision=b,null==e?(u=!0,M(s,b)):s;s=f,o+=2}}function T(t,e){var r,n,o;for((r=e.indexOf("."))>-1&&(e=e.replace(".","")),(n=e.search(/e/i))>0?(r<0&&(r=n),r+=+e.slice(n+1),e=e.substring(0,n)):r<0&&(r=e.length),n=0;48===e.charCodeAt(n);)++n;for(o=e.length;48===e.charCodeAt(o-1);)--o;if(e=e.slice(n,o)){if(o-=n,r=r-n-1,t.e=p(r/v),t.d=[],n=(r+1)%v,r<0&&(n+=v),n<o){for(n&&t.d.push(+e.slice(0,n)),o-=v;n<o;)t.d.push(+e.slice(n,n+=v));e=e.slice(n),n=v-e.length}else n-=o;for(;n--;)e+="0";if(t.d.push(+e),u&&(t.e>b||t.e<-b))throw Error(f+r)}else t.s=0,t.e=0,t.d=[0];return t}function M(t,e,r){var n,o,i,a,c,l,s,y,m=t.d;for(a=1,i=m[0];i>=10;i/=10)a++;if((n=e-a)<0)n+=v,o=e,s=m[y=0];else{if((y=Math.ceil((n+1)/v))>=(i=m.length))return t;for(s=i=m[y],a=1;i>=10;i/=10)a++;o=(n%=v)-v+a}if(void 0!==r&&(c=s/(i=h(10,a-o-1))%10|0,l=e<0||void 0!==m[y+1]||s%i,l=r<4?(c||l)&&(0==r||r==(t.s<0?3:2)):c>5||5==c&&(4==r||l||6==r&&(n>0?o>0?s/h(10,a-o):0:m[y-1])%10&1||r==(t.s<0?8:7))),e<1||!m[0])return l?(i=A(t),m.length=1,e=e-i-1,m[0]=h(10,(v-e%v)%v),t.e=p(-e/v)||0):(m.length=1,m[0]=t.e=t.s=0),t;if(0==n?(m.length=y,i=1,y--):(m.length=y+1,i=h(10,v-n),m[y]=o>0?(s/h(10,a-o)%h(10,o)|0)*i:0),l)for(;;){if(0==y){(m[0]+=i)==d&&(m[0]=1,++t.e);break}if(m[y]+=i,m[y]!=d)break;m[y--]=0,i=1}for(n=m.length;0===m[--n];)m.pop();if(u&&(t.e>b||t.e<-b))throw Error(f+A(t));return t}function _(t,e){var r,n,o,i,a,c,l,s,f,p,h=t.constructor,y=h.precision;if(!t.s||!e.s)return e.s?e.s=-e.s:e=new h(t),u?M(e,y):e;if(l=t.d,p=e.d,n=e.e,s=t.e,l=l.slice(),a=s-n){for((f=a<0)?(r=l,a=-a,c=p.length):(r=p,n=s,c=l.length),a>(o=Math.max(Math.ceil(y/v),c)+2)&&(a=o,r.length=1),r.reverse(),o=a;o--;)r.push(0);r.reverse()}else{for((f=(o=l.length)<(c=p.length))&&(c=o),o=0;o<c;o++)if(l[o]!=p[o]){f=l[o]<p[o];break}a=0}for(f&&(r=l,l=p,p=r,e.s=-e.s),c=l.length,o=p.length-c;o>0;--o)l[c++]=0;for(o=p.length;o>a;){if(l[--o]<p[o]){for(i=o;i&&0===l[--i];)l[i]=d-1;--l[i],l[o]+=d}l[o]-=p[o]}for(;0===l[--c];)l.pop();for(;0===l[0];l.shift())--n;return l[0]?(e.d=l,e.e=n,u?M(e,y):e):new h(0)}function C(t,e,r){var n,o=A(t),i=O(t.d),a=i.length;return e?(r&&(n=r-a)>0?i=i.charAt(0)+"."+i.slice(1)+E(n):a>1&&(i=i.charAt(0)+"."+i.slice(1)),i=i+(o<0?"e":"e+")+o):o<0?(i="0."+E(-o-1)+i,r&&(n=r-a)>0&&(i+=E(n))):o>=a?(i+=E(o+1-a),r&&(n=r-o-1)>0&&(i=i+"."+E(n))):((n=o+1)<a&&(i=i.slice(0,n)+"."+i.slice(n)),r&&(n=r-a)>0&&(o+1===a&&(i+="."),i+=E(n))),t.s<0?"-"+i:i}function D(t,e){if(t.length>e)return t.length=e,!0}function I(t){if(!t||"object"!=typeof t)throw Error(l+"Object expected");var e,r,n,o=["precision",1,a,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(e=0;e<o.length;e+=3)if(void 0!==(n=t[r=o[e]])){if(!(p(n)===n&&n>=o[e+1]&&n<=o[e+2]))throw Error(s+r+": "+n);this[r]=n}if(void 0!==(n=t[r="LN10"])){if(n!=Math.LN10)throw Error(s+r+": "+n);this[r]=new this(n)}return this}c=function t(e){var r,n,o;function i(t){var e=this;if(!(e instanceof i))return new i(t);if(e.constructor=i,t instanceof i)return e.s=t.s,e.e=t.e,void(e.d=(t=t.d)?t.slice():t);if("number"==typeof t){if(0*t!=0)throw Error(s+t);if(t>0)e.s=1;else{if(!(t<0))return e.s=0,e.e=0,void(e.d=[0]);t=-t,e.s=-1}return t===~~t&&t<1e7?(e.e=0,void(e.d=[t])):T(e,t.toString())}if("string"!=typeof t)throw Error(s+t);if(45===t.charCodeAt(0)?(t=t.slice(1),e.s=-1):e.s=1,!y.test(t))throw Error(s+t);T(e,t)}if(i.prototype=g,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=t,i.config=i.set=I,void 0===e&&(e={}),e)for(o=["precision","rounding","toExpNeg","toExpPos","LN10"],r=0;r<o.length;)e.hasOwnProperty(n=o[r++])||(e[n]=this[n]);return i.config(e),i}(c),c.default=c.Decimal=c,i=new c(1),void 0===(n=function(){return c}.call(e,r,e,t))||(t.exports=n)}()},9328:t=>{"use strict";var e=Object.prototype.hasOwnProperty,r="~";function n(){}function o(t,e,r){this.fn=t,this.context=e,this.once=r||!1}function i(t,e,n,i,a){if("function"!=typeof n)throw new TypeError("The listener must be a function");var c=new o(n,i||t,a),u=r?r+e:e;return t._events[u]?t._events[u].fn?t._events[u]=[t._events[u],c]:t._events[u].push(c):(t._events[u]=c,t._eventsCount++),t}function a(t,e){0==--t._eventsCount?t._events=new n:delete t._events[e]}function c(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),(new n).__proto__||(r=!1)),c.prototype.eventNames=function(){var t,n,o=[];if(0===this._eventsCount)return o;for(n in t=this._events)e.call(t,n)&&o.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(t)):o},c.prototype.listeners=function(t){var e=r?r+t:t,n=this._events[e];if(!n)return[];if(n.fn)return[n.fn];for(var o=0,i=n.length,a=new Array(i);o<i;o++)a[o]=n[o].fn;return a},c.prototype.listenerCount=function(t){var e=r?r+t:t,n=this._events[e];return n?n.fn?1:n.length:0},c.prototype.emit=function(t,e,n,o,i,a){var c=r?r+t:t;if(!this._events[c])return!1;var u,l,s=this._events[c],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(t,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,e),!0;case 3:return s.fn.call(s.context,e,n),!0;case 4:return s.fn.call(s.context,e,n,o),!0;case 5:return s.fn.call(s.context,e,n,o,i),!0;case 6:return s.fn.call(s.context,e,n,o,i,a),!0}for(l=1,u=new Array(f-1);l<f;l++)u[l-1]=arguments[l];s.fn.apply(s.context,u)}else{var p,h=s.length;for(l=0;l<h;l++)switch(s[l].once&&this.removeListener(t,s[l].fn,void 0,!0),f){case 1:s[l].fn.call(s[l].context);break;case 2:s[l].fn.call(s[l].context,e);break;case 3:s[l].fn.call(s[l].context,e,n);break;case 4:s[l].fn.call(s[l].context,e,n,o);break;default:if(!u)for(p=1,u=new Array(f-1);p<f;p++)u[p-1]=arguments[p];s[l].fn.apply(s[l].context,u)}}return!0},c.prototype.on=function(t,e,r){return i(this,t,e,r,!1)},c.prototype.once=function(t,e,r){return i(this,t,e,r,!0)},c.prototype.removeListener=function(t,e,n,o){var i=r?r+t:t;if(!this._events[i])return this;if(!e)return a(this,i),this;var c=this._events[i];if(c.fn)c.fn!==e||o&&!c.once||n&&c.context!==n||a(this,i);else{for(var u=0,l=[],s=c.length;u<s;u++)(c[u].fn!==e||o&&!c[u].once||n&&c[u].context!==n)&&l.push(c[u]);l.length?this._events[i]=1===l.length?l[0]:l:a(this,i)}return this},c.prototype.removeAllListeners=function(t){var e;return t?(e=r?r+t:t,this._events[e]&&a(this,e)):(this._events=new n,this._eventsCount=0),this},c.prototype.off=c.prototype.removeListener,c.prototype.addListener=c.prototype.on,c.prefixed=r,c.EventEmitter=c,t.exports=c},6032:(t,e,r)=>{var n=r(7892)(r(7188),"DataView");t.exports=n},1276:(t,e,r)=>{var n=r(4212),o=r(2688),i=r(3916),a=r(6952),c=r(1016);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=c,t.exports=u},3040:(t,e,r)=>{var n=r(5968),o=r(3740),i=r(4996),a=r(2600),c=r(7336);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=c,t.exports=u},420:(t,e,r)=>{var n=r(7892)(r(7188),"Map");t.exports=n},1476:(t,e,r)=>{var n=r(8720),o=r(4760),i=r(88),a=r(9776),c=r(8619);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=c,t.exports=u},404:(t,e,r)=>{var n=r(7892)(r(7188),"Promise");t.exports=n},6920:(t,e,r)=>{var n=r(7892)(r(7188),"Set");t.exports=n},6152:(t,e,r)=>{var n=r(1476),o=r(9516),i=r(3504);function a(t){var e=-1,r=null==t?0:t.length;for(this.__data__=new n;++e<r;)this.add(t[e])}a.prototype.add=a.prototype.push=o,a.prototype.has=i,t.exports=a},520:(t,e,r)=>{var n=r(3040),o=r(5643),i=r(3368),a=r(636),c=r(3012),u=r(3388);function l(t){var e=this.__data__=new n(t);this.size=e.size}l.prototype.clear=o,l.prototype.delete=i,l.prototype.get=a,l.prototype.has=c,l.prototype.set=u,t.exports=l},7128:(t,e,r)=>{var n=r(7188).Symbol;t.exports=n},9704:(t,e,r)=>{var n=r(7188).Uint8Array;t.exports=n},5200:(t,e,r)=>{var n=r(7892)(r(7188),"WeakMap");t.exports=n},2253:t=>{t.exports=function(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)}},6064:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n&&!1!==e(t[r],r,t););return t}},3908:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(!e(t[r],r,t))return!1;return!0}},8640:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,o=0,i=[];++r<n;){var a=t[r];e(a,r,t)&&(i[o++]=a)}return i}},8688:(t,e,r)=>{var n=r(3992);t.exports=function(t,e){return!!(null==t?0:t.length)&&n(t,e,0)>-1}},5336:t=>{t.exports=function(t,e,r){for(var n=-1,o=null==t?0:t.length;++n<o;)if(r(e,t[n]))return!0;return!1}},7640:(t,e,r)=>{var n=r(736),o=r(348),i=r(2488),a=r(7684),c=r(1188),u=r(6700),l=Object.prototype.hasOwnProperty;t.exports=function(t,e){var r=i(t),s=!r&&o(t),f=!r&&!s&&a(t),p=!r&&!s&&!f&&u(t),h=r||s||f||p,y=h?n(t.length,String):[],d=y.length;for(var v in t)!e&&!l.call(t,v)||h&&("length"==v||f&&("offset"==v||"parent"==v)||p&&("buffer"==v||"byteLength"==v||"byteOffset"==v)||c(v,d))||y.push(v);return y}},2040:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,o=Array(n);++r<n;)o[r]=e(t[r],r,t);return o}},1168:t=>{t.exports=function(t,e){for(var r=-1,n=e.length,o=t.length;++r<n;)t[o+r]=e[r];return t}},5600:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(e(t[r],r,t))return!0;return!1}},1120:t=>{t.exports=function(t){return t.split("")}},8288:(t,e,r)=>{var n=r(6139),o=r(864),i=Object.prototype.hasOwnProperty;t.exports=function(t,e,r){var a=t[e];i.call(t,e)&&o(a,r)&&(void 0!==r||e in t)||n(t,e,r)}},6600:(t,e,r)=>{var n=r(864);t.exports=function(t,e){for(var r=t.length;r--;)if(n(t[r][0],e))return r;return-1}},8040:(t,e,r)=>{var n=r(7612),o=r(5160);t.exports=function(t,e){return t&&n(e,o(e),t)}},4244:(t,e,r)=>{var n=r(7612),o=r(2756);t.exports=function(t,e){return t&&n(e,o(e),t)}},6139:(t,e,r)=>{var n=r(7792);t.exports=function(t,e,r){"__proto__"==e&&n?n(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r}},5620:(t,e,r)=>{var n=r(520),o=r(6064),i=r(8288),a=r(8040),c=r(4244),u=r(1328),l=r(8416),s=r(8219),f=r(792),p=r(1096),h=r(8856),y=r(3871),d=r(4636),v=r(7576),m=r(6224),b=r(2488),g=r(7684),x=r(8544),w=r(8940),O=r(9504),j=r(5160),S=r(2756),A="[object Arguments]",P="[object Function]",E="[object Object]",k={};k[A]=k["[object Array]"]=k["[object ArrayBuffer]"]=k["[object DataView]"]=k["[object Boolean]"]=k["[object Date]"]=k["[object Float32Array]"]=k["[object Float64Array]"]=k["[object Int8Array]"]=k["[object Int16Array]"]=k["[object Int32Array]"]=k["[object Map]"]=k["[object Number]"]=k[E]=k["[object RegExp]"]=k["[object Set]"]=k["[object String]"]=k["[object Symbol]"]=k["[object Uint8Array]"]=k["[object Uint8ClampedArray]"]=k["[object Uint16Array]"]=k["[object Uint32Array]"]=!0,k["[object Error]"]=k[P]=k["[object WeakMap]"]=!1,t.exports=function t(e,r,T,M,_,C){var D,I=1&r,N=2&r,R=4&r;if(T&&(D=_?T(e,M,_,C):T(e)),void 0!==D)return D;if(!w(e))return e;var B=b(e);if(B){if(D=d(e),!I)return l(e,D)}else{var L=y(e),z=L==P||"[object GeneratorFunction]"==L;if(g(e))return u(e,I);if(L==E||L==A||z&&!_){if(D=N||z?{}:m(e),!I)return N?f(e,c(D,e)):s(e,a(D,e))}else{if(!k[L])return _?e:{};D=v(e,L,I)}}C||(C=new n);var F=C.get(e);if(F)return F;C.set(e,D),O(e)?e.forEach((function(n){D.add(t(n,r,T,n,e,C))})):x(e)&&e.forEach((function(n,o){D.set(o,t(n,r,T,o,e,C))}));var U=B?void 0:(R?N?h:p:N?S:j)(e);return o(U||e,(function(n,o){U&&(n=e[o=n]),i(D,o,t(n,r,T,o,e,C))})),D}},2471:(t,e,r)=>{var n=r(8940),o=Object.create,i=function(){function t(){}return function(e){if(!n(e))return{};if(o)return o(e);t.prototype=e;var r=new t;return t.prototype=void 0,r}}();t.exports=i},3651:(t,e,r)=>{var n=r(316),o=r(9236)(n);t.exports=o},6608:(t,e,r)=>{var n=r(3651);t.exports=function(t,e){var r=!0;return n(t,(function(t,n,o){return r=!!e(t,n,o)})),r}},9208:(t,e,r)=>{var n=r(7712);t.exports=function(t,e,r){for(var o=-1,i=t.length;++o<i;){var a=t[o],c=e(a);if(null!=c&&(void 0===u?c==c&&!n(c):r(c,u)))var u=c,l=a}return l}},4832:t=>{t.exports=function(t,e,r,n){for(var o=t.length,i=r+(n?1:-1);n?i--:++i<o;)if(e(t[i],i,t))return i;return-1}},8108:(t,e,r)=>{var n=r(1168),o=r(6552);t.exports=function t(e,r,i,a,c){var u=-1,l=e.length;for(i||(i=o),c||(c=[]);++u<l;){var s=e[u];r>0&&i(s)?r>1?t(s,r-1,i,a,c):n(c,s):a||(c[c.length]=s)}return c}},4596:(t,e,r)=>{var n=r(8168)();t.exports=n},316:(t,e,r)=>{var n=r(4596),o=r(5160);t.exports=function(t,e){return t&&n(t,e,o)}},4240:(t,e,r)=>{var n=r(7736),o=r(7668);t.exports=function(t,e){for(var r=0,i=(e=n(e,t)).length;null!=t&&r<i;)t=t[o(e[r++])];return r&&r==i?t:void 0}},4668:(t,e,r)=>{var n=r(1168),o=r(2488);t.exports=function(t,e,r){var i=e(t);return o(t)?i:n(i,r(t))}},6944:(t,e,r)=>{var n=r(7128),o=r(5664),i=r(3168),a=n?n.toStringTag:void 0;t.exports=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":a&&a in Object(t)?o(t):i(t)}},1412:t=>{t.exports=function(t,e){return t>e}},7732:t=>{t.exports=function(t,e){return null!=t&&e in Object(t)}},3992:(t,e,r)=>{var n=r(4832),o=r(1624),i=r(4568);t.exports=function(t,e,r){return e==e?i(t,e,r):n(t,o,r)}},3432:(t,e,r)=>{var n=r(6944),o=r(2892);t.exports=function(t){return o(t)&&"[object Arguments]"==n(t)}},9184:(t,e,r)=>{var n=r(4840),o=r(2892);t.exports=function t(e,r,i,a,c){return e===r||(null==e||null==r||!o(e)&&!o(r)?e!=e&&r!=r:n(e,r,i,a,t,c))}},4840:(t,e,r)=>{var n=r(520),o=r(9124),i=r(2352),a=r(8608),c=r(3871),u=r(2488),l=r(7684),s=r(6700),f="[object Arguments]",p="[object Array]",h="[object Object]",y=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,d,v,m){var b=u(t),g=u(e),x=b?p:c(t),w=g?p:c(e),O=(x=x==f?h:x)==h,j=(w=w==f?h:w)==h,S=x==w;if(S&&l(t)){if(!l(e))return!1;b=!0,O=!1}if(S&&!O)return m||(m=new n),b||s(t)?o(t,e,r,d,v,m):i(t,e,x,r,d,v,m);if(!(1&r)){var A=O&&y.call(t,"__wrapped__"),P=j&&y.call(e,"__wrapped__");if(A||P){var E=A?t.value():t,k=P?e.value():e;return m||(m=new n),v(E,k,r,d,m)}}return!!S&&(m||(m=new n),a(t,e,r,d,v,m))}},1360:(t,e,r)=>{var n=r(3871),o=r(2892);t.exports=function(t){return o(t)&&"[object Map]"==n(t)}},4939:(t,e,r)=>{var n=r(520),o=r(9184);t.exports=function(t,e,r,i){var a=r.length,c=a,u=!i;if(null==t)return!c;for(t=Object(t);a--;){var l=r[a];if(u&&l[2]?l[1]!==t[l[0]]:!(l[0]in t))return!1}for(;++a<c;){var s=(l=r[a])[0],f=t[s],p=l[1];if(u&&l[2]){if(void 0===f&&!(s in t))return!1}else{var h=new n;if(i)var y=i(f,p,s,t,e,h);if(!(void 0===y?o(p,f,3,i,h):y))return!1}}return!0}},1624:t=>{t.exports=function(t){return t!=t}},7200:(t,e,r)=>{var n=r(7920),o=r(6084),i=r(8940),a=r(7456),c=/^\[object .+?Constructor\]$/,u=Function.prototype,l=Object.prototype,s=u.toString,f=l.hasOwnProperty,p=RegExp("^"+s.call(f).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");t.exports=function(t){return!(!i(t)||o(t))&&(n(t)?p:c).test(a(t))}},156:(t,e,r)=>{var n=r(3871),o=r(2892);t.exports=function(t){return o(t)&&"[object Set]"==n(t)}},7160:(t,e,r)=>{var n=r(6944),o=r(9024),i=r(2892),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1,t.exports=function(t){return i(t)&&o(t.length)&&!!a[n(t)]}},3968:(t,e,r)=>{var n=r(4493),o=r(8056),i=r(552),a=r(2488),c=r(4860);t.exports=function(t){return"function"==typeof t?t:null==t?i:"object"==typeof t?a(t)?o(t[0],t[1]):n(t):c(t)}},5552:(t,e,r)=>{var n=r(1004),o=r(3320),i=Object.prototype.hasOwnProperty;t.exports=function(t){if(!n(t))return o(t);var e=[];for(var r in Object(t))i.call(t,r)&&"constructor"!=r&&e.push(r);return e}},3632:(t,e,r)=>{var n=r(8940),o=r(1004),i=r(8512),a=Object.prototype.hasOwnProperty;t.exports=function(t){if(!n(t))return i(t);var e=o(t),r=[];for(var c in t)("constructor"!=c||!e&&a.call(t,c))&&r.push(c);return r}},4432:t=>{t.exports=function(t,e){return t<e}},4320:(t,e,r)=>{var n=r(3651),o=r(4900);t.exports=function(t,e){var r=-1,i=o(t)?Array(t.length):[];return n(t,(function(t,n,o){i[++r]=e(t,n,o)})),i}},4493:(t,e,r)=>{var n=r(4939),o=r(3640),i=r(2584);t.exports=function(t){var e=o(t);return 1==e.length&&e[0][2]?i(e[0][0],e[0][1]):function(r){return r===t||n(r,t,e)}}},8056:(t,e,r)=>{var n=r(9184),o=r(9448),i=r(1256),a=r(9640),c=r(3960),u=r(2584),l=r(7668);t.exports=function(t,e){return a(t)&&c(e)?u(l(t),e):function(r){var a=o(r,t);return void 0===a&&a===e?i(r,t):n(e,a,3)}}},6223:(t,e,r)=>{var n=r(2040),o=r(4240),i=r(3968),a=r(4320),c=r(3416),u=r(9165),l=r(2044),s=r(552),f=r(2488);t.exports=function(t,e,r){e=e.length?n(e,(function(t){return f(t)?function(e){return o(e,1===t.length?t[0]:t)}:t})):[s];var p=-1;e=n(e,u(i));var h=a(t,(function(t,r,o){return{criteria:n(e,(function(e){return e(t)})),index:++p,value:t}}));return c(h,(function(t,e){return l(t,e,r)}))}},7112:t=>{t.exports=function(t){return function(e){return null==e?void 0:e[t]}}},4184:(t,e,r)=>{var n=r(4240);t.exports=function(t){return function(e){return n(e,t)}}},848:t=>{var e=Math.ceil,r=Math.max;t.exports=function(t,n,o,i){for(var a=-1,c=r(e((n-t)/(o||1)),0),u=Array(c);c--;)u[i?c:++a]=t,t+=o;return u}},8292:(t,e,r)=>{var n=r(552),o=r(8840),i=r(7360);t.exports=function(t,e){return i(o(t,e,n),t+"")}},3120:(t,e,r)=>{var n=r(6347),o=r(7792),i=r(552),a=o?function(t,e){return o(t,"toString",{configurable:!0,enumerable:!1,value:n(e),writable:!0})}:i;t.exports=a},4732:t=>{t.exports=function(t,e,r){var n=-1,o=t.length;e<0&&(e=-e>o?0:o+e),(r=r>o?o:r)<0&&(r+=o),o=e>r?0:r-e>>>0,e>>>=0;for(var i=Array(o);++n<o;)i[n]=t[n+e];return i}},3748:(t,e,r)=>{var n=r(3651);t.exports=function(t,e){var r;return n(t,(function(t,n,o){return!(r=e(t,n,o))})),!!r}},3416:t=>{t.exports=function(t,e){var r=t.length;for(t.sort(e);r--;)t[r]=t[r].value;return t}},1448:t=>{t.exports=function(t,e){for(var r,n=-1,o=t.length;++n<o;){var i=e(t[n]);void 0!==i&&(r=void 0===r?i:r+i)}return r}},736:t=>{t.exports=function(t,e){for(var r=-1,n=Array(t);++r<t;)n[r]=e(r);return n}},6524:(t,e,r)=>{var n=r(7128),o=r(2040),i=r(2488),a=r(7712),c=n?n.prototype:void 0,u=c?c.toString:void 0;t.exports=function t(e){if("string"==typeof e)return e;if(i(e))return o(e,t)+"";if(a(e))return u?u.call(e):"";var r=e+"";return"0"==r&&1/e==-Infinity?"-0":r}},4428:(t,e,r)=>{var n=r(5608),o=/^\s+/;t.exports=function(t){return t?t.slice(0,n(t)+1).replace(o,""):t}},9165:t=>{t.exports=function(t){return function(e){return t(e)}}},3756:(t,e,r)=>{var n=r(6152),o=r(8688),i=r(5336),a=r(968),c=r(6840),u=r(2060);t.exports=function(t,e,r){var l=-1,s=o,f=t.length,p=!0,h=[],y=h;if(r)p=!1,s=i;else if(f>=200){var d=e?null:c(t);if(d)return u(d);p=!1,s=a,y=new n}else y=e?[]:h;t:for(;++l<f;){var v=t[l],m=e?e(v):v;if(v=r||0!==v?v:0,p&&m==m){for(var b=y.length;b--;)if(y[b]===m)continue t;e&&y.push(m),h.push(v)}else s(y,m,r)||(y!==h&&y.push(m),h.push(v))}return h}},5840:(t,e,r)=>{var n=r(7736),o=r(2008),i=r(5088),a=r(7668);t.exports=function(t,e){return e=n(e,t),null==(t=i(t,e))||delete t[a(o(e))]}},968:t=>{t.exports=function(t,e){return t.has(e)}},7736:(t,e,r)=>{var n=r(2488),o=r(9640),i=r(976),a=r(1972);t.exports=function(t,e){return n(t)?t:o(t,e)?[t]:i(a(t))}},7320:(t,e,r)=>{var n=r(4732);t.exports=function(t,e,r){var o=t.length;return r=void 0===r?o:r,!e&&r>=o?t:n(t,e,r)}},5987:(t,e,r)=>{var n=r(9704);t.exports=function(t){var e=new t.constructor(t.byteLength);return new n(e).set(new n(t)),e}},1328:(t,e,r)=>{t=r.nmd(t);var n=r(7188),o=e&&!e.nodeType&&e,i=o&&t&&!t.nodeType&&t,a=i&&i.exports===o?n.Buffer:void 0,c=a?a.allocUnsafe:void 0;t.exports=function(t,e){if(e)return t.slice();var r=t.length,n=c?c(r):new t.constructor(r);return t.copy(n),n}},9488:(t,e,r)=>{var n=r(5987);t.exports=function(t,e){var r=e?n(t.buffer):t.buffer;return new t.constructor(r,t.byteOffset,t.byteLength)}},2640:t=>{var e=/\w*$/;t.exports=function(t){var r=new t.constructor(t.source,e.exec(t));return r.lastIndex=t.lastIndex,r}},2656:(t,e,r)=>{var n=r(7128),o=n?n.prototype:void 0,i=o?o.valueOf:void 0;t.exports=function(t){return i?Object(i.call(t)):{}}},2100:(t,e,r)=>{var n=r(5987);t.exports=function(t,e){var r=e?n(t.buffer):t.buffer;return new t.constructor(r,t.byteOffset,t.length)}},3228:(t,e,r)=>{var n=r(7712);t.exports=function(t,e){if(t!==e){var r=void 0!==t,o=null===t,i=t==t,a=n(t),c=void 0!==e,u=null===e,l=e==e,s=n(e);if(!u&&!s&&!a&&t>e||a&&c&&l&&!u&&!s||o&&c&&l||!r&&l||!i)return 1;if(!o&&!a&&!s&&t<e||s&&r&&i&&!o&&!a||u&&r&&i||!c&&i||!l)return-1}return 0}},2044:(t,e,r)=>{var n=r(3228);t.exports=function(t,e,r){for(var o=-1,i=t.criteria,a=e.criteria,c=i.length,u=r.length;++o<c;){var l=n(i[o],a[o]);if(l)return o>=u?l:l*("desc"==r[o]?-1:1)}return t.index-e.index}},8416:t=>{t.exports=function(t,e){var r=-1,n=t.length;for(e||(e=Array(n));++r<n;)e[r]=t[r];return e}},7612:(t,e,r)=>{var n=r(8288),o=r(6139);t.exports=function(t,e,r,i){var a=!r;r||(r={});for(var c=-1,u=e.length;++c<u;){var l=e[c],s=i?i(r[l],t[l],l,r,t):void 0;void 0===s&&(s=t[l]),a?o(r,l,s):n(r,l,s)}return r}},8219:(t,e,r)=>{var n=r(7612),o=r(3520);t.exports=function(t,e){return n(t,o(t),e)}},792:(t,e,r)=>{var n=r(7612),o=r(1216);t.exports=function(t,e){return n(t,o(t),e)}},5280:(t,e,r)=>{var n=r(7188)["__core-js_shared__"];t.exports=n},9236:(t,e,r)=>{var n=r(4900);t.exports=function(t,e){return function(r,o){if(null==r)return r;if(!n(r))return t(r,o);for(var i=r.length,a=e?i:-1,c=Object(r);(e?a--:++a<i)&&!1!==o(c[a],a,c););return r}}},8168:t=>{t.exports=function(t){return function(e,r,n){for(var o=-1,i=Object(e),a=n(e),c=a.length;c--;){var u=a[t?c:++o];if(!1===r(i[u],u,i))break}return e}}},7288:(t,e,r)=>{var n=r(7320),o=r(9240),i=r(8595),a=r(1972);t.exports=function(t){return function(e){e=a(e);var r=o(e)?i(e):void 0,c=r?r[0]:e.charAt(0),u=r?n(r,1).join(""):e.slice(1);return c[t]()+u}}},100:(t,e,r)=>{var n=r(3968),o=r(4900),i=r(5160);t.exports=function(t){return function(e,r,a){var c=Object(e);if(!o(e)){var u=n(r,3);e=i(e),r=function(t){return u(c[t],t,c)}}var l=t(e,r,a);return l>-1?c[u?e[l]:l]:void 0}}},7836:(t,e,r)=>{var n=r(848),o=r(4221),i=r(7556);t.exports=function(t){return function(e,r,a){return a&&"number"!=typeof a&&o(e,r,a)&&(r=a=void 0),e=i(e),void 0===r?(r=e,e=0):r=i(r),a=void 0===a?e<r?1:-1:i(a),n(e,r,a,t)}}},6840:(t,e,r)=>{var n=r(6920),o=r(2648),i=r(2060),a=n&&1/i(new n([,-0]))[1]==1/0?function(t){return new n(t)}:o;t.exports=a},3104:(t,e,r)=>{var n=r(308);t.exports=function(t){return n(t)?void 0:t}},7792:(t,e,r)=>{var n=r(7892),o=function(){try{var t=n(Object,"defineProperty");return t({},"",{}),t}catch(t){}}();t.exports=o},9124:(t,e,r)=>{var n=r(6152),o=r(5600),i=r(968);t.exports=function(t,e,r,a,c,u){var l=1&r,s=t.length,f=e.length;if(s!=f&&!(l&&f>s))return!1;var p=u.get(t),h=u.get(e);if(p&&h)return p==e&&h==t;var y=-1,d=!0,v=2&r?new n:void 0;for(u.set(t,e),u.set(e,t);++y<s;){var m=t[y],b=e[y];if(a)var g=l?a(b,m,y,e,t,u):a(m,b,y,t,e,u);if(void 0!==g){if(g)continue;d=!1;break}if(v){if(!o(e,(function(t,e){if(!i(v,e)&&(m===t||c(m,t,r,a,u)))return v.push(e)}))){d=!1;break}}else if(m!==b&&!c(m,b,r,a,u)){d=!1;break}}return u.delete(t),u.delete(e),d}},2352:(t,e,r)=>{var n=r(7128),o=r(9704),i=r(864),a=r(9124),c=r(3152),u=r(2060),l=n?n.prototype:void 0,s=l?l.valueOf:void 0;t.exports=function(t,e,r,n,l,f,p){switch(r){case"[object DataView]":if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)return!1;t=t.buffer,e=e.buffer;case"[object ArrayBuffer]":return!(t.byteLength!=e.byteLength||!f(new o(t),new o(e)));case"[object Boolean]":case"[object Date]":case"[object Number]":return i(+t,+e);case"[object Error]":return t.name==e.name&&t.message==e.message;case"[object RegExp]":case"[object String]":return t==e+"";case"[object Map]":var h=c;case"[object Set]":var y=1&n;if(h||(h=u),t.size!=e.size&&!y)return!1;var d=p.get(t);if(d)return d==e;n|=2,p.set(t,e);var v=a(h(t),h(e),n,l,f,p);return p.delete(t),v;case"[object Symbol]":if(s)return s.call(t)==s.call(e)}return!1}},8608:(t,e,r)=>{var n=r(1096),o=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,i,a,c){var u=1&r,l=n(t),s=l.length;if(s!=n(e).length&&!u)return!1;for(var f=s;f--;){var p=l[f];if(!(u?p in e:o.call(e,p)))return!1}var h=c.get(t),y=c.get(e);if(h&&y)return h==e&&y==t;var d=!0;c.set(t,e),c.set(e,t);for(var v=u;++f<s;){var m=t[p=l[f]],b=e[p];if(i)var g=u?i(b,m,p,e,t,c):i(m,b,p,t,e,c);if(!(void 0===g?m===b||a(m,b,r,i,c):g)){d=!1;break}v||(v="constructor"==p)}if(d&&!v){var x=t.constructor,w=e.constructor;x==w||!("constructor"in t)||!("constructor"in e)||"function"==typeof x&&x instanceof x&&"function"==typeof w&&w instanceof w||(d=!1)}return c.delete(t),c.delete(e),d}},7452:(t,e,r)=>{var n=r(4576),o=r(8840),i=r(7360);t.exports=function(t){return i(o(t,void 0,n),t+"")}},4848:(t,e,r)=>{var n="object"==typeof r.g&&r.g&&r.g.Object===Object&&r.g;t.exports=n},1096:(t,e,r)=>{var n=r(4668),o=r(3520),i=r(5160);t.exports=function(t){return n(t,i,o)}},8856:(t,e,r)=>{var n=r(4668),o=r(1216),i=r(2756);t.exports=function(t){return n(t,i,o)}},6068:(t,e,r)=>{var n=r(6096);t.exports=function(t,e){var r=t.__data__;return n(e)?r["string"==typeof e?"string":"hash"]:r.map}},3640:(t,e,r)=>{var n=r(3960),o=r(5160);t.exports=function(t){for(var e=o(t),r=e.length;r--;){var i=e[r],a=t[i];e[r]=[i,a,n(a)]}return e}},7892:(t,e,r)=>{var n=r(7200),o=r(5692);t.exports=function(t,e){var r=o(t,e);return n(r)?r:void 0}},476:(t,e,r)=>{var n=r(1304)(Object.getPrototypeOf,Object);t.exports=n},5664:(t,e,r)=>{var n=r(7128),o=Object.prototype,i=o.hasOwnProperty,a=o.toString,c=n?n.toStringTag:void 0;t.exports=function(t){var e=i.call(t,c),r=t[c];try{t[c]=void 0;var n=!0}catch(t){}var o=a.call(t);return n&&(e?t[c]=r:delete t[c]),o}},3520:(t,e,r)=>{var n=r(8640),o=r(872),i=Object.prototype.propertyIsEnumerable,a=Object.getOwnPropertySymbols,c=a?function(t){return null==t?[]:(t=Object(t),n(a(t),(function(e){return i.call(t,e)})))}:o;t.exports=c},1216:(t,e,r)=>{var n=r(1168),o=r(476),i=r(3520),a=r(872),c=Object.getOwnPropertySymbols?function(t){for(var e=[];t;)n(e,i(t)),t=o(t);return e}:a;t.exports=c},3871:(t,e,r)=>{var n=r(6032),o=r(420),i=r(404),a=r(6920),c=r(5200),u=r(6944),l=r(7456),s="[object Map]",f="[object Promise]",p="[object Set]",h="[object WeakMap]",y="[object DataView]",d=l(n),v=l(o),m=l(i),b=l(a),g=l(c),x=u;(n&&x(new n(new ArrayBuffer(1)))!=y||o&&x(new o)!=s||i&&x(i.resolve())!=f||a&&x(new a)!=p||c&&x(new c)!=h)&&(x=function(t){var e=u(t),r="[object Object]"==e?t.constructor:void 0,n=r?l(r):"";if(n)switch(n){case d:return y;case v:return s;case m:return f;case b:return p;case g:return h}return e}),t.exports=x},5692:t=>{t.exports=function(t,e){return null==t?void 0:t[e]}},2828:(t,e,r)=>{var n=r(7736),o=r(348),i=r(2488),a=r(1188),c=r(9024),u=r(7668);t.exports=function(t,e,r){for(var l=-1,s=(e=n(e,t)).length,f=!1;++l<s;){var p=u(e[l]);if(!(f=null!=t&&r(t,p)))break;t=t[p]}return f||++l!=s?f:!!(s=null==t?0:t.length)&&c(s)&&a(p,s)&&(i(t)||o(t))}},9240:t=>{var e=RegExp("[\\u200d\\ud800-\\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");t.exports=function(t){return e.test(t)}},4212:(t,e,r)=>{var n=r(5604);t.exports=function(){this.__data__=n?n(null):{},this.size=0}},2688:t=>{t.exports=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e}},3916:(t,e,r)=>{var n=r(5604),o=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;if(n){var r=e[t];return"__lodash_hash_undefined__"===r?void 0:r}return o.call(e,t)?e[t]:void 0}},6952:(t,e,r)=>{var n=r(5604),o=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;return n?void 0!==e[t]:o.call(e,t)}},1016:(t,e,r)=>{var n=r(5604);t.exports=function(t,e){var r=this.__data__;return this.size+=this.has(t)?0:1,r[t]=n&&void 0===e?"__lodash_hash_undefined__":e,this}},4636:t=>{var e=Object.prototype.hasOwnProperty;t.exports=function(t){var r=t.length,n=new t.constructor(r);return r&&"string"==typeof t[0]&&e.call(t,"index")&&(n.index=t.index,n.input=t.input),n}},7576:(t,e,r)=>{var n=r(5987),o=r(9488),i=r(2640),a=r(2656),c=r(2100);t.exports=function(t,e,r){var u=t.constructor;switch(e){case"[object ArrayBuffer]":return n(t);case"[object Boolean]":case"[object Date]":return new u(+t);case"[object DataView]":return o(t,r);case"[object Float32Array]":case"[object Float64Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":return c(t,r);case"[object Map]":case"[object Set]":return new u;case"[object Number]":case"[object String]":return new u(t);case"[object RegExp]":return i(t);case"[object Symbol]":return a(t)}}},6224:(t,e,r)=>{var n=r(2471),o=r(476),i=r(1004);t.exports=function(t){return"function"!=typeof t.constructor||i(t)?{}:n(o(t))}},6552:(t,e,r)=>{var n=r(7128),o=r(348),i=r(2488),a=n?n.isConcatSpreadable:void 0;t.exports=function(t){return i(t)||o(t)||!!(a&&t&&t[a])}},1188:t=>{var e=/^(?:0|[1-9]\d*)$/;t.exports=function(t,r){var n=typeof t;return!!(r=null==r?9007199254740991:r)&&("number"==n||"symbol"!=n&&e.test(t))&&t>-1&&t%1==0&&t<r}},4221:(t,e,r)=>{var n=r(864),o=r(4900),i=r(1188),a=r(8940);t.exports=function(t,e,r){if(!a(r))return!1;var c=typeof e;return!!("number"==c?o(r)&&i(e,r.length):"string"==c&&e in r)&&n(r[e],t)}},9640:(t,e,r)=>{var n=r(2488),o=r(7712),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.exports=function(t,e){if(n(t))return!1;var r=typeof t;return!("number"!=r&&"symbol"!=r&&"boolean"!=r&&null!=t&&!o(t))||(a.test(t)||!i.test(t)||null!=e&&t in Object(e))}},6096:t=>{t.exports=function(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t}},6084:(t,e,r)=>{var n,o=r(5280),i=(n=/[^.]+$/.exec(o&&o.keys&&o.keys.IE_PROTO||""))?"Symbol(src)_1."+n:"";t.exports=function(t){return!!i&&i in t}},1004:t=>{var e=Object.prototype;t.exports=function(t){var r=t&&t.constructor;return t===("function"==typeof r&&r.prototype||e)}},3960:(t,e,r)=>{var n=r(8940);t.exports=function(t){return t==t&&!n(t)}},5968:t=>{t.exports=function(){this.__data__=[],this.size=0}},3740:(t,e,r)=>{var n=r(6600),o=Array.prototype.splice;t.exports=function(t){var e=this.__data__,r=n(e,t);return!(r<0)&&(r==e.length-1?e.pop():o.call(e,r,1),--this.size,!0)}},4996:(t,e,r)=>{var n=r(6600);t.exports=function(t){var e=this.__data__,r=n(e,t);return r<0?void 0:e[r][1]}},2600:(t,e,r)=>{var n=r(6600);t.exports=function(t){return n(this.__data__,t)>-1}},7336:(t,e,r)=>{var n=r(6600);t.exports=function(t,e){var r=this.__data__,o=n(r,t);return o<0?(++this.size,r.push([t,e])):r[o][1]=e,this}},8720:(t,e,r)=>{var n=r(1276),o=r(3040),i=r(420);t.exports=function(){this.size=0,this.__data__={hash:new n,map:new(i||o),string:new n}}},4760:(t,e,r)=>{var n=r(6068);t.exports=function(t){var e=n(this,t).delete(t);return this.size-=e?1:0,e}},88:(t,e,r)=>{var n=r(6068);t.exports=function(t){return n(this,t).get(t)}},9776:(t,e,r)=>{var n=r(6068);t.exports=function(t){return n(this,t).has(t)}},8619:(t,e,r)=>{var n=r(6068);t.exports=function(t,e){var r=n(this,t),o=r.size;return r.set(t,e),this.size+=r.size==o?0:1,this}},3152:t=>{t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach((function(t,n){r[++e]=[n,t]})),r}},2584:t=>{t.exports=function(t,e){return function(r){return null!=r&&(r[t]===e&&(void 0!==e||t in Object(r)))}}},9032:(t,e,r)=>{var n=r(1576);t.exports=function(t){var e=n(t,(function(t){return 500===r.size&&r.clear(),t})),r=e.cache;return e}},5604:(t,e,r)=>{var n=r(7892)(Object,"create");t.exports=n},3320:(t,e,r)=>{var n=r(1304)(Object.keys,Object);t.exports=n},8512:t=>{t.exports=function(t){var e=[];if(null!=t)for(var r in Object(t))e.push(r);return e}},9180:(t,e,r)=>{t=r.nmd(t);var n=r(4848),o=e&&!e.nodeType&&e,i=o&&t&&!t.nodeType&&t,a=i&&i.exports===o&&n.process,c=function(){try{var t=i&&i.require&&i.require("util").types;return t||a&&a.binding&&a.binding("util")}catch(t){}}();t.exports=c},3168:t=>{var e=Object.prototype.toString;t.exports=function(t){return e.call(t)}},1304:t=>{t.exports=function(t,e){return function(r){return t(e(r))}}},8840:(t,e,r)=>{var n=r(2253),o=Math.max;t.exports=function(t,e,r){return e=o(void 0===e?t.length-1:e,0),function(){for(var i=arguments,a=-1,c=o(i.length-e,0),u=Array(c);++a<c;)u[a]=i[e+a];a=-1;for(var l=Array(e+1);++a<e;)l[a]=i[a];return l[e]=r(u),n(t,this,l)}}},5088:(t,e,r)=>{var n=r(4240),o=r(4732);t.exports=function(t,e){return e.length<2?t:n(t,o(e,0,-1))}},7188:(t,e,r)=>{var n=r(4848),o="object"==typeof self&&self&&self.Object===Object&&self,i=n||o||Function("return this")();t.exports=i},9516:t=>{t.exports=function(t){return this.__data__.set(t,"__lodash_hash_undefined__"),this}},3504:t=>{t.exports=function(t){return this.__data__.has(t)}},2060:t=>{t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach((function(t){r[++e]=t})),r}},7360:(t,e,r)=>{var n=r(3120),o=r(4208)(n);t.exports=o},4208:t=>{var e=Date.now;t.exports=function(t){var r=0,n=0;return function(){var o=e(),i=16-(o-n);if(n=o,i>0){if(++r>=800)return arguments[0]}else r=0;return t.apply(void 0,arguments)}}},5643:(t,e,r)=>{var n=r(3040);t.exports=function(){this.__data__=new n,this.size=0}},3368:t=>{t.exports=function(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r}},636:t=>{t.exports=function(t){return this.__data__.get(t)}},3012:t=>{t.exports=function(t){return this.__data__.has(t)}},3388:(t,e,r)=>{var n=r(3040),o=r(420),i=r(1476);t.exports=function(t,e){var r=this.__data__;if(r instanceof n){var a=r.__data__;if(!o||a.length<199)return a.push([t,e]),this.size=++r.size,this;r=this.__data__=new i(a)}return r.set(t,e),this.size=r.size,this}},4568:t=>{t.exports=function(t,e,r){for(var n=r-1,o=t.length;++n<o;)if(t[n]===e)return n;return-1}},8595:(t,e,r)=>{var n=r(1120),o=r(9240),i=r(6448);t.exports=function(t){return o(t)?i(t):n(t)}},976:(t,e,r)=>{var n=r(9032),o=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,i=/\\(\\)?/g,a=n((function(t){var e=[];return 46===t.charCodeAt(0)&&e.push(""),t.replace(o,(function(t,r,n,o){e.push(n?o.replace(i,"$1"):r||t)})),e}));t.exports=a},7668:(t,e,r)=>{var n=r(7712);t.exports=function(t){if("string"==typeof t||n(t))return t;var e=t+"";return"0"==e&&1/t==-Infinity?"-0":e}},7456:t=>{var e=Function.prototype.toString;t.exports=function(t){if(null!=t){try{return e.call(t)}catch(t){}try{return t+""}catch(t){}}return""}},5608:t=>{var e=/\s/;t.exports=function(t){for(var r=t.length;r--&&e.test(t.charAt(r)););return r}},6448:t=>{var e="\\ud800-\\udfff",r="["+e+"]",n="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",o="\\ud83c[\\udffb-\\udfff]",i="[^"+e+"]",a="(?:\\ud83c[\\udde6-\\uddff]){2}",c="[\\ud800-\\udbff][\\udc00-\\udfff]",u="(?:"+n+"|"+o+")"+"?",l="[\\ufe0e\\ufe0f]?",s=l+u+("(?:\\u200d(?:"+[i,a,c].join("|")+")"+l+u+")*"),f="(?:"+[i+n+"?",n,a,c,r].join("|")+")",p=RegExp(o+"(?="+o+")|"+f+s,"g");t.exports=function(t){return t.match(p)||[]}},6347:t=>{t.exports=function(t){return function(){return t}}},3336:(t,e,r)=>{var n=r(8940),o=r(9868),i=r(8472),a=Math.max,c=Math.min;t.exports=function(t,e,r){var u,l,s,f,p,h,y=0,d=!1,v=!1,m=!0;if("function"!=typeof t)throw new TypeError("Expected a function");function b(e){var r=u,n=l;return u=l=void 0,y=e,f=t.apply(n,r)}function g(t){var r=t-h;return void 0===h||r>=e||r<0||v&&t-y>=s}function x(){var t=o();if(g(t))return w(t);p=setTimeout(x,function(t){var r=e-(t-h);return v?c(r,s-(t-y)):r}(t))}function w(t){return p=void 0,m&&u?b(t):(u=l=void 0,f)}function O(){var t=o(),r=g(t);if(u=arguments,l=this,h=t,r){if(void 0===p)return function(t){return y=t,p=setTimeout(x,e),d?b(t):f}(h);if(v)return clearTimeout(p),p=setTimeout(x,e),b(h)}return void 0===p&&(p=setTimeout(x,e)),f}return e=i(e)||0,n(r)&&(d=!!r.leading,s=(v="maxWait"in r)?a(i(r.maxWait)||0,e):s,m="trailing"in r?!!r.trailing:m),O.cancel=function(){void 0!==p&&clearTimeout(p),y=0,u=h=l=p=void 0},O.flush=function(){return void 0===p?f:w(o())},O}},864:t=>{t.exports=function(t,e){return t===e||t!=t&&e!=e}},164:(t,e,r)=>{var n=r(3908),o=r(6608),i=r(3968),a=r(2488),c=r(4221);t.exports=function(t,e,r){var u=a(t)?n:o;return r&&c(t,e,r)&&(e=void 0),u(t,i(e,3))}},7120:(t,e,r)=>{var n=r(100)(r(2988));t.exports=n},2988:(t,e,r)=>{var n=r(4832),o=r(3968),i=r(4400),a=Math.max;t.exports=function(t,e,r){var c=null==t?0:t.length;if(!c)return-1;var u=null==r?0:i(r);return u<0&&(u=a(c+u,0)),n(t,o(e,3),u)}},9540:(t,e,r)=>{t.exports=r(7580)},7440:(t,e,r)=>{var n=r(8108),o=r(7096);t.exports=function(t,e){return n(o(t,e),1)}},4576:(t,e,r)=>{var n=r(8108);t.exports=function(t){return(null==t?0:t.length)?n(t,1):[]}},9448:(t,e,r)=>{var n=r(4240);t.exports=function(t,e,r){var o=null==t?void 0:n(t,e);return void 0===o?r:o}},1256:(t,e,r)=>{var n=r(7732),o=r(2828);t.exports=function(t,e){return null!=t&&o(t,e,n)}},7580:t=>{t.exports=function(t){return t&&t.length?t[0]:void 0}},552:t=>{t.exports=function(t){return t}},348:(t,e,r)=>{var n=r(3432),o=r(2892),i=Object.prototype,a=i.hasOwnProperty,c=i.propertyIsEnumerable,u=n(function(){return arguments}())?n:function(t){return o(t)&&a.call(t,"callee")&&!c.call(t,"callee")};t.exports=u},2488:t=>{var e=Array.isArray;t.exports=e},4900:(t,e,r)=>{var n=r(7920),o=r(9024);t.exports=function(t){return null!=t&&o(t.length)&&!n(t)}},7e3:(t,e,r)=>{var n=r(6944),o=r(2892);t.exports=function(t){return!0===t||!1===t||o(t)&&"[object Boolean]"==n(t)}},7684:(t,e,r)=>{t=r.nmd(t);var n=r(7188),o=r(4068),i=e&&!e.nodeType&&e,a=i&&t&&!t.nodeType&&t,c=a&&a.exports===i?n.Buffer:void 0,u=(c?c.isBuffer:void 0)||o;t.exports=u},948:(t,e,r)=>{var n=r(9184);t.exports=function(t,e){return n(t,e)}},7920:(t,e,r)=>{var n=r(6944),o=r(8940);t.exports=function(t){if(!o(t))return!1;var e=n(t);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e}},9024:t=>{t.exports=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=9007199254740991}},8544:(t,e,r)=>{var n=r(1360),o=r(9165),i=r(9180),a=i&&i.isMap,c=a?o(a):n;t.exports=c},8880:(t,e,r)=>{var n=r(568);t.exports=function(t){return n(t)&&t!=+t}},4764:t=>{t.exports=function(t){return null==t}},568:(t,e,r)=>{var n=r(6944),o=r(2892);t.exports=function(t){return"number"==typeof t||o(t)&&"[object Number]"==n(t)}},8940:t=>{t.exports=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}},2892:t=>{t.exports=function(t){return null!=t&&"object"==typeof t}},308:(t,e,r)=>{var n=r(6944),o=r(476),i=r(2892),a=Function.prototype,c=Object.prototype,u=a.toString,l=c.hasOwnProperty,s=u.call(Object);t.exports=function(t){if(!i(t)||"[object Object]"!=n(t))return!1;var e=o(t);if(null===e)return!0;var r=l.call(e,"constructor")&&e.constructor;return"function"==typeof r&&r instanceof r&&u.call(r)==s}},9504:(t,e,r)=>{var n=r(156),o=r(9165),i=r(9180),a=i&&i.isSet,c=a?o(a):n;t.exports=c},6384:(t,e,r)=>{var n=r(6944),o=r(2488),i=r(2892);t.exports=function(t){return"string"==typeof t||!o(t)&&i(t)&&"[object String]"==n(t)}},7712:(t,e,r)=>{var n=r(6944),o=r(2892);t.exports=function(t){return"symbol"==typeof t||o(t)&&"[object Symbol]"==n(t)}},6700:(t,e,r)=>{var n=r(7160),o=r(9165),i=r(9180),a=i&&i.isTypedArray,c=a?o(a):n;t.exports=c},5160:(t,e,r)=>{var n=r(7640),o=r(5552),i=r(4900);t.exports=function(t){return i(t)?n(t):o(t)}},2756:(t,e,r)=>{var n=r(7640),o=r(3632),i=r(4900);t.exports=function(t){return i(t)?n(t,!0):o(t)}},2008:t=>{t.exports=function(t){var e=null==t?0:t.length;return e?t[e-1]:void 0}},7096:(t,e,r)=>{var n=r(2040),o=r(3968),i=r(4320),a=r(2488);t.exports=function(t,e){return(a(t)?n:i)(t,o(e,3))}},6452:(t,e,r)=>{var n=r(6139),o=r(316),i=r(3968);t.exports=function(t,e){var r={};return e=i(e,3),o(t,(function(t,o,i){n(r,o,e(t,o,i))})),r}},4464:(t,e,r)=>{var n=r(9208),o=r(1412),i=r(552);t.exports=function(t){return t&&t.length?n(t,i,o):void 0}},6564:(t,e,r)=>{var n=r(9208),o=r(1412),i=r(3968);t.exports=function(t,e){return t&&t.length?n(t,i(e,2),o):void 0}},1576:(t,e,r)=>{var n=r(1476);function o(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw new TypeError("Expected a function");var r=function(){var n=arguments,o=e?e.apply(this,n):n[0],i=r.cache;if(i.has(o))return i.get(o);var a=t.apply(this,n);return r.cache=i.set(o,a)||i,a};return r.cache=new(o.Cache||n),r}o.Cache=n,t.exports=o},4044:(t,e,r)=>{var n=r(9208),o=r(4432),i=r(552);t.exports=function(t){return t&&t.length?n(t,i,o):void 0}},908:(t,e,r)=>{var n=r(9208),o=r(3968),i=r(4432);t.exports=function(t,e){return t&&t.length?n(t,o(e,2),i):void 0}},2648:t=>{t.exports=function(){}},9868:(t,e,r)=>{var n=r(7188);t.exports=function(){return n.Date.now()}},1860:(t,e,r)=>{var n=r(2040),o=r(5620),i=r(5840),a=r(7736),c=r(7612),u=r(3104),l=r(7452),s=r(8856),f=l((function(t,e){var r={};if(null==t)return r;var l=!1;e=n(e,(function(e){return e=a(e,t),l||(l=e.length>1),e})),c(t,s(t),r),l&&(r=o(r,7,u));for(var f=e.length;f--;)i(r,e[f]);return r}));t.exports=f},4860:(t,e,r)=>{var n=r(7112),o=r(4184),i=r(9640),a=r(7668);t.exports=function(t){return i(t)?n(a(t)):o(t)}},9136:(t,e,r)=>{var n=r(7836)();t.exports=n},9500:(t,e,r)=>{var n=r(5600),o=r(3968),i=r(3748),a=r(2488),c=r(4221);t.exports=function(t,e,r){var u=a(t)?n:i;return r&&c(t,e,r)&&(e=void 0),u(t,o(e,3))}},1232:(t,e,r)=>{var n=r(8108),o=r(6223),i=r(8292),a=r(4221),c=i((function(t,e){if(null==t)return[];var r=e.length;return r>1&&a(t,e[0],e[1])?e=[]:r>2&&a(e[0],e[1],e[2])&&(e=[e[0]]),o(t,n(e,1),[])}));t.exports=c},872:t=>{t.exports=function(){return[]}},4068:t=>{t.exports=function(){return!1}},6356:(t,e,r)=>{var n=r(3968),o=r(1448);t.exports=function(t,e){return t&&t.length?o(t,n(e,2)):0}},9212:(t,e,r)=>{var n=r(3336),o=r(8940);t.exports=function(t,e,r){var i=!0,a=!0;if("function"!=typeof t)throw new TypeError("Expected a function");return o(r)&&(i="leading"in r?!!r.leading:i,a="trailing"in r?!!r.trailing:a),n(t,e,{leading:i,maxWait:e,trailing:a})}},7556:(t,e,r)=>{var n=r(8472),o=1/0;t.exports=function(t){return t?(t=n(t))===o||t===-1/0?17976931348623157e292*(t<0?-1:1):t==t?t:0:0===t?t:0}},4400:(t,e,r)=>{var n=r(7556);t.exports=function(t){var e=n(t),r=e%1;return e==e?r?e-r:e:0}},8472:(t,e,r)=>{var n=r(4428),o=r(8940),i=r(7712),a=/^[-+]0x[0-9a-f]+$/i,c=/^0b[01]+$/i,u=/^0o[0-7]+$/i,l=parseInt;t.exports=function(t){if("number"==typeof t)return t;if(i(t))return NaN;if(o(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=o(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=n(t);var r=c.test(t);return r||u.test(t)?l(t.slice(2),r?2:8):a.test(t)?NaN:+t}},1972:(t,e,r)=>{var n=r(6524);t.exports=function(t){return null==t?"":n(t)}},6424:(t,e,r)=>{var n=r(3968),o=r(3756);t.exports=function(t,e){return t&&t.length?o(t,n(e,2)):[]}},4704:(t,e,r)=>{var n=r(7288)("toUpperCase");t.exports=n},9764:(t,e)=>{"use strict";var r,n=Symbol.for("react.element"),o=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),u=Symbol.for("react.provider"),l=Symbol.for("react.context"),s=Symbol.for("react.server_context"),f=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),h=Symbol.for("react.suspense_list"),y=Symbol.for("react.memo"),d=Symbol.for("react.lazy"),v=Symbol.for("react.offscreen");function m(t){if("object"==typeof t&&null!==t){var e=t.$$typeof;switch(e){case n:switch(t=t.type){case i:case c:case a:case p:case h:return t;default:switch(t=t&&t.$$typeof){case s:case l:case f:case d:case y:case u:return t;default:return e}}case o:return e}}}r=Symbol.for("react.module.reference"),e.isFragment=function(t){return m(t)===i}},2168:(t,e,r)=>{"use strict";t.exports=r(9764)},9660:t=>{"use strict";t.exports=e},741:e=>{"use strict";e.exports=t}},n={};function o(t){var e=n[t];if(void 0!==e)return e.exports;var i=n[t]={id:t,loaded:!1,exports:{}};return r[t].call(i.exports,i,i.exports,o),i.loaded=!0,i.exports}o.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return o.d(e,{a:e}),e},o.d=(t,e)=>{for(var r in e)o.o(e,r)&&!o.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},o.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),o.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),o.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},o.nmd=t=>(t.paths=[],t.children||(t.children=[]),t);var i={};return(()=>{"use strict";o.r(i),o.d(i,{Area:()=>ix,AreaChart:()=>QO,Bar:()=>Sm,BarChart:()=>Zw,Brush:()=>Vv,CartesianAxis:()=>ug,CartesianGrid:()=>Pg,Cell:()=>Tr,ComposedChart:()=>ej,Cross:()=>iy,Curve:()=>Yf,Customized:()=>cf,DefaultLegendContent:()=>de,DefaultTooltipContent:()=>$e,Dot:()=>Zh,ErrorBar:()=>Pl,Funnel:()=>_j,FunnelChart:()=>Cj,Global:()=>cr,Label:()=>Us,LabelList:()=>rf,Layer:()=>yt,Legend:()=>Ce,Line:()=>$g,LineChart:()=>Yw,Pie:()=>Ud,PieChart:()=>Jw,PolarAngleAxis:()=>Zy,PolarGrid:()=>by,PolarRadiusAxis:()=>By,Polygon:()=>Gh,Radar:()=>nv,RadarChart:()=>ZO,RadialBar:()=>Pv,RadialBarChart:()=>tj,Rectangle:()=>Uh,ReferenceArea:()=>zb,ReferenceDot:()=>Eb,ReferenceLine:()=>db,ResponsiveContainer:()=>kr,Sankey:()=>YO,Scatter:()=>Mx,ScatterChart:()=>JO,Sector:()=>vf,SunburstChart:()=>sj,Surface:()=>st,Symbols:()=>ne,Text:()=>hn,Tooltip:()=>gr,Trapezoid:()=>pd,Treemap:()=>wO,XAxis:()=>Ux,YAxis:()=>Jx,ZAxis:()=>yx});var t={};o.r(t),o.d(t,{scaleBand:()=>jn,scaleDiverging:()=>bu,scaleDivergingLog:()=>gu,scaleDivergingPow:()=>wu,scaleDivergingSqrt:()=>Ou,scaleDivergingSymlog:()=>xu,scaleIdentity:()=>pi,scaleImplicit:()=>wn,scaleLinear:()=>fi,scaleLog:()=>wi,scaleOrdinal:()=>On,scalePoint:()=>An,scalePow:()=>Mi,scaleQuantile:()=>Wi,scaleQuantize:()=>$i,scaleRadial:()=>Di,scaleSequential:()=>fu,scaleSequentialLog:()=>pu,scaleSequentialPow:()=>yu,scaleSequentialQuantile:()=>vu,scaleSequentialSqrt:()=>du,scaleSequentialSymlog:()=>hu,scaleSqrt:()=>_i,scaleSymlog:()=>Ai,scaleThreshold:()=>Ki,scaleTime:()=>cu,scaleUtc:()=>uu,tickFormat:()=>li});var e=o(741),r=o.n(e);function n(t){var e,r,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t)if(Array.isArray(t)){var i=t.length;for(e=0;e<i;e++)t[e]&&(r=n(t[e]))&&(o&&(o+=" "),o+=r)}else for(r in t)t[r]&&(o&&(o+=" "),o+=r);return o}const a=function(){for(var t,e,r=0,o="",i=arguments.length;r<i;r++)(t=arguments[r])&&(e=n(t))&&(o&&(o+=" "),o+=e);return o};var c=o(9448),u=o.n(c),l=o(4764),s=o.n(l),f=o(6384),p=o.n(f),h=o(7920),y=o.n(h),d=o(8940),v=o.n(d),m=o(2168),b=o(8880),g=o.n(b),x=o(568),w=o.n(x),O=function(t){return 0===t?0:t>0?1:-1},j=function(t){return p()(t)&&t.indexOf("%")===t.length-1},S=function(t){return w()(t)&&!g()(t)},A=function(t){return S(t)||p()(t)},P=0,E=function(t){var e=++P;return"".concat(t||"").concat(e)},k=function(t,e){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!S(t)&&!p()(t))return n;if(j(t)){var i=t.indexOf("%");r=e*parseFloat(t.slice(0,i))/100}else r=+t;return g()(r)&&(r=n),o&&r>e&&(r=e),r},T=function(t){if(!t)return null;var e=Object.keys(t);return e&&e.length?t[e[0]]:null},M=function(t,e){return S(t)&&S(e)?function(r){return t+r*(e-t)}:function(){return e}};function _(t,e,r){return t&&t.length?t.find((function(t){return t&&("function"==typeof e?e(t):u()(t,e))===r})):null}var C=function(t,e){return S(t)&&S(e)?t-e:p()(t)&&p()(e)?t.localeCompare(e):t instanceof Date&&e instanceof Date?t.getTime()-e.getTime():String(t).localeCompare(String(e))};function D(t,e){for(var r in t)if({}.hasOwnProperty.call(t,r)&&(!{}.hasOwnProperty.call(e,r)||t[r]!==e[r]))return!1;for(var n in e)if({}.hasOwnProperty.call(e,n)&&!{}.hasOwnProperty.call(t,n))return!1;return!0}function I(t){return I="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},I(t)}var N=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],R=["points","pathLength"],B={svg:["viewBox","children"],polygon:R,polyline:R},L=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],z=function(t,r){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var n=t;if((0,e.isValidElement)(t)&&(n=t.props),!v()(n))return null;var o={};return Object.keys(n).forEach((function(t){L.includes(t)&&(o[t]=r||function(e){return n[t](n,e)})})),o},F=function(t,e,r){if(!v()(t)||"object"!==I(t))return null;var n=null;return Object.keys(t).forEach((function(o){var i=t[o];L.includes(o)&&"function"==typeof i&&(n||(n={}),n[o]=function(t,e,r){return function(n){return t(e,r,n),null}}(i,e,r))})),n},U=["children"],W=["children"];function $(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function K(t){return K="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},K(t)}var q={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},V=function(t){return"string"==typeof t?t:t?t.displayName||t.name||"Component":""},X=null,H=null,G=function t(r){if(r===X&&Array.isArray(H))return H;var n=[];return e.Children.forEach(r,(function(e){s()(e)||((0,m.isFragment)(e)?n=n.concat(t(e.props.children)):n.push(e))})),H=n,X=r,n};function Y(t,e){var r=[],n=[];return n=Array.isArray(e)?e.map((function(t){return V(t)})):[V(e)],G(t).forEach((function(t){var e=u()(t,"type.displayName")||u()(t,"type.name");-1!==n.indexOf(e)&&r.push(t)})),r}function Z(t,e){var r=Y(t,e);return r&&r[0]}var J=function(t){if(!t||!t.props)return!1;var e=t.props,r=e.width,n=e.height;return!(!S(r)||r<=0||!S(n)||n<=0)},Q=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],tt=function(t){return t&&t.type&&p()(t.type)&&Q.indexOf(t.type)>=0},et=function(t){return t&&"object"===K(t)&&"clipDot"in t},rt=function(t){var e=[];return G(t).forEach((function(t){tt(t)&&e.push(t)})),e},nt=function(t,r,n){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var o=t;if((0,e.isValidElement)(t)&&(o=t.props),!v()(o))return null;var i={};return Object.keys(o).forEach((function(t){var e;(function(t,e,r,n){var o,i=null!==(o=null==B?void 0:B[n])&&void 0!==o?o:[];return e.startsWith("data-")||!y()(t)&&(n&&i.includes(e)||N.includes(e))||r&&L.includes(e)})(null===(e=o)||void 0===e?void 0:e[t],t,r,n)&&(i[t]=o[t])})),i},ot=function t(r,n){if(r===n)return!0;var o=e.Children.count(r);if(o!==e.Children.count(n))return!1;if(0===o)return!0;if(1===o)return it(Array.isArray(r)?r[0]:r,Array.isArray(n)?n[0]:n);for(var i=0;i<o;i++){var a=r[i],c=n[i];if(Array.isArray(a)||Array.isArray(c)){if(!t(a,c))return!1}else if(!it(a,c))return!1}return!0},it=function(t,e){if(s()(t)&&s()(e))return!0;if(!s()(t)&&!s()(e)){var r=t.props||{},n=r.children,o=$(r,U),i=e.props||{},a=i.children,c=$(i,W);return n&&a?D(o,c)&&ot(n,a):!n&&!a&&D(o,c)}return!1},at=function(t,e){var r=[],n={};return G(t).forEach((function(t,o){if(tt(t))r.push(t);else if(t){var i=V(t.type),a=e[i]||{},c=a.handler,u=a.once;if(c&&(!u||!n[i])){var l=c(t,i,o);r.push(l),n[i]=!0}}})),r},ct=["children","width","height","viewBox","className","style","title","desc"];function ut(){return ut=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ut.apply(this,arguments)}function lt(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function st(t){var e=t.children,n=t.width,o=t.height,i=t.viewBox,c=t.className,u=t.style,l=t.title,s=t.desc,f=lt(t,ct),p=i||{width:n,height:o,x:0,y:0},h=a("recharts-surface",c);return r().createElement("svg",ut({},nt(f,!0,"svg"),{className:h,width:n,height:o,style:u,viewBox:"".concat(p.x," ").concat(p.y," ").concat(p.width," ").concat(p.height)}),r().createElement("title",null,l),r().createElement("desc",null,s),e)}var ft=["children","className"];function pt(){return pt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},pt.apply(this,arguments)}function ht(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var yt=r().forwardRef((function(t,e){var n=t.children,o=t.className,i=ht(t,ft),c=a("recharts-layer",o);return r().createElement("g",pt({className:c},nt(i,!0),{ref:e}),n)})),dt=function(t,e){for(var r=arguments.length,n=new Array(r>2?r-2:0),o=2;o<r;o++)n[o-2]=arguments[o]},vt=o(4704),mt=o.n(vt);Math.abs,Math.atan2;const bt=Math.cos,gt=(Math.max,Math.min,Math.sin),xt=Math.sqrt,wt=Math.PI,Ot=2*wt;const jt={draw(t,e){const r=xt(e/wt);t.moveTo(r,0),t.arc(0,0,r,0,Ot)}},St={draw(t,e){const r=xt(e/5)/2;t.moveTo(-3*r,-r),t.lineTo(-r,-r),t.lineTo(-r,-3*r),t.lineTo(r,-3*r),t.lineTo(r,-r),t.lineTo(3*r,-r),t.lineTo(3*r,r),t.lineTo(r,r),t.lineTo(r,3*r),t.lineTo(-r,3*r),t.lineTo(-r,r),t.lineTo(-3*r,r),t.closePath()}},At=xt(1/3),Pt=2*At,Et={draw(t,e){const r=xt(e/Pt),n=r*At;t.moveTo(0,-r),t.lineTo(n,0),t.lineTo(0,r),t.lineTo(-n,0),t.closePath()}},kt={draw(t,e){const r=xt(e),n=-r/2;t.rect(n,n,r,r)}},Tt=gt(wt/10)/gt(7*wt/10),Mt=gt(Ot/10)*Tt,_t=-bt(Ot/10)*Tt,Ct={draw(t,e){const r=xt(.8908130915292852*e),n=Mt*r,o=_t*r;t.moveTo(0,-r),t.lineTo(n,o);for(let e=1;e<5;++e){const i=Ot*e/5,a=bt(i),c=gt(i);t.lineTo(c*r,-a*r),t.lineTo(a*n-c*o,c*n+a*o)}t.closePath()}},Dt=xt(3),It={draw(t,e){const r=-xt(e/(3*Dt));t.moveTo(0,2*r),t.lineTo(-Dt*r,-r),t.lineTo(Dt*r,-r),t.closePath()}},Nt=-.5,Rt=xt(3)/2,Bt=1/xt(12),Lt=3*(Bt/2+1),zt={draw(t,e){const r=xt(e/Lt),n=r/2,o=r*Bt,i=n,a=r*Bt+r,c=-i,u=a;t.moveTo(n,o),t.lineTo(i,a),t.lineTo(c,u),t.lineTo(Nt*n-Rt*o,Rt*n+Nt*o),t.lineTo(Nt*i-Rt*a,Rt*i+Nt*a),t.lineTo(Nt*c-Rt*u,Rt*c+Nt*u),t.lineTo(Nt*n+Rt*o,Nt*o-Rt*n),t.lineTo(Nt*i+Rt*a,Nt*a-Rt*i),t.lineTo(Nt*c+Rt*u,Nt*u-Rt*c),t.closePath()}};function Ft(t){return function(){return t}}const Ut=Math.PI,Wt=2*Ut,$t=1e-6,Kt=Wt-$t;function qt(t){this._+=t[0];for(let e=1,r=t.length;e<r;++e)this._+=arguments[e]+t[e]}class Vt{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?qt:function(t){let e=Math.floor(t);if(!(e>=0))throw new Error(`invalid digits: ${t}`);if(e>15)return qt;const r=10**e;return function(t){this._+=t[0];for(let e=1,n=t.length;e<n;++e)this._+=Math.round(arguments[e]*r)/r+t[e]}}(t)}moveTo(t,e){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,e){this._append`L${this._x1=+t},${this._y1=+e}`}quadraticCurveTo(t,e,r,n){this._append`Q${+t},${+e},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(t,e,r,n,o,i){this._append`C${+t},${+e},${+r},${+n},${this._x1=+o},${this._y1=+i}`}arcTo(t,e,r,n,o){if(t=+t,e=+e,r=+r,n=+n,(o=+o)<0)throw new Error(`negative radius: ${o}`);let i=this._x1,a=this._y1,c=r-t,u=n-e,l=i-t,s=a-e,f=l*l+s*s;if(null===this._x1)this._append`M${this._x1=t},${this._y1=e}`;else if(f>$t)if(Math.abs(s*c-u*l)>$t&&o){let p=r-i,h=n-a,y=c*c+u*u,d=p*p+h*h,v=Math.sqrt(y),m=Math.sqrt(f),b=o*Math.tan((Ut-Math.acos((y+f-d)/(2*v*m)))/2),g=b/m,x=b/v;Math.abs(g-1)>$t&&this._append`L${t+g*l},${e+g*s}`,this._append`A${o},${o},0,0,${+(s*p>l*h)},${this._x1=t+x*c},${this._y1=e+x*u}`}else this._append`L${this._x1=t},${this._y1=e}`;else;}arc(t,e,r,n,o,i){if(t=+t,e=+e,i=!!i,(r=+r)<0)throw new Error(`negative radius: ${r}`);let a=r*Math.cos(n),c=r*Math.sin(n),u=t+a,l=e+c,s=1^i,f=i?n-o:o-n;null===this._x1?this._append`M${u},${l}`:(Math.abs(this._x1-u)>$t||Math.abs(this._y1-l)>$t)&&this._append`L${u},${l}`,r&&(f<0&&(f=f%Wt+Wt),f>Kt?this._append`A${r},${r},0,1,${s},${t-a},${e-c}A${r},${r},0,1,${s},${this._x1=u},${this._y1=l}`:f>$t&&this._append`A${r},${r},0,${+(f>=Ut)},${s},${this._x1=t+r*Math.cos(o)},${this._y1=e+r*Math.sin(o)}`)}rect(t,e,r,n){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}h${r=+r}v${+n}h${-r}Z`}toString(){return this._}}function Xt(t){let e=3;return t.digits=function(r){if(!arguments.length)return e;if(null==r)e=null;else{const t=Math.floor(r);if(!(t>=0))throw new RangeError(`invalid digits: ${r}`);e=t}return t},()=>new Vt(e)}Vt.prototype;xt(3),xt(3);function Ht(t){return Ht="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ht(t)}var Gt=["type","size","sizeType"];function Yt(){return Yt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Yt.apply(this,arguments)}function Zt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Jt(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Zt(Object(r),!0).forEach((function(e){Qt(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Zt(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Qt(t,e,r){var n;return n=function(t,e){if("object"!=Ht(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Ht(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==Ht(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function te(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var ee={symbolCircle:jt,symbolCross:St,symbolDiamond:Et,symbolSquare:kt,symbolStar:Ct,symbolTriangle:It,symbolWye:zt},re=Math.PI/180,ne=function(t){var e,n,o=t.type,i=void 0===o?"circle":o,c=t.size,u=void 0===c?64:c,l=t.sizeType,s=void 0===l?"area":l,f=Jt(Jt({},te(t,Gt)),{},{type:i,size:u,sizeType:s}),p=f.className,h=f.cx,y=f.cy,d=nt(f,!0);return h===+h&&y===+y&&u===+u?r().createElement("path",Yt({},d,{className:a("recharts-symbols",p),transform:"translate(".concat(h,", ").concat(y,")"),d:(e=function(t){var e="symbol".concat(mt()(t));return ee[e]||jt}(i),n=function(t,e){let r=null,n=Xt(o);function o(){let o;if(r||(r=o=n()),t.apply(this,arguments).draw(r,+e.apply(this,arguments)),o)return r=null,o+""||null}return t="function"==typeof t?t:Ft(t||jt),e="function"==typeof e?e:Ft(void 0===e?64:+e),o.type=function(e){return arguments.length?(t="function"==typeof e?e:Ft(e),o):t},o.size=function(t){return arguments.length?(e="function"==typeof t?t:Ft(+t),o):e},o.context=function(t){return arguments.length?(r=null==t?null:t,o):r},o}().type(e).size(function(t,e,r){if("area"===e)return t;switch(r){case"cross":return 5*t*t/9;case"diamond":return.5*t*t/Math.sqrt(3);case"square":return t*t;case"star":var n=18*re;return 1.25*t*t*(Math.tan(n)-Math.tan(2*n)*Math.pow(Math.tan(n),2));case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}}(u,s,i)),n())})):null};function oe(t){return oe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},oe(t)}function ie(){return ie=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ie.apply(this,arguments)}function ae(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function ce(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,he(n.key),n)}}function ue(t,e,r){return e=se(e),function(t,e){if(e&&("object"===oe(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,le()?Reflect.construct(e,r||[],se(t).constructor):e.apply(t,r))}function le(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(le=function(){return!!t})()}function se(t){return se=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},se(t)}function fe(t,e){return fe=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},fe(t,e)}function pe(t,e,r){return(e=he(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function he(t){var e=function(t,e){if("object"!=oe(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=oe(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==oe(e)?e:e+""}ne.registerSymbol=function(t,e){ee["symbol".concat(mt()(t))]=e};var ye=32,de=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),ue(this,e,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&fe(t,e)}(e,t),n=e,o=[{key:"renderIcon",value:function(t){var e=this.props.inactiveColor,n=16,o=ye/6,i=ye/3,a=t.inactive?e:t.color;if("plainline"===t.type)return r().createElement("line",{strokeWidth:4,fill:"none",stroke:a,strokeDasharray:t.payload.strokeDasharray,x1:0,y1:n,x2:ye,y2:n,className:"recharts-legend-icon"});if("line"===t.type)return r().createElement("path",{strokeWidth:4,fill:"none",stroke:a,d:"M0,".concat(n,"h").concat(i,"\n            A").concat(o,",").concat(o,",0,1,1,").concat(2*i,",").concat(n,"\n            H").concat(ye,"M").concat(2*i,",").concat(n,"\n            A").concat(o,",").concat(o,",0,1,1,").concat(i,",").concat(n),className:"recharts-legend-icon"});if("rect"===t.type)return r().createElement("path",{stroke:"none",fill:a,d:"M0,".concat(4,"h").concat(ye,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(r().isValidElement(t.legendIcon)){var c=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ae(Object(r),!0).forEach((function(e){pe(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ae(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}({},t);return delete c.legendIcon,r().cloneElement(t.legendIcon,c)}return r().createElement(ne,{fill:a,cx:n,cy:n,size:ye,sizeType:"diameter",type:t.type})}},{key:"renderItems",value:function(){var t=this,e=this.props,n=e.payload,o=e.iconSize,i=e.layout,c=e.formatter,u=e.inactiveColor,l={x:0,y:0,width:ye,height:ye},s={display:"horizontal"===i?"inline-block":"block",marginRight:10},f={display:"inline-block",verticalAlign:"middle",marginRight:4};return n.map((function(e,n){var i=e.formatter||c,p=a(pe(pe({"recharts-legend-item":!0},"legend-item-".concat(n),!0),"inactive",e.inactive));if("none"===e.type)return null;var h=y()(e.value)?null:e.value;dt(!y()(e.value),'The name property is also required when using a function for the dataKey of a chart\'s cartesian components. Ex: <Bar name="Name of my Data"/>');var d=e.inactive?u:e.color;return r().createElement("li",ie({className:p,style:s,key:"legend-item-".concat(n)},F(t.props,e,n)),r().createElement(st,{width:o,height:o,viewBox:l,style:f},t.renderIcon(e)),r().createElement("span",{className:"recharts-legend-item-text",style:{color:d}},i?i(h,e,n):h))}))}},{key:"render",value:function(){var t=this.props,e=t.payload,n=t.layout,o=t.align;if(!e||!e.length)return null;var i={padding:0,margin:0,textAlign:"horizontal"===n?o:"left"};return r().createElement("ul",{className:"recharts-default-legend",style:i},this.renderItems())}}],o&&ce(n.prototype,o),i&&ce(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,o,i}(e.PureComponent);pe(de,"displayName","Legend"),pe(de,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var ve=o(6424),me=o.n(ve);function be(t,e,r){return!0===e?me()(t,r):y()(e)?me()(t,e):t}function ge(t){return ge="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ge(t)}var xe=["ref"];function we(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Oe(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?we(Object(r),!0).forEach((function(e){ke(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):we(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function je(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Te(n.key),n)}}function Se(t,e,r){return e=Pe(e),function(t,e){if(e&&("object"===ge(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Ae()?Reflect.construct(e,r||[],Pe(t).constructor):e.apply(t,r))}function Ae(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(Ae=function(){return!!t})()}function Pe(t){return Pe=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Pe(t)}function Ee(t,e){return Ee=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Ee(t,e)}function ke(t,e,r){return(e=Te(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Te(t){var e=function(t,e){if("object"!=ge(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ge(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ge(e)?e:e+""}function Me(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function _e(t){return t.value}var Ce=function(t){function e(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return ke(t=Se(this,e,[].concat(n)),"lastBoundingBox",{width:-1,height:-1}),t}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Ee(t,e)}(e,t),n=e,i=[{key:"getWithHeight",value:function(t,e){var r=Oe(Oe({},this.defaultProps),t.props).layout;return"vertical"===r&&S(t.props.height)?{height:t.props.height}:"horizontal"===r?{width:t.props.width||e}:null}}],(o=[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();return t.height=this.wrapperNode.offsetHeight,t.width=this.wrapperNode.offsetWidth,t}return null}},{key:"updateBBox",value:function(){var t=this.props.onBBoxUpdate,e=this.getBBox();e?(Math.abs(e.width-this.lastBoundingBox.width)>1||Math.abs(e.height-this.lastBoundingBox.height)>1)&&(this.lastBoundingBox.width=e.width,this.lastBoundingBox.height=e.height,t&&t(e)):-1===this.lastBoundingBox.width&&-1===this.lastBoundingBox.height||(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,t&&t(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?Oe({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(t){var e,r,n=this.props,o=n.layout,i=n.align,a=n.verticalAlign,c=n.margin,u=n.chartWidth,l=n.chartHeight;return t&&(void 0!==t.left&&null!==t.left||void 0!==t.right&&null!==t.right)||(e="center"===i&&"vertical"===o?{left:((u||0)-this.getBBoxSnapshot().width)/2}:"right"===i?{right:c&&c.right||0}:{left:c&&c.left||0}),t&&(void 0!==t.top&&null!==t.top||void 0!==t.bottom&&null!==t.bottom)||(r="middle"===a?{top:((l||0)-this.getBBoxSnapshot().height)/2}:"bottom"===a?{bottom:c&&c.bottom||0}:{top:c&&c.top||0}),Oe(Oe({},e),r)}},{key:"render",value:function(){var t=this,e=this.props,n=e.content,o=e.width,i=e.height,a=e.wrapperStyle,c=e.payloadUniqBy,u=e.payload,l=Oe(Oe({position:"absolute",width:o||"auto",height:i||"auto"},this.getDefaultPosition(a)),a);return r().createElement("div",{className:"recharts-legend-wrapper",style:l,ref:function(e){t.wrapperNode=e}},function(t,e){if(r().isValidElement(t))return r().cloneElement(t,e);if("function"==typeof t)return r().createElement(t,e);e.ref;var n=Me(e,xe);return r().createElement(de,n)}(n,Oe(Oe({},this.props),{},{payload:be(u,c,_e)})))}}])&&je(n.prototype,o),i&&je(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,o,i}(e.PureComponent);ke(Ce,"displayName","Legend"),ke(Ce,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});var De=o(1232),Ie=o.n(De);function Ne(t){return Ne="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ne(t)}function Re(){return Re=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Re.apply(this,arguments)}function Be(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return Le(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Le(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Le(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function ze(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Fe(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ze(Object(r),!0).forEach((function(e){Ue(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ze(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Ue(t,e,r){var n;return n=function(t,e){if("object"!=Ne(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Ne(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==Ne(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function We(t){return Array.isArray(t)&&A(t[0])&&A(t[1])?t.join(" ~ "):t}var $e=function(t){var e=t.separator,n=void 0===e?" : ":e,o=t.contentStyle,i=void 0===o?{}:o,c=t.itemStyle,u=void 0===c?{}:c,l=t.labelStyle,f=void 0===l?{}:l,p=t.payload,h=t.formatter,y=t.itemSorter,d=t.wrapperClassName,v=t.labelClassName,m=t.label,b=t.labelFormatter,g=t.accessibilityLayer,x=void 0!==g&&g,w=Fe({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},i),O=Fe({margin:0},f),j=!s()(m),S=j?m:"",P=a("recharts-default-tooltip",d),E=a("recharts-tooltip-label",v);j&&b&&null!=p&&(S=b(m,p));var k=x?{role:"status","aria-live":"assertive"}:{};return r().createElement("div",Re({className:P,style:w},k),r().createElement("p",{className:E,style:O},r().isValidElement(S)?S:"".concat(S)),function(){if(p&&p.length){var t=(y?Ie()(p,y):p).map((function(t,e){if("none"===t.type)return null;var o=Fe({display:"block",paddingTop:4,paddingBottom:4,color:t.color||"#000"},u),i=t.formatter||h||We,a=t.value,c=t.name,l=a,s=c;if(i&&null!=l&&null!=s){var f=i(a,c,t,e,p);if(Array.isArray(f)){var y=Be(f,2);l=y[0],s=y[1]}else l=f}return r().createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(e),style:o},A(s)?r().createElement("span",{className:"recharts-tooltip-item-name"},s):null,A(s)?r().createElement("span",{className:"recharts-tooltip-item-separator"},n):null,r().createElement("span",{className:"recharts-tooltip-item-value"},l),r().createElement("span",{className:"recharts-tooltip-item-unit"},t.unit||""))}));return r().createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},t)}return null}())};function Ke(t){return Ke="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ke(t)}function qe(t,e,r){var n;return n=function(t,e){if("object"!=Ke(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Ke(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==Ke(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Ve="recharts-tooltip-wrapper",Xe={visibility:"hidden"};function He(t){var e=t.coordinate,r=t.translateX,n=t.translateY;return a(Ve,qe(qe(qe(qe({},"".concat(Ve,"-right"),S(r)&&e&&S(e.x)&&r>=e.x),"".concat(Ve,"-left"),S(r)&&e&&S(e.x)&&r<e.x),"".concat(Ve,"-bottom"),S(n)&&e&&S(e.y)&&n>=e.y),"".concat(Ve,"-top"),S(n)&&e&&S(e.y)&&n<e.y))}function Ge(t){var e=t.allowEscapeViewBox,r=t.coordinate,n=t.key,o=t.offsetTopLeft,i=t.position,a=t.reverseDirection,c=t.tooltipDimension,u=t.viewBox,l=t.viewBoxDimension;if(i&&S(i[n]))return i[n];var s=r[n]-c-o,f=r[n]+o;return e[n]?a[n]?s:f:a[n]?s<u[n]?Math.max(f,u[n]):Math.max(s,u[n]):f+c>u[n]+l?Math.max(s,u[n]):Math.max(f,u[n])}function Ye(t){return Ye="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ye(t)}function Ze(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Je(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Ze(Object(r),!0).forEach((function(e){or(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ze(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Qe(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ir(n.key),n)}}function tr(t,e,r){return e=rr(e),function(t,e){if(e&&("object"===Ye(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,er()?Reflect.construct(e,r||[],rr(t).constructor):e.apply(t,r))}function er(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(er=function(){return!!t})()}function rr(t){return rr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},rr(t)}function nr(t,e){return nr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},nr(t,e)}function or(t,e,r){return(e=ir(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ir(t){var e=function(t,e){if("object"!=Ye(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Ye(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Ye(e)?e:e+""}var ar=function(t){function e(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return or(t=tr(this,e,[].concat(n)),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),or(t,"handleKeyDown",(function(e){var r,n,o,i;"Escape"===e.key&&t.setState({dismissed:!0,dismissedAtCoordinate:{x:null!==(r=null===(n=t.props.coordinate)||void 0===n?void 0:n.x)&&void 0!==r?r:0,y:null!==(o=null===(i=t.props.coordinate)||void 0===i?void 0:i.y)&&void 0!==o?o:0}})})),t}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&nr(t,e)}(e,t),n=e,o=[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();(Math.abs(t.width-this.state.lastBoundingBox.width)>1||Math.abs(t.height-this.state.lastBoundingBox.height)>1)&&this.setState({lastBoundingBox:{width:t.width,height:t.height}})}else-1===this.state.lastBoundingBox.width&&-1===this.state.lastBoundingBox.height||this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var t,e;this.props.active&&this.updateBBox(),this.state.dismissed&&((null===(t=this.props.coordinate)||void 0===t?void 0:t.x)===this.state.dismissedAtCoordinate.x&&(null===(e=this.props.coordinate)||void 0===e?void 0:e.y)===this.state.dismissedAtCoordinate.y||(this.state.dismissed=!1))}},{key:"render",value:function(){var t=this,e=this.props,n=e.active,o=e.allowEscapeViewBox,i=e.animationDuration,a=e.animationEasing,c=e.children,u=e.coordinate,l=e.hasPayload,s=e.isAnimationActive,f=e.offset,p=e.position,h=e.reverseDirection,y=e.useTranslate3d,d=e.viewBox,v=e.wrapperStyle,m=function(t){var e,r,n=t.allowEscapeViewBox,o=t.coordinate,i=t.offsetTopLeft,a=t.position,c=t.reverseDirection,u=t.tooltipBox,l=t.useTranslate3d,s=t.viewBox;return{cssProperties:u.height>0&&u.width>0&&o?function(t){var e=t.translateX,r=t.translateY;return{transform:t.useTranslate3d?"translate3d(".concat(e,"px, ").concat(r,"px, 0)"):"translate(".concat(e,"px, ").concat(r,"px)")}}({translateX:e=Ge({allowEscapeViewBox:n,coordinate:o,key:"x",offsetTopLeft:i,position:a,reverseDirection:c,tooltipDimension:u.width,viewBox:s,viewBoxDimension:s.width}),translateY:r=Ge({allowEscapeViewBox:n,coordinate:o,key:"y",offsetTopLeft:i,position:a,reverseDirection:c,tooltipDimension:u.height,viewBox:s,viewBoxDimension:s.height}),useTranslate3d:l}):Xe,cssClasses:He({translateX:e,translateY:r,coordinate:o})}}({allowEscapeViewBox:o,coordinate:u,offsetTopLeft:f,position:p,reverseDirection:h,tooltipBox:this.state.lastBoundingBox,useTranslate3d:y,viewBox:d}),b=m.cssClasses,g=m.cssProperties,x=Je(Je({transition:s&&n?"transform ".concat(i,"ms ").concat(a):void 0},g),{},{pointerEvents:"none",visibility:!this.state.dismissed&&n&&l?"visible":"hidden",position:"absolute",top:0,left:0},v);return r().createElement("div",{tabIndex:-1,className:b,style:x,ref:function(e){t.wrapperNode=e}},c)}}],o&&Qe(n.prototype,o),i&&Qe(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,o,i}(e.PureComponent),cr={isSsr:!("undefined"!=typeof window&&window.document&&window.document.createElement&&window.setTimeout),get:function(t){return cr[t]},set:function(t,e){if("string"==typeof t)cr[t]=e;else{var r=Object.keys(t);r&&r.length&&r.forEach((function(e){cr[e]=t[e]}))}}};function ur(t){return ur="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ur(t)}function lr(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function sr(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?lr(Object(r),!0).forEach((function(e){vr(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lr(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function fr(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,mr(n.key),n)}}function pr(t,e,r){return e=yr(e),function(t,e){if(e&&("object"===ur(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,hr()?Reflect.construct(e,r||[],yr(t).constructor):e.apply(t,r))}function hr(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(hr=function(){return!!t})()}function yr(t){return yr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},yr(t)}function dr(t,e){return dr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},dr(t,e)}function vr(t,e,r){return(e=mr(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function mr(t){var e=function(t,e){if("object"!=ur(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ur(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ur(e)?e:e+""}function br(t){return t.dataKey}var gr=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),pr(this,e,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&dr(t,e)}(e,t),n=e,o=[{key:"render",value:function(){var t=this,e=this.props,n=e.active,o=e.allowEscapeViewBox,i=e.animationDuration,a=e.animationEasing,c=e.content,u=e.coordinate,l=e.filterNull,s=e.isAnimationActive,f=e.offset,p=e.payload,h=e.payloadUniqBy,y=e.position,d=e.reverseDirection,v=e.useTranslate3d,m=e.viewBox,b=e.wrapperStyle,g=null!=p?p:[];l&&g.length&&(g=be(p.filter((function(e){return null!=e.value&&(!0!==e.hide||t.props.includeHidden)})),h,br));var x=g.length>0;return r().createElement(ar,{allowEscapeViewBox:o,animationDuration:i,animationEasing:a,isAnimationActive:s,active:n,coordinate:u,hasPayload:x,offset:f,position:y,reverseDirection:d,useTranslate3d:v,viewBox:m,wrapperStyle:b},function(t,e){return r().isValidElement(t)?r().cloneElement(t,e):"function"==typeof t?r().createElement(t,e):r().createElement($e,e)}(c,sr(sr({},this.props),{},{payload:g})))}}],o&&fr(n.prototype,o),i&&fr(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,o,i}(e.PureComponent);vr(gr,"displayName","Tooltip"),vr(gr,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!cr.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var xr=o(9212),wr=o.n(xr);function Or(t){return Or="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Or(t)}function jr(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Sr(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?jr(Object(r),!0).forEach((function(e){Ar(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):jr(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Ar(t,e,r){var n;return n=function(t,e){if("object"!=Or(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Or(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==Or(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Pr(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return Er(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Er(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Er(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var kr=(0,e.forwardRef)((function(t,n){var o=t.aspect,i=t.initialDimension,c=void 0===i?{width:-1,height:-1}:i,u=t.width,l=void 0===u?"100%":u,s=t.height,f=void 0===s?"100%":s,p=t.minWidth,h=void 0===p?0:p,y=t.minHeight,d=t.maxHeight,v=t.children,m=t.debounce,b=void 0===m?0:m,g=t.id,x=t.className,w=t.onResize,O=t.style,S=void 0===O?{}:O,A=(0,e.useRef)(null),P=(0,e.useRef)();P.current=w,(0,e.useImperativeHandle)(n,(function(){return Object.defineProperty(A.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),A.current},configurable:!0})}));var E=Pr((0,e.useState)({containerWidth:c.width,containerHeight:c.height}),2),k=E[0],T=E[1],M=(0,e.useCallback)((function(t,e){T((function(r){var n=Math.round(t),o=Math.round(e);return r.containerWidth===n&&r.containerHeight===o?r:{containerWidth:n,containerHeight:o}}))}),[]);(0,e.useEffect)((function(){var t=function(t){var e,r=t[0].contentRect,n=r.width,o=r.height;M(n,o),null===(e=P.current)||void 0===e||e.call(P,n,o)};b>0&&(t=wr()(t,b,{trailing:!0,leading:!1}));var e=new ResizeObserver(t),r=A.current.getBoundingClientRect(),n=r.width,o=r.height;return M(n,o),e.observe(A.current),function(){e.disconnect()}}),[M,b]);var _=(0,e.useMemo)((function(){var t=k.containerWidth,n=k.containerHeight;if(t<0||n<0)return null;dt(j(l)||j(f),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",l,f),dt(!o||o>0,"The aspect(%s) must be greater than zero.",o);var i=j(l)?t:l,a=j(f)?n:f;o&&o>0&&(i?a=i/o:a&&(i=a*o),d&&a>d&&(a=d)),dt(i>0||a>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",i,a,l,f,h,y,o);var c=!Array.isArray(v)&&V(v.type).endsWith("Chart");return r().Children.map(v,(function(t){return r().isValidElement(t)?(0,e.cloneElement)(t,Sr({width:i,height:a},c?{style:Sr({height:"100%",width:"100%",maxHeight:a,maxWidth:i},t.props.style)}:{})):t}))}),[o,v,f,d,y,h,k,l]);return r().createElement("div",{id:g?"".concat(g):void 0,className:a("recharts-responsive-container",x),style:Sr(Sr({},S),{},{width:l,height:f,minWidth:h,minHeight:y,maxHeight:d}),ref:A},_)})),Tr=function(t){return null};function Mr(t){return Mr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Mr(t)}function _r(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Cr(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?_r(Object(r),!0).forEach((function(e){Dr(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):_r(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Dr(t,e,r){var n;return n=function(t,e){if("object"!=Mr(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Mr(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==Mr(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}Tr.displayName="Cell";var Ir={widthCache:{},cacheCount:0},Nr={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},Rr="recharts_measurement_span";var Br=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==t||cr.isSsr)return{width:0,height:0};var r,n=(r=Cr({},e),Object.keys(r).forEach((function(t){r[t]||delete r[t]})),r),o=JSON.stringify({text:t,copyStyle:n});if(Ir.widthCache[o])return Ir.widthCache[o];try{var i=document.getElementById(Rr);i||((i=document.createElement("span")).setAttribute("id",Rr),i.setAttribute("aria-hidden","true"),document.body.appendChild(i));var a=Cr(Cr({},Nr),n);Object.assign(i.style,a),i.textContent="".concat(t);var c=i.getBoundingClientRect(),u={width:c.width,height:c.height};return Ir.widthCache[o]=u,++Ir.cacheCount>2e3&&(Ir.cacheCount=0,Ir.widthCache={}),u}catch(t){return{width:0,height:0}}};function Lr(t){return Lr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Lr(t)}function zr(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return Fr(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Fr(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Fr(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Ur(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Wr(n.key),n)}}function Wr(t){var e=function(t,e){if("object"!=Lr(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Lr(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Lr(e)?e:e+""}var $r=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,Kr=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,qr=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,Vr=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,Xr={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},Hr=Object.keys(Xr),Gr="NaN";var Yr=function(){function t(e,r){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.num=e,this.unit=r,this.num=e,this.unit=r,Number.isNaN(e)&&(this.unit=""),""===r||qr.test(r)||(this.num=NaN,this.unit=""),Hr.includes(r)&&(this.num=function(t,e){return t*Xr[e]}(e,r),this.unit="px")}return e=t,n=[{key:"parse",value:function(e){var r,n=zr(null!==(r=Vr.exec(e))&&void 0!==r?r:[],3),o=n[1],i=n[2];return new t(parseFloat(o),null!=i?i:"")}}],(r=[{key:"add",value:function(e){return this.unit!==e.unit?new t(NaN,""):new t(this.num+e.num,this.unit)}},{key:"subtract",value:function(e){return this.unit!==e.unit?new t(NaN,""):new t(this.num-e.num,this.unit)}},{key:"multiply",value:function(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new t(NaN,""):new t(this.num*e.num,this.unit||e.unit)}},{key:"divide",value:function(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new t(NaN,""):new t(this.num/e.num,this.unit||e.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}])&&Ur(e.prototype,r),n&&Ur(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,r,n}();function Zr(t){if(t.includes(Gr))return Gr;for(var e=t;e.includes("*")||e.includes("/");){var r,n=zr(null!==(r=$r.exec(e))&&void 0!==r?r:[],4),o=n[1],i=n[2],a=n[3],c=Yr.parse(null!=o?o:""),u=Yr.parse(null!=a?a:""),l="*"===i?c.multiply(u):c.divide(u);if(l.isNaN())return Gr;e=e.replace($r,l.toString())}for(;e.includes("+")||/.-\d+(?:\.\d+)?/.test(e);){var s,f=zr(null!==(s=Kr.exec(e))&&void 0!==s?s:[],4),p=f[1],h=f[2],y=f[3],d=Yr.parse(null!=p?p:""),v=Yr.parse(null!=y?y:""),m="+"===h?d.add(v):d.subtract(v);if(m.isNaN())return Gr;e=e.replace(Kr,m.toString())}return e}var Jr=/\(([^()]*)\)/;function Qr(t){var e=t.replace(/\s+/g,"");return e=function(t){for(var e=t;e.includes("(");){var r=zr(Jr.exec(e),2)[1];e=e.replace(Jr,Zr(r))}return e}(e),e=Zr(e)}function tn(t){var e=function(t){try{return Qr(t)}catch(t){return Gr}}(t.slice(5,-1));return e===Gr?"":e}var en=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],rn=["dx","dy","angle","className","breakAll"];function nn(){return nn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},nn.apply(this,arguments)}function on(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function an(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return cn(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return cn(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function cn(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var un=/[ \f\n\r\t\v\u2028\u2029]+/,ln=function(t){var e=t.children,r=t.breakAll,n=t.style;try{var o=[];return s()(e)||(o=r?e.toString().split(""):e.toString().split(un)),{wordsWithComputedWidth:o.map((function(t){return{word:t,width:Br(t,n).width}})),spaceWidth:r?0:Br(" ",n).width}}catch(t){return null}},sn=function(t){return[{words:s()(t)?[]:t.toString().split(un)}]},fn=function(t){var e=t.width,r=t.scaleToFit,n=t.children,o=t.style,i=t.breakAll,a=t.maxLines;if((e||r)&&!cr.isSsr){var c=ln({breakAll:i,children:n,style:o});return c?function(t,e,r,n,o){var i=t.maxLines,a=t.children,c=t.style,u=t.breakAll,l=S(i),s=a,f=function(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]).reduce((function(t,e){var i=e.word,a=e.width,c=t[t.length-1];if(c&&(null==n||o||c.width+a+r<Number(n)))c.words.push(i),c.width+=a+r;else{var u={words:[i],width:a};t.push(u)}return t}),[])},p=f(e);if(!l)return p;for(var h,y=function(t){var e=s.slice(0,t),r=ln({breakAll:u,style:c,children:e+"…"}).wordsWithComputedWidth,o=f(r),a=o.length>i||function(t){return t.reduce((function(t,e){return t.width>e.width?t:e}))}(o).width>Number(n);return[a,o]},d=0,v=s.length-1,m=0;d<=v&&m<=s.length-1;){var b=Math.floor((d+v)/2),g=an(y(b-1),2),x=g[0],w=g[1],O=an(y(b),1)[0];if(x||O||(d=b+1),x&&O&&(v=b-1),!x&&O){h=w;break}m++}return h||p}({breakAll:i,children:n,maxLines:a,style:o},c.wordsWithComputedWidth,c.spaceWidth,e,r):sn(n)}return sn(n)},pn="#808080",hn=function(t){var n=t.x,o=void 0===n?0:n,i=t.y,c=void 0===i?0:i,u=t.lineHeight,l=void 0===u?"1em":u,s=t.capHeight,f=void 0===s?"0.71em":s,p=t.scaleToFit,h=void 0!==p&&p,y=t.textAnchor,d=void 0===y?"start":y,v=t.verticalAnchor,m=void 0===v?"end":v,b=t.fill,g=void 0===b?pn:b,x=on(t,en),w=(0,e.useMemo)((function(){return fn({breakAll:x.breakAll,children:x.children,maxLines:x.maxLines,scaleToFit:h,style:x.style,width:x.width})}),[x.breakAll,x.children,x.maxLines,h,x.style,x.width]),O=x.dx,j=x.dy,P=x.angle,E=x.className,k=x.breakAll,T=on(x,rn);if(!A(o)||!A(c))return null;var M,_=o+(S(O)?O:0),C=c+(S(j)?j:0);switch(m){case"start":M=tn("calc(".concat(f,")"));break;case"middle":M=tn("calc(".concat((w.length-1)/2," * -").concat(l," + (").concat(f," / 2))"));break;default:M=tn("calc(".concat(w.length-1," * -").concat(l,")"))}var D=[];if(h){var I=w[0].width,N=x.width;D.push("scale(".concat((S(N)?N/I:1)/I,")"))}return P&&D.push("rotate(".concat(P,", ").concat(_,", ").concat(C,")")),D.length&&(T.transform=D.join(" ")),r().createElement("text",nn({},nt(T,!0),{x:_,y:C,className:a("recharts-text",E),textAnchor:d,fill:g.includes("url")?pn:g}),w.map((function(t,e){var n=t.words.join(k?"":" ");return r().createElement("tspan",{x:_,dy:0===e?M:l,key:"".concat(n,"-").concat(e)},n)})))};function yn(t,e){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(e).domain(t)}return this}function dn(t,e){switch(arguments.length){case 0:break;case 1:"function"==typeof t?this.interpolator(t):this.range(t);break;default:this.domain(t),"function"==typeof e?this.interpolator(e):this.range(e)}return this}class vn extends Map{constructor(t,e=xn){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),null!=t)for(const[e,r]of t)this.set(e,r)}get(t){return super.get(mn(this,t))}has(t){return super.has(mn(this,t))}set(t,e){return super.set(bn(this,t),e)}delete(t){return super.delete(gn(this,t))}}Set;function mn({_intern:t,_key:e},r){const n=e(r);return t.has(n)?t.get(n):r}function bn({_intern:t,_key:e},r){const n=e(r);return t.has(n)?t.get(n):(t.set(n,r),r)}function gn({_intern:t,_key:e},r){const n=e(r);return t.has(n)&&(r=t.get(n),t.delete(n)),r}function xn(t){return null!==t&&"object"==typeof t?t.valueOf():t}const wn=Symbol("implicit");function On(){var t=new vn,e=[],r=[],n=wn;function o(o){let i=t.get(o);if(void 0===i){if(n!==wn)return n;t.set(o,i=e.push(o)-1)}return r[i%r.length]}return o.domain=function(r){if(!arguments.length)return e.slice();e=[],t=new vn;for(const n of r)t.has(n)||t.set(n,e.push(n)-1);return o},o.range=function(t){return arguments.length?(r=Array.from(t),o):r.slice()},o.unknown=function(t){return arguments.length?(n=t,o):n},o.copy=function(){return On(e,r).unknown(n)},yn.apply(o,arguments),o}function jn(){var t,e,r=On().unknown(void 0),n=r.domain,o=r.range,i=0,a=1,c=!1,u=0,l=0,s=.5;function f(){var r=n().length,f=a<i,p=f?a:i,h=f?i:a;t=(h-p)/Math.max(1,r-u+2*l),c&&(t=Math.floor(t)),p+=(h-p-t*(r-u))*s,e=t*(1-u),c&&(p=Math.round(p),e=Math.round(e));var y=function(t,e,r){t=+t,e=+e,r=(o=arguments.length)<2?(e=t,t=0,1):o<3?1:+r;for(var n=-1,o=0|Math.max(0,Math.ceil((e-t)/r)),i=new Array(o);++n<o;)i[n]=t+n*r;return i}(r).map((function(e){return p+t*e}));return o(f?y.reverse():y)}return delete r.unknown,r.domain=function(t){return arguments.length?(n(t),f()):n()},r.range=function(t){return arguments.length?([i,a]=t,i=+i,a=+a,f()):[i,a]},r.rangeRound=function(t){return[i,a]=t,i=+i,a=+a,c=!0,f()},r.bandwidth=function(){return e},r.step=function(){return t},r.round=function(t){return arguments.length?(c=!!t,f()):c},r.padding=function(t){return arguments.length?(u=Math.min(1,l=+t),f()):u},r.paddingInner=function(t){return arguments.length?(u=Math.min(1,t),f()):u},r.paddingOuter=function(t){return arguments.length?(l=+t,f()):l},r.align=function(t){return arguments.length?(s=Math.max(0,Math.min(1,t)),f()):s},r.copy=function(){return jn(n(),[i,a]).round(c).paddingInner(u).paddingOuter(l).align(s)},yn.apply(f(),arguments)}function Sn(t){var e=t.copy;return t.padding=t.paddingOuter,delete t.paddingInner,delete t.paddingOuter,t.copy=function(){return Sn(e())},t}function An(){return Sn(jn.apply(null,arguments).paddingInner(1))}const Pn=Math.sqrt(50),En=Math.sqrt(10),kn=Math.sqrt(2);function Tn(t,e,r){const n=(e-t)/Math.max(0,r),o=Math.floor(Math.log10(n)),i=n/Math.pow(10,o),a=i>=Pn?10:i>=En?5:i>=kn?2:1;let c,u,l;return o<0?(l=Math.pow(10,-o)/a,c=Math.round(t*l),u=Math.round(e*l),c/l<t&&++c,u/l>e&&--u,l=-l):(l=Math.pow(10,o)*a,c=Math.round(t/l),u=Math.round(e/l),c*l<t&&++c,u*l>e&&--u),u<c&&.5<=r&&r<2?Tn(t,e,2*r):[c,u,l]}function Mn(t,e,r){if(!((r=+r)>0))return[];if((t=+t)===(e=+e))return[t];const n=e<t,[o,i,a]=n?Tn(e,t,r):Tn(t,e,r);if(!(i>=o))return[];const c=i-o+1,u=new Array(c);if(n)if(a<0)for(let t=0;t<c;++t)u[t]=(i-t)/-a;else for(let t=0;t<c;++t)u[t]=(i-t)*a;else if(a<0)for(let t=0;t<c;++t)u[t]=(o+t)/-a;else for(let t=0;t<c;++t)u[t]=(o+t)*a;return u}function _n(t,e,r){return Tn(t=+t,e=+e,r=+r)[2]}function Cn(t,e,r){r=+r;const n=(e=+e)<(t=+t),o=n?_n(e,t,r):_n(t,e,r);return(n?-1:1)*(o<0?1/-o:o)}function Dn(t,e){return null==t||null==e?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function In(t,e){return null==t||null==e?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function Nn(t){let e,r,n;function o(t,n,o=0,i=t.length){if(o<i){if(0!==e(n,n))return i;do{const e=o+i>>>1;r(t[e],n)<0?o=e+1:i=e}while(o<i)}return o}return 2!==t.length?(e=Dn,r=(e,r)=>Dn(t(e),r),n=(e,r)=>t(e)-r):(e=t===Dn||t===In?t:Rn,r=t,n=t),{left:o,center:function(t,e,r=0,i=t.length){const a=o(t,e,r,i-1);return a>r&&n(t[a-1],e)>-n(t[a],e)?a-1:a},right:function(t,n,o=0,i=t.length){if(o<i){if(0!==e(n,n))return i;do{const e=o+i>>>1;r(t[e],n)<=0?o=e+1:i=e}while(o<i)}return o}}}function Rn(){return 0}function Bn(t){return null===t?NaN:+t}const Ln=Nn(Dn),zn=Ln.right,Fn=(Ln.left,Nn(Bn).center,zn);function Un(t,e,r){t.prototype=e.prototype=r,r.constructor=t}function Wn(t,e){var r=Object.create(t.prototype);for(var n in e)r[n]=e[n];return r}function $n(){}var Kn=.7,qn=1/Kn,Vn="\\s*([+-]?\\d+)\\s*",Xn="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",Hn="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",Gn=/^#([0-9a-f]{3,8})$/,Yn=new RegExp(`^rgb\\(${Vn},${Vn},${Vn}\\)$`),Zn=new RegExp(`^rgb\\(${Hn},${Hn},${Hn}\\)$`),Jn=new RegExp(`^rgba\\(${Vn},${Vn},${Vn},${Xn}\\)$`),Qn=new RegExp(`^rgba\\(${Hn},${Hn},${Hn},${Xn}\\)$`),to=new RegExp(`^hsl\\(${Xn},${Hn},${Hn}\\)$`),eo=new RegExp(`^hsla\\(${Xn},${Hn},${Hn},${Xn}\\)$`),ro={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function no(){return this.rgb().formatHex()}function oo(){return this.rgb().formatRgb()}function io(t){var e,r;return t=(t+"").trim().toLowerCase(),(e=Gn.exec(t))?(r=e[1].length,e=parseInt(e[1],16),6===r?ao(e):3===r?new lo(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===r?co(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===r?co(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=Yn.exec(t))?new lo(e[1],e[2],e[3],1):(e=Zn.exec(t))?new lo(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=Jn.exec(t))?co(e[1],e[2],e[3],e[4]):(e=Qn.exec(t))?co(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=to.exec(t))?vo(e[1],e[2]/100,e[3]/100,1):(e=eo.exec(t))?vo(e[1],e[2]/100,e[3]/100,e[4]):ro.hasOwnProperty(t)?ao(ro[t]):"transparent"===t?new lo(NaN,NaN,NaN,0):null}function ao(t){return new lo(t>>16&255,t>>8&255,255&t,1)}function co(t,e,r,n){return n<=0&&(t=e=r=NaN),new lo(t,e,r,n)}function uo(t,e,r,n){return 1===arguments.length?((o=t)instanceof $n||(o=io(o)),o?new lo((o=o.rgb()).r,o.g,o.b,o.opacity):new lo):new lo(t,e,r,null==n?1:n);var o}function lo(t,e,r,n){this.r=+t,this.g=+e,this.b=+r,this.opacity=+n}function so(){return`#${yo(this.r)}${yo(this.g)}${yo(this.b)}`}function fo(){const t=po(this.opacity);return`${1===t?"rgb(":"rgba("}${ho(this.r)}, ${ho(this.g)}, ${ho(this.b)}${1===t?")":`, ${t})`}`}function po(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function ho(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function yo(t){return((t=ho(t))<16?"0":"")+t.toString(16)}function vo(t,e,r,n){return n<=0?t=e=r=NaN:r<=0||r>=1?t=e=NaN:e<=0&&(t=NaN),new bo(t,e,r,n)}function mo(t){if(t instanceof bo)return new bo(t.h,t.s,t.l,t.opacity);if(t instanceof $n||(t=io(t)),!t)return new bo;if(t instanceof bo)return t;var e=(t=t.rgb()).r/255,r=t.g/255,n=t.b/255,o=Math.min(e,r,n),i=Math.max(e,r,n),a=NaN,c=i-o,u=(i+o)/2;return c?(a=e===i?(r-n)/c+6*(r<n):r===i?(n-e)/c+2:(e-r)/c+4,c/=u<.5?i+o:2-i-o,a*=60):c=u>0&&u<1?0:a,new bo(a,c,u,t.opacity)}function bo(t,e,r,n){this.h=+t,this.s=+e,this.l=+r,this.opacity=+n}function go(t){return(t=(t||0)%360)<0?t+360:t}function xo(t){return Math.max(0,Math.min(1,t||0))}function wo(t,e,r){return 255*(t<60?e+(r-e)*t/60:t<180?r:t<240?e+(r-e)*(240-t)/60:e)}function Oo(t,e,r,n,o){var i=t*t,a=i*t;return((1-3*t+3*i-a)*e+(4-6*i+3*a)*r+(1+3*t+3*i-3*a)*n+a*o)/6}Un($n,io,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:no,formatHex:no,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return mo(this).formatHsl()},formatRgb:oo,toString:oo}),Un(lo,uo,Wn($n,{brighter(t){return t=null==t?qn:Math.pow(qn,t),new lo(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?Kn:Math.pow(Kn,t),new lo(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new lo(ho(this.r),ho(this.g),ho(this.b),po(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:so,formatHex:so,formatHex8:function(){return`#${yo(this.r)}${yo(this.g)}${yo(this.b)}${yo(255*(isNaN(this.opacity)?1:this.opacity))}`},formatRgb:fo,toString:fo})),Un(bo,(function(t,e,r,n){return 1===arguments.length?mo(t):new bo(t,e,r,null==n?1:n)}),Wn($n,{brighter(t){return t=null==t?qn:Math.pow(qn,t),new bo(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?Kn:Math.pow(Kn,t),new bo(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+360*(this.h<0),e=isNaN(t)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*e,o=2*r-n;return new lo(wo(t>=240?t-240:t+120,o,n),wo(t,o,n),wo(t<120?t+240:t-120,o,n),this.opacity)},clamp(){return new bo(go(this.h),xo(this.s),xo(this.l),po(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const t=po(this.opacity);return`${1===t?"hsl(":"hsla("}${go(this.h)}, ${100*xo(this.s)}%, ${100*xo(this.l)}%${1===t?")":`, ${t})`}`}}));const jo=t=>()=>t;function So(t,e){return function(r){return t+r*e}}function Ao(t){return 1==(t=+t)?Po:function(e,r){return r-e?function(t,e,r){return t=Math.pow(t,r),e=Math.pow(e,r)-t,r=1/r,function(n){return Math.pow(t+n*e,r)}}(e,r,t):jo(isNaN(e)?r:e)}}function Po(t,e){var r=e-t;return r?So(t,r):jo(isNaN(t)?e:t)}const Eo=function t(e){var r=Ao(e);function n(t,e){var n=r((t=uo(t)).r,(e=uo(e)).r),o=r(t.g,e.g),i=r(t.b,e.b),a=Po(t.opacity,e.opacity);return function(e){return t.r=n(e),t.g=o(e),t.b=i(e),t.opacity=a(e),t+""}}return n.gamma=t,n}(1);function ko(t){return function(e){var r,n,o=e.length,i=new Array(o),a=new Array(o),c=new Array(o);for(r=0;r<o;++r)n=uo(e[r]),i[r]=n.r||0,a[r]=n.g||0,c[r]=n.b||0;return i=t(i),a=t(a),c=t(c),n.opacity=1,function(t){return n.r=i(t),n.g=a(t),n.b=c(t),n+""}}}ko((function(t){var e=t.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,e-1):Math.floor(r*e),o=t[n],i=t[n+1],a=n>0?t[n-1]:2*o-i,c=n<e-1?t[n+2]:2*i-o;return Oo((r-n/e)*e,a,o,i,c)}})),ko((function(t){var e=t.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*e),o=t[(n+e-1)%e],i=t[n%e],a=t[(n+1)%e],c=t[(n+2)%e];return Oo((r-n/e)*e,o,i,a,c)}}));function To(t,e){var r,n=e?e.length:0,o=t?Math.min(n,t.length):0,i=new Array(o),a=new Array(n);for(r=0;r<o;++r)i[r]=Bo(t[r],e[r]);for(;r<n;++r)a[r]=e[r];return function(t){for(r=0;r<o;++r)a[r]=i[r](t);return a}}function Mo(t,e){var r=new Date;return t=+t,e=+e,function(n){return r.setTime(t*(1-n)+e*n),r}}function _o(t,e){return t=+t,e=+e,function(r){return t*(1-r)+e*r}}function Co(t,e){var r,n={},o={};for(r in null!==t&&"object"==typeof t||(t={}),null!==e&&"object"==typeof e||(e={}),e)r in t?n[r]=Bo(t[r],e[r]):o[r]=e[r];return function(t){for(r in n)o[r]=n[r](t);return o}}var Do=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,Io=new RegExp(Do.source,"g");function No(t,e){var r,n,o,i=Do.lastIndex=Io.lastIndex=0,a=-1,c=[],u=[];for(t+="",e+="";(r=Do.exec(t))&&(n=Io.exec(e));)(o=n.index)>i&&(o=e.slice(i,o),c[a]?c[a]+=o:c[++a]=o),(r=r[0])===(n=n[0])?c[a]?c[a]+=n:c[++a]=n:(c[++a]=null,u.push({i:a,x:_o(r,n)})),i=Io.lastIndex;return i<e.length&&(o=e.slice(i),c[a]?c[a]+=o:c[++a]=o),c.length<2?u[0]?function(t){return function(e){return t(e)+""}}(u[0].x):function(t){return function(){return t}}(e):(e=u.length,function(t){for(var r,n=0;n<e;++n)c[(r=u[n]).i]=r.x(t);return c.join("")})}function Ro(t,e){e||(e=[]);var r,n=t?Math.min(e.length,t.length):0,o=e.slice();return function(i){for(r=0;r<n;++r)o[r]=t[r]*(1-i)+e[r]*i;return o}}function Bo(t,e){var r,n,o=typeof e;return null==e||"boolean"===o?jo(e):("number"===o?_o:"string"===o?(r=io(e))?(e=r,Eo):No:e instanceof io?Eo:e instanceof Date?Mo:(n=e,!ArrayBuffer.isView(n)||n instanceof DataView?Array.isArray(e)?To:"function"!=typeof e.valueOf&&"function"!=typeof e.toString||isNaN(e)?Co:_o:Ro))(t,e)}function Lo(t,e){return t=+t,e=+e,function(r){return Math.round(t*(1-r)+e*r)}}function zo(t){return+t}var Fo=[0,1];function Uo(t){return t}function Wo(t,e){return(e-=t=+t)?function(r){return(r-t)/e}:(r=isNaN(e)?NaN:.5,function(){return r});var r}function $o(t,e,r){var n=t[0],o=t[1],i=e[0],a=e[1];return o<n?(n=Wo(o,n),i=r(a,i)):(n=Wo(n,o),i=r(i,a)),function(t){return i(n(t))}}function Ko(t,e,r){var n=Math.min(t.length,e.length)-1,o=new Array(n),i=new Array(n),a=-1;for(t[n]<t[0]&&(t=t.slice().reverse(),e=e.slice().reverse());++a<n;)o[a]=Wo(t[a],t[a+1]),i[a]=r(e[a],e[a+1]);return function(e){var r=Fn(t,e,1,n)-1;return i[r](o[r](e))}}function qo(t,e){return e.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function Vo(){var t,e,r,n,o,i,a=Fo,c=Fo,u=Bo,l=Uo;function s(){var t=Math.min(a.length,c.length);return l!==Uo&&(l=function(t,e){var r;return t>e&&(r=t,t=e,e=r),function(r){return Math.max(t,Math.min(e,r))}}(a[0],a[t-1])),n=t>2?Ko:$o,o=i=null,f}function f(e){return null==e||isNaN(e=+e)?r:(o||(o=n(a.map(t),c,u)))(t(l(e)))}return f.invert=function(r){return l(e((i||(i=n(c,a.map(t),_o)))(r)))},f.domain=function(t){return arguments.length?(a=Array.from(t,zo),s()):a.slice()},f.range=function(t){return arguments.length?(c=Array.from(t),s()):c.slice()},f.rangeRound=function(t){return c=Array.from(t),u=Lo,s()},f.clamp=function(t){return arguments.length?(l=!!t||Uo,s()):l!==Uo},f.interpolate=function(t){return arguments.length?(u=t,s()):u},f.unknown=function(t){return arguments.length?(r=t,f):r},function(r,n){return t=r,e=n,s()}}function Xo(){return Vo()(Uo,Uo)}var Ho,Go=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function Yo(t){if(!(e=Go.exec(t)))throw new Error("invalid format: "+t);var e;return new Zo({fill:e[1],align:e[2],sign:e[3],symbol:e[4],zero:e[5],width:e[6],comma:e[7],precision:e[8]&&e[8].slice(1),trim:e[9],type:e[10]})}function Zo(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function Jo(t,e){if((r=(t=e?t.toExponential(e-1):t.toExponential()).indexOf("e"))<0)return null;var r,n=t.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+t.slice(r+1)]}function Qo(t){return(t=Jo(Math.abs(t)))?t[1]:NaN}function ti(t,e){var r=Jo(t,e);if(!r)return t+"";var n=r[0],o=r[1];return o<0?"0."+new Array(-o).join("0")+n:n.length>o+1?n.slice(0,o+1)+"."+n.slice(o+1):n+new Array(o-n.length+2).join("0")}Yo.prototype=Zo.prototype,Zo.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};const ei={"%":(t,e)=>(100*t).toFixed(e),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,e)=>t.toExponential(e),f:(t,e)=>t.toFixed(e),g:(t,e)=>t.toPrecision(e),o:t=>Math.round(t).toString(8),p:(t,e)=>ti(100*t,e),r:ti,s:function(t,e){var r=Jo(t,e);if(!r)return t+"";var n=r[0],o=r[1],i=o-(Ho=3*Math.max(-8,Math.min(8,Math.floor(o/3))))+1,a=n.length;return i===a?n:i>a?n+new Array(i-a+1).join("0"):i>0?n.slice(0,i)+"."+n.slice(i):"0."+new Array(1-i).join("0")+Jo(t,Math.max(0,e+i-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function ri(t){return t}var ni,oi,ii,ai=Array.prototype.map,ci=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function ui(t){var e,r,n=void 0===t.grouping||void 0===t.thousands?ri:(e=ai.call(t.grouping,Number),r=t.thousands+"",function(t,n){for(var o=t.length,i=[],a=0,c=e[0],u=0;o>0&&c>0&&(u+c+1>n&&(c=Math.max(1,n-u)),i.push(t.substring(o-=c,o+c)),!((u+=c+1)>n));)c=e[a=(a+1)%e.length];return i.reverse().join(r)}),o=void 0===t.currency?"":t.currency[0]+"",i=void 0===t.currency?"":t.currency[1]+"",a=void 0===t.decimal?".":t.decimal+"",c=void 0===t.numerals?ri:function(t){return function(e){return e.replace(/[0-9]/g,(function(e){return t[+e]}))}}(ai.call(t.numerals,String)),u=void 0===t.percent?"%":t.percent+"",l=void 0===t.minus?"−":t.minus+"",s=void 0===t.nan?"NaN":t.nan+"";function f(t){var e=(t=Yo(t)).fill,r=t.align,f=t.sign,p=t.symbol,h=t.zero,y=t.width,d=t.comma,v=t.precision,m=t.trim,b=t.type;"n"===b?(d=!0,b="g"):ei[b]||(void 0===v&&(v=12),m=!0,b="g"),(h||"0"===e&&"="===r)&&(h=!0,e="0",r="=");var g="$"===p?o:"#"===p&&/[boxX]/.test(b)?"0"+b.toLowerCase():"",x="$"===p?i:/[%p]/.test(b)?u:"",w=ei[b],O=/[defgprs%]/.test(b);function j(t){var o,i,u,p=g,j=x;if("c"===b)j=w(t)+j,t="";else{var S=(t=+t)<0||1/t<0;if(t=isNaN(t)?s:w(Math.abs(t),v),m&&(t=function(t){t:for(var e,r=t.length,n=1,o=-1;n<r;++n)switch(t[n]){case".":o=e=n;break;case"0":0===o&&(o=n),e=n;break;default:if(!+t[n])break t;o>0&&(o=0)}return o>0?t.slice(0,o)+t.slice(e+1):t}(t)),S&&0==+t&&"+"!==f&&(S=!1),p=(S?"("===f?f:l:"-"===f||"("===f?"":f)+p,j=("s"===b?ci[8+Ho/3]:"")+j+(S&&"("===f?")":""),O)for(o=-1,i=t.length;++o<i;)if(48>(u=t.charCodeAt(o))||u>57){j=(46===u?a+t.slice(o+1):t.slice(o))+j,t=t.slice(0,o);break}}d&&!h&&(t=n(t,1/0));var A=p.length+t.length+j.length,P=A<y?new Array(y-A+1).join(e):"";switch(d&&h&&(t=n(P+t,P.length?y-j.length:1/0),P=""),r){case"<":t=p+t+j+P;break;case"=":t=p+P+t+j;break;case"^":t=P.slice(0,A=P.length>>1)+p+t+j+P.slice(A);break;default:t=P+p+t+j}return c(t)}return v=void 0===v?6:/[gprs]/.test(b)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v)),j.toString=function(){return t+""},j}return{format:f,formatPrefix:function(t,e){var r=f(((t=Yo(t)).type="f",t)),n=3*Math.max(-8,Math.min(8,Math.floor(Qo(e)/3))),o=Math.pow(10,-n),i=ci[8+n/3];return function(t){return r(o*t)+i}}}}function li(t,e,r,n){var o,i=Cn(t,e,r);switch((n=Yo(null==n?",f":n)).type){case"s":var a=Math.max(Math.abs(t),Math.abs(e));return null!=n.precision||isNaN(o=function(t,e){return Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(Qo(e)/3)))-Qo(Math.abs(t)))}(i,a))||(n.precision=o),ii(n,a);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(o=function(t,e){return t=Math.abs(t),e=Math.abs(e)-t,Math.max(0,Qo(e)-Qo(t))+1}(i,Math.max(Math.abs(t),Math.abs(e))))||(n.precision=o-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(o=function(t){return Math.max(0,-Qo(Math.abs(t)))}(i))||(n.precision=o-2*("%"===n.type))}return oi(n)}function si(t){var e=t.domain;return t.ticks=function(t){var r=e();return Mn(r[0],r[r.length-1],null==t?10:t)},t.tickFormat=function(t,r){var n=e();return li(n[0],n[n.length-1],null==t?10:t,r)},t.nice=function(r){null==r&&(r=10);var n,o,i=e(),a=0,c=i.length-1,u=i[a],l=i[c],s=10;for(l<u&&(o=u,u=l,l=o,o=a,a=c,c=o);s-- >0;){if((o=_n(u,l,r))===n)return i[a]=u,i[c]=l,e(i);if(o>0)u=Math.floor(u/o)*o,l=Math.ceil(l/o)*o;else{if(!(o<0))break;u=Math.ceil(u*o)/o,l=Math.floor(l*o)/o}n=o}return t},t}function fi(){var t=Xo();return t.copy=function(){return qo(t,fi())},yn.apply(t,arguments),si(t)}function pi(t){var e;function r(t){return null==t||isNaN(t=+t)?e:t}return r.invert=r,r.domain=r.range=function(e){return arguments.length?(t=Array.from(e,zo),r):t.slice()},r.unknown=function(t){return arguments.length?(e=t,r):e},r.copy=function(){return pi(t).unknown(e)},t=arguments.length?Array.from(t,zo):[0,1],si(r)}function hi(t,e){var r,n=0,o=(t=t.slice()).length-1,i=t[n],a=t[o];return a<i&&(r=n,n=o,o=r,r=i,i=a,a=r),t[n]=e.floor(i),t[o]=e.ceil(a),t}function yi(t){return Math.log(t)}function di(t){return Math.exp(t)}function vi(t){return-Math.log(-t)}function mi(t){return-Math.exp(-t)}function bi(t){return isFinite(t)?+("1e"+t):t<0?0:t}function gi(t){return(e,r)=>-t(-e,r)}function xi(t){const e=t(yi,di),r=e.domain;let n,o,i=10;function a(){return n=function(t){return t===Math.E?Math.log:10===t&&Math.log10||2===t&&Math.log2||(t=Math.log(t),e=>Math.log(e)/t)}(i),o=function(t){return 10===t?bi:t===Math.E?Math.exp:e=>Math.pow(t,e)}(i),r()[0]<0?(n=gi(n),o=gi(o),t(vi,mi)):t(yi,di),e}return e.base=function(t){return arguments.length?(i=+t,a()):i},e.domain=function(t){return arguments.length?(r(t),a()):r()},e.ticks=t=>{const e=r();let a=e[0],c=e[e.length-1];const u=c<a;u&&([a,c]=[c,a]);let l,s,f=n(a),p=n(c);const h=null==t?10:+t;let y=[];if(!(i%1)&&p-f<h){if(f=Math.floor(f),p=Math.ceil(p),a>0){for(;f<=p;++f)for(l=1;l<i;++l)if(s=f<0?l/o(-f):l*o(f),!(s<a)){if(s>c)break;y.push(s)}}else for(;f<=p;++f)for(l=i-1;l>=1;--l)if(s=f>0?l/o(-f):l*o(f),!(s<a)){if(s>c)break;y.push(s)}2*y.length<h&&(y=Mn(a,c,h))}else y=Mn(f,p,Math.min(p-f,h)).map(o);return u?y.reverse():y},e.tickFormat=(t,r)=>{if(null==t&&(t=10),null==r&&(r=10===i?"s":","),"function"!=typeof r&&(i%1||null!=(r=Yo(r)).precision||(r.trim=!0),r=oi(r)),t===1/0)return r;const a=Math.max(1,i*t/e.ticks().length);return t=>{let e=t/o(Math.round(n(t)));return e*i<i-.5&&(e*=i),e<=a?r(t):""}},e.nice=()=>r(hi(r(),{floor:t=>o(Math.floor(n(t))),ceil:t=>o(Math.ceil(n(t)))})),e}function wi(){const t=xi(Vo()).domain([1,10]);return t.copy=()=>qo(t,wi()).base(t.base()),yn.apply(t,arguments),t}function Oi(t){return function(e){return Math.sign(e)*Math.log1p(Math.abs(e/t))}}function ji(t){return function(e){return Math.sign(e)*Math.expm1(Math.abs(e))*t}}function Si(t){var e=1,r=t(Oi(e),ji(e));return r.constant=function(r){return arguments.length?t(Oi(e=+r),ji(e)):e},si(r)}function Ai(){var t=Si(Vo());return t.copy=function(){return qo(t,Ai()).constant(t.constant())},yn.apply(t,arguments)}function Pi(t){return function(e){return e<0?-Math.pow(-e,t):Math.pow(e,t)}}function Ei(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function ki(t){return t<0?-t*t:t*t}function Ti(t){var e=t(Uo,Uo),r=1;return e.exponent=function(e){return arguments.length?1===(r=+e)?t(Uo,Uo):.5===r?t(Ei,ki):t(Pi(r),Pi(1/r)):r},si(e)}function Mi(){var t=Ti(Vo());return t.copy=function(){return qo(t,Mi()).exponent(t.exponent())},yn.apply(t,arguments),t}function _i(){return Mi.apply(null,arguments).exponent(.5)}function Ci(t){return Math.sign(t)*t*t}function Di(){var t,e=Xo(),r=[0,1],n=!1;function o(r){var o=function(t){return Math.sign(t)*Math.sqrt(Math.abs(t))}(e(r));return isNaN(o)?t:n?Math.round(o):o}return o.invert=function(t){return e.invert(Ci(t))},o.domain=function(t){return arguments.length?(e.domain(t),o):e.domain()},o.range=function(t){return arguments.length?(e.range((r=Array.from(t,zo)).map(Ci)),o):r.slice()},o.rangeRound=function(t){return o.range(t).round(!0)},o.round=function(t){return arguments.length?(n=!!t,o):n},o.clamp=function(t){return arguments.length?(e.clamp(t),o):e.clamp()},o.unknown=function(e){return arguments.length?(t=e,o):t},o.copy=function(){return Di(e.domain(),r).round(n).clamp(e.clamp()).unknown(t)},yn.apply(o,arguments),si(o)}function Ii(t,e){let r;if(void 0===e)for(const e of t)null!=e&&(r<e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let o of t)null!=(o=e(o,++n,t))&&(r<o||void 0===r&&o>=o)&&(r=o)}return r}function Ni(t,e){let r;if(void 0===e)for(const e of t)null!=e&&(r>e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let o of t)null!=(o=e(o,++n,t))&&(r>o||void 0===r&&o>=o)&&(r=o)}return r}function Ri(t=Dn){if(t===Dn)return Bi;if("function"!=typeof t)throw new TypeError("compare is not a function");return(e,r)=>{const n=t(e,r);return n||0===n?n:(0===t(r,r))-(0===t(e,e))}}function Bi(t,e){return(null==t||!(t>=t))-(null==e||!(e>=e))||(t<e?-1:t>e?1:0)}function Li(t,e,r=0,n=1/0,o){if(e=Math.floor(e),r=Math.floor(Math.max(0,r)),n=Math.floor(Math.min(t.length-1,n)),!(r<=e&&e<=n))return t;for(o=void 0===o?Bi:Ri(o);n>r;){if(n-r>600){const i=n-r+1,a=e-r+1,c=Math.log(i),u=.5*Math.exp(2*c/3),l=.5*Math.sqrt(c*u*(i-u)/i)*(a-i/2<0?-1:1);Li(t,e,Math.max(r,Math.floor(e-a*u/i+l)),Math.min(n,Math.floor(e+(i-a)*u/i+l)),o)}const i=t[e];let a=r,c=n;for(zi(t,r,e),o(t[n],i)>0&&zi(t,r,n);a<c;){for(zi(t,a,c),++a,--c;o(t[a],i)<0;)++a;for(;o(t[c],i)>0;)--c}0===o(t[r],i)?zi(t,r,c):(++c,zi(t,c,n)),c<=e&&(r=c+1),e<=c&&(n=c-1)}return t}function zi(t,e,r){const n=t[e];t[e]=t[r],t[r]=n}function Fi(t,e,r){if(t=Float64Array.from(function*(t,e){if(void 0===e)for(let e of t)null!=e&&(e=+e)>=e&&(yield e);else{let r=-1;for(let n of t)null!=(n=e(n,++r,t))&&(n=+n)>=n&&(yield n)}}(t,r)),(n=t.length)&&!isNaN(e=+e)){if(e<=0||n<2)return Ni(t);if(e>=1)return Ii(t);var n,o=(n-1)*e,i=Math.floor(o),a=Ii(Li(t,i).subarray(0,i+1));return a+(Ni(t.subarray(i+1))-a)*(o-i)}}function Ui(t,e,r=Bn){if((n=t.length)&&!isNaN(e=+e)){if(e<=0||n<2)return+r(t[0],0,t);if(e>=1)return+r(t[n-1],n-1,t);var n,o=(n-1)*e,i=Math.floor(o),a=+r(t[i],i,t);return a+(+r(t[i+1],i+1,t)-a)*(o-i)}}function Wi(){var t,e=[],r=[],n=[];function o(){var t=0,o=Math.max(1,r.length);for(n=new Array(o-1);++t<o;)n[t-1]=Ui(e,t/o);return i}function i(e){return null==e||isNaN(e=+e)?t:r[Fn(n,e)]}return i.invertExtent=function(t){var o=r.indexOf(t);return o<0?[NaN,NaN]:[o>0?n[o-1]:e[0],o<n.length?n[o]:e[e.length-1]]},i.domain=function(t){if(!arguments.length)return e.slice();e=[];for(let r of t)null==r||isNaN(r=+r)||e.push(r);return e.sort(Dn),o()},i.range=function(t){return arguments.length?(r=Array.from(t),o()):r.slice()},i.unknown=function(e){return arguments.length?(t=e,i):t},i.quantiles=function(){return n.slice()},i.copy=function(){return Wi().domain(e).range(r).unknown(t)},yn.apply(i,arguments)}function $i(){var t,e=0,r=1,n=1,o=[.5],i=[0,1];function a(e){return null!=e&&e<=e?i[Fn(o,e,0,n)]:t}function c(){var t=-1;for(o=new Array(n);++t<n;)o[t]=((t+1)*r-(t-n)*e)/(n+1);return a}return a.domain=function(t){return arguments.length?([e,r]=t,e=+e,r=+r,c()):[e,r]},a.range=function(t){return arguments.length?(n=(i=Array.from(t)).length-1,c()):i.slice()},a.invertExtent=function(t){var a=i.indexOf(t);return a<0?[NaN,NaN]:a<1?[e,o[0]]:a>=n?[o[n-1],r]:[o[a-1],o[a]]},a.unknown=function(e){return arguments.length?(t=e,a):a},a.thresholds=function(){return o.slice()},a.copy=function(){return $i().domain([e,r]).range(i).unknown(t)},yn.apply(si(a),arguments)}function Ki(){var t,e=[.5],r=[0,1],n=1;function o(o){return null!=o&&o<=o?r[Fn(e,o,0,n)]:t}return o.domain=function(t){return arguments.length?(e=Array.from(t),n=Math.min(e.length,r.length-1),o):e.slice()},o.range=function(t){return arguments.length?(r=Array.from(t),n=Math.min(e.length,r.length-1),o):r.slice()},o.invertExtent=function(t){var n=r.indexOf(t);return[e[n-1],e[n]]},o.unknown=function(e){return arguments.length?(t=e,o):t},o.copy=function(){return Ki().domain(e).range(r).unknown(t)},yn.apply(o,arguments)}ni=ui({thousands:",",grouping:[3],currency:["$",""]}),oi=ni.format,ii=ni.formatPrefix;const qi=1e3,Vi=6e4,Xi=36e5,Hi=864e5,Gi=6048e5,Yi=2592e6,Zi=31536e6,Ji=new Date,Qi=new Date;function ta(t,e,r,n){function o(e){return t(e=0===arguments.length?new Date:new Date(+e)),e}return o.floor=e=>(t(e=new Date(+e)),e),o.ceil=r=>(t(r=new Date(r-1)),e(r,1),t(r),r),o.round=t=>{const e=o(t),r=o.ceil(t);return t-e<r-t?e:r},o.offset=(t,r)=>(e(t=new Date(+t),null==r?1:Math.floor(r)),t),o.range=(r,n,i)=>{const a=[];if(r=o.ceil(r),i=null==i?1:Math.floor(i),!(r<n&&i>0))return a;let c;do{a.push(c=new Date(+r)),e(r,i),t(r)}while(c<r&&r<n);return a},o.filter=r=>ta((e=>{if(e>=e)for(;t(e),!r(e);)e.setTime(e-1)}),((t,n)=>{if(t>=t)if(n<0)for(;++n<=0;)for(;e(t,-1),!r(t););else for(;--n>=0;)for(;e(t,1),!r(t););})),r&&(o.count=(e,n)=>(Ji.setTime(+e),Qi.setTime(+n),t(Ji),t(Qi),Math.floor(r(Ji,Qi))),o.every=t=>(t=Math.floor(t),isFinite(t)&&t>0?t>1?o.filter(n?e=>n(e)%t==0:e=>o.count(0,e)%t==0):o:null)),o}const ea=ta((()=>{}),((t,e)=>{t.setTime(+t+e)}),((t,e)=>e-t));ea.every=t=>(t=Math.floor(t),isFinite(t)&&t>0?t>1?ta((e=>{e.setTime(Math.floor(e/t)*t)}),((e,r)=>{e.setTime(+e+r*t)}),((e,r)=>(r-e)/t)):ea:null);ea.range;const ra=ta((t=>{t.setTime(t-t.getMilliseconds())}),((t,e)=>{t.setTime(+t+e*qi)}),((t,e)=>(e-t)/qi),(t=>t.getUTCSeconds())),na=(ra.range,ta((t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*qi)}),((t,e)=>{t.setTime(+t+e*Vi)}),((t,e)=>(e-t)/Vi),(t=>t.getMinutes()))),oa=(na.range,ta((t=>{t.setUTCSeconds(0,0)}),((t,e)=>{t.setTime(+t+e*Vi)}),((t,e)=>(e-t)/Vi),(t=>t.getUTCMinutes()))),ia=(oa.range,ta((t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*qi-t.getMinutes()*Vi)}),((t,e)=>{t.setTime(+t+e*Xi)}),((t,e)=>(e-t)/Xi),(t=>t.getHours()))),aa=(ia.range,ta((t=>{t.setUTCMinutes(0,0,0)}),((t,e)=>{t.setTime(+t+e*Xi)}),((t,e)=>(e-t)/Xi),(t=>t.getUTCHours()))),ca=(aa.range,ta((t=>t.setHours(0,0,0,0)),((t,e)=>t.setDate(t.getDate()+e)),((t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*Vi)/Hi),(t=>t.getDate()-1))),ua=(ca.range,ta((t=>{t.setUTCHours(0,0,0,0)}),((t,e)=>{t.setUTCDate(t.getUTCDate()+e)}),((t,e)=>(e-t)/Hi),(t=>t.getUTCDate()-1))),la=(ua.range,ta((t=>{t.setUTCHours(0,0,0,0)}),((t,e)=>{t.setUTCDate(t.getUTCDate()+e)}),((t,e)=>(e-t)/Hi),(t=>Math.floor(t/Hi))));la.range;function sa(t){return ta((e=>{e.setDate(e.getDate()-(e.getDay()+7-t)%7),e.setHours(0,0,0,0)}),((t,e)=>{t.setDate(t.getDate()+7*e)}),((t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*Vi)/Gi))}const fa=sa(0),pa=sa(1),ha=sa(2),ya=sa(3),da=sa(4),va=sa(5),ma=sa(6);fa.range,pa.range,ha.range,ya.range,da.range,va.range,ma.range;function ba(t){return ta((e=>{e.setUTCDate(e.getUTCDate()-(e.getUTCDay()+7-t)%7),e.setUTCHours(0,0,0,0)}),((t,e)=>{t.setUTCDate(t.getUTCDate()+7*e)}),((t,e)=>(e-t)/Gi))}const ga=ba(0),xa=ba(1),wa=ba(2),Oa=ba(3),ja=ba(4),Sa=ba(5),Aa=ba(6),Pa=(ga.range,xa.range,wa.range,Oa.range,ja.range,Sa.range,Aa.range,ta((t=>{t.setDate(1),t.setHours(0,0,0,0)}),((t,e)=>{t.setMonth(t.getMonth()+e)}),((t,e)=>e.getMonth()-t.getMonth()+12*(e.getFullYear()-t.getFullYear())),(t=>t.getMonth()))),Ea=(Pa.range,ta((t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)}),((t,e)=>{t.setUTCMonth(t.getUTCMonth()+e)}),((t,e)=>e.getUTCMonth()-t.getUTCMonth()+12*(e.getUTCFullYear()-t.getUTCFullYear())),(t=>t.getUTCMonth()))),ka=(Ea.range,ta((t=>{t.setMonth(0,1),t.setHours(0,0,0,0)}),((t,e)=>{t.setFullYear(t.getFullYear()+e)}),((t,e)=>e.getFullYear()-t.getFullYear()),(t=>t.getFullYear())));ka.every=t=>isFinite(t=Math.floor(t))&&t>0?ta((e=>{e.setFullYear(Math.floor(e.getFullYear()/t)*t),e.setMonth(0,1),e.setHours(0,0,0,0)}),((e,r)=>{e.setFullYear(e.getFullYear()+r*t)})):null;ka.range;const Ta=ta((t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)}),((t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e)}),((t,e)=>e.getUTCFullYear()-t.getUTCFullYear()),(t=>t.getUTCFullYear()));Ta.every=t=>isFinite(t=Math.floor(t))&&t>0?ta((e=>{e.setUTCFullYear(Math.floor(e.getUTCFullYear()/t)*t),e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)}),((e,r)=>{e.setUTCFullYear(e.getUTCFullYear()+r*t)})):null;Ta.range;function Ma(t,e,r,n,o,i){const a=[[ra,1,qi],[ra,5,5e3],[ra,15,15e3],[ra,30,3e4],[i,1,Vi],[i,5,3e5],[i,15,9e5],[i,30,18e5],[o,1,Xi],[o,3,108e5],[o,6,216e5],[o,12,432e5],[n,1,Hi],[n,2,1728e5],[r,1,Gi],[e,1,Yi],[e,3,7776e6],[t,1,Zi]];function c(e,r,n){const o=Math.abs(r-e)/n,i=Nn((([,,t])=>t)).right(a,o);if(i===a.length)return t.every(Cn(e/Zi,r/Zi,n));if(0===i)return ea.every(Math.max(Cn(e,r,n),1));const[c,u]=a[o/a[i-1][2]<a[i][2]/o?i-1:i];return c.every(u)}return[function(t,e,r){const n=e<t;n&&([t,e]=[e,t]);const o=r&&"function"==typeof r.range?r:c(t,e,r),i=o?o.range(t,+e+1):[];return n?i.reverse():i},c]}const[_a,Ca]=Ma(Ta,Ea,ga,la,aa,oa),[Da,Ia]=Ma(ka,Pa,fa,ca,ia,na);function Na(t){if(0<=t.y&&t.y<100){var e=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return e.setFullYear(t.y),e}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function Ra(t){if(0<=t.y&&t.y<100){var e=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return e.setUTCFullYear(t.y),e}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function Ba(t,e,r){return{y:t,m:e,d:r,H:0,M:0,S:0,L:0}}var La,za,Fa,Ua={"-":"",_:" ",0:"0"},Wa=/^\s*\d+/,$a=/^%/,Ka=/[\\^$*+?|[\]().{}]/g;function qa(t,e,r){var n=t<0?"-":"",o=(n?-t:t)+"",i=o.length;return n+(i<r?new Array(r-i+1).join(e)+o:o)}function Va(t){return t.replace(Ka,"\\$&")}function Xa(t){return new RegExp("^(?:"+t.map(Va).join("|")+")","i")}function Ha(t){return new Map(t.map(((t,e)=>[t.toLowerCase(),e])))}function Ga(t,e,r){var n=Wa.exec(e.slice(r,r+1));return n?(t.w=+n[0],r+n[0].length):-1}function Ya(t,e,r){var n=Wa.exec(e.slice(r,r+1));return n?(t.u=+n[0],r+n[0].length):-1}function Za(t,e,r){var n=Wa.exec(e.slice(r,r+2));return n?(t.U=+n[0],r+n[0].length):-1}function Ja(t,e,r){var n=Wa.exec(e.slice(r,r+2));return n?(t.V=+n[0],r+n[0].length):-1}function Qa(t,e,r){var n=Wa.exec(e.slice(r,r+2));return n?(t.W=+n[0],r+n[0].length):-1}function tc(t,e,r){var n=Wa.exec(e.slice(r,r+4));return n?(t.y=+n[0],r+n[0].length):-1}function ec(t,e,r){var n=Wa.exec(e.slice(r,r+2));return n?(t.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function rc(t,e,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(e.slice(r,r+6));return n?(t.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function nc(t,e,r){var n=Wa.exec(e.slice(r,r+1));return n?(t.q=3*n[0]-3,r+n[0].length):-1}function oc(t,e,r){var n=Wa.exec(e.slice(r,r+2));return n?(t.m=n[0]-1,r+n[0].length):-1}function ic(t,e,r){var n=Wa.exec(e.slice(r,r+2));return n?(t.d=+n[0],r+n[0].length):-1}function ac(t,e,r){var n=Wa.exec(e.slice(r,r+3));return n?(t.m=0,t.d=+n[0],r+n[0].length):-1}function cc(t,e,r){var n=Wa.exec(e.slice(r,r+2));return n?(t.H=+n[0],r+n[0].length):-1}function uc(t,e,r){var n=Wa.exec(e.slice(r,r+2));return n?(t.M=+n[0],r+n[0].length):-1}function lc(t,e,r){var n=Wa.exec(e.slice(r,r+2));return n?(t.S=+n[0],r+n[0].length):-1}function sc(t,e,r){var n=Wa.exec(e.slice(r,r+3));return n?(t.L=+n[0],r+n[0].length):-1}function fc(t,e,r){var n=Wa.exec(e.slice(r,r+6));return n?(t.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function pc(t,e,r){var n=$a.exec(e.slice(r,r+1));return n?r+n[0].length:-1}function hc(t,e,r){var n=Wa.exec(e.slice(r));return n?(t.Q=+n[0],r+n[0].length):-1}function yc(t,e,r){var n=Wa.exec(e.slice(r));return n?(t.s=+n[0],r+n[0].length):-1}function dc(t,e){return qa(t.getDate(),e,2)}function vc(t,e){return qa(t.getHours(),e,2)}function mc(t,e){return qa(t.getHours()%12||12,e,2)}function bc(t,e){return qa(1+ca.count(ka(t),t),e,3)}function gc(t,e){return qa(t.getMilliseconds(),e,3)}function xc(t,e){return gc(t,e)+"000"}function wc(t,e){return qa(t.getMonth()+1,e,2)}function Oc(t,e){return qa(t.getMinutes(),e,2)}function jc(t,e){return qa(t.getSeconds(),e,2)}function Sc(t){var e=t.getDay();return 0===e?7:e}function Ac(t,e){return qa(fa.count(ka(t)-1,t),e,2)}function Pc(t){var e=t.getDay();return e>=4||0===e?da(t):da.ceil(t)}function Ec(t,e){return t=Pc(t),qa(da.count(ka(t),t)+(4===ka(t).getDay()),e,2)}function kc(t){return t.getDay()}function Tc(t,e){return qa(pa.count(ka(t)-1,t),e,2)}function Mc(t,e){return qa(t.getFullYear()%100,e,2)}function _c(t,e){return qa((t=Pc(t)).getFullYear()%100,e,2)}function Cc(t,e){return qa(t.getFullYear()%1e4,e,4)}function Dc(t,e){var r=t.getDay();return qa((t=r>=4||0===r?da(t):da.ceil(t)).getFullYear()%1e4,e,4)}function Ic(t){var e=t.getTimezoneOffset();return(e>0?"-":(e*=-1,"+"))+qa(e/60|0,"0",2)+qa(e%60,"0",2)}function Nc(t,e){return qa(t.getUTCDate(),e,2)}function Rc(t,e){return qa(t.getUTCHours(),e,2)}function Bc(t,e){return qa(t.getUTCHours()%12||12,e,2)}function Lc(t,e){return qa(1+ua.count(Ta(t),t),e,3)}function zc(t,e){return qa(t.getUTCMilliseconds(),e,3)}function Fc(t,e){return zc(t,e)+"000"}function Uc(t,e){return qa(t.getUTCMonth()+1,e,2)}function Wc(t,e){return qa(t.getUTCMinutes(),e,2)}function $c(t,e){return qa(t.getUTCSeconds(),e,2)}function Kc(t){var e=t.getUTCDay();return 0===e?7:e}function qc(t,e){return qa(ga.count(Ta(t)-1,t),e,2)}function Vc(t){var e=t.getUTCDay();return e>=4||0===e?ja(t):ja.ceil(t)}function Xc(t,e){return t=Vc(t),qa(ja.count(Ta(t),t)+(4===Ta(t).getUTCDay()),e,2)}function Hc(t){return t.getUTCDay()}function Gc(t,e){return qa(xa.count(Ta(t)-1,t),e,2)}function Yc(t,e){return qa(t.getUTCFullYear()%100,e,2)}function Zc(t,e){return qa((t=Vc(t)).getUTCFullYear()%100,e,2)}function Jc(t,e){return qa(t.getUTCFullYear()%1e4,e,4)}function Qc(t,e){var r=t.getUTCDay();return qa((t=r>=4||0===r?ja(t):ja.ceil(t)).getUTCFullYear()%1e4,e,4)}function tu(){return"+0000"}function eu(){return"%"}function ru(t){return+t}function nu(t){return Math.floor(+t/1e3)}function ou(t){return new Date(t)}function iu(t){return t instanceof Date?+t:+new Date(+t)}function au(t,e,r,n,o,i,a,c,u,l){var s=Xo(),f=s.invert,p=s.domain,h=l(".%L"),y=l(":%S"),d=l("%I:%M"),v=l("%I %p"),m=l("%a %d"),b=l("%b %d"),g=l("%B"),x=l("%Y");function w(t){return(u(t)<t?h:c(t)<t?y:a(t)<t?d:i(t)<t?v:n(t)<t?o(t)<t?m:b:r(t)<t?g:x)(t)}return s.invert=function(t){return new Date(f(t))},s.domain=function(t){return arguments.length?p(Array.from(t,iu)):p().map(ou)},s.ticks=function(e){var r=p();return t(r[0],r[r.length-1],null==e?10:e)},s.tickFormat=function(t,e){return null==e?w:l(e)},s.nice=function(t){var r=p();return t&&"function"==typeof t.range||(t=e(r[0],r[r.length-1],null==t?10:t)),t?p(hi(r,t)):s},s.copy=function(){return qo(s,au(t,e,r,n,o,i,a,c,u,l))},s}function cu(){return yn.apply(au(Da,Ia,ka,Pa,fa,ca,ia,na,ra,za).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function uu(){return yn.apply(au(_a,Ca,Ta,Ea,ga,ua,aa,oa,ra,Fa).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function lu(){var t,e,r,n,o,i=0,a=1,c=Uo,u=!1;function l(e){return null==e||isNaN(e=+e)?o:c(0===r?.5:(e=(n(e)-t)*r,u?Math.max(0,Math.min(1,e)):e))}function s(t){return function(e){var r,n;return arguments.length?([r,n]=e,c=t(r,n),l):[c(0),c(1)]}}return l.domain=function(o){return arguments.length?([i,a]=o,t=n(i=+i),e=n(a=+a),r=t===e?0:1/(e-t),l):[i,a]},l.clamp=function(t){return arguments.length?(u=!!t,l):u},l.interpolator=function(t){return arguments.length?(c=t,l):c},l.range=s(Bo),l.rangeRound=s(Lo),l.unknown=function(t){return arguments.length?(o=t,l):o},function(o){return n=o,t=o(i),e=o(a),r=t===e?0:1/(e-t),l}}function su(t,e){return e.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function fu(){var t=si(lu()(Uo));return t.copy=function(){return su(t,fu())},dn.apply(t,arguments)}function pu(){var t=xi(lu()).domain([1,10]);return t.copy=function(){return su(t,pu()).base(t.base())},dn.apply(t,arguments)}function hu(){var t=Si(lu());return t.copy=function(){return su(t,hu()).constant(t.constant())},dn.apply(t,arguments)}function yu(){var t=Ti(lu());return t.copy=function(){return su(t,yu()).exponent(t.exponent())},dn.apply(t,arguments)}function du(){return yu.apply(null,arguments).exponent(.5)}function vu(){var t=[],e=Uo;function r(r){if(null!=r&&!isNaN(r=+r))return e((Fn(t,r,1)-1)/(t.length-1))}return r.domain=function(e){if(!arguments.length)return t.slice();t=[];for(let r of e)null==r||isNaN(r=+r)||t.push(r);return t.sort(Dn),r},r.interpolator=function(t){return arguments.length?(e=t,r):e},r.range=function(){return t.map(((r,n)=>e(n/(t.length-1))))},r.quantiles=function(e){return Array.from({length:e+1},((r,n)=>Fi(t,n/e)))},r.copy=function(){return vu(e).domain(t)},dn.apply(r,arguments)}function mu(){var t,e,r,n,o,i,a,c=0,u=.5,l=1,s=1,f=Uo,p=!1;function h(t){return isNaN(t=+t)?a:(t=.5+((t=+i(t))-e)*(s*t<s*e?n:o),f(p?Math.max(0,Math.min(1,t)):t))}function y(t){return function(e){var r,n,o;return arguments.length?([r,n,o]=e,f=function(t,e){void 0===e&&(e=t,t=Bo);for(var r=0,n=e.length-1,o=e[0],i=new Array(n<0?0:n);r<n;)i[r]=t(o,o=e[++r]);return function(t){var e=Math.max(0,Math.min(n-1,Math.floor(t*=n)));return i[e](t-e)}}(t,[r,n,o]),h):[f(0),f(.5),f(1)]}}return h.domain=function(a){return arguments.length?([c,u,l]=a,t=i(c=+c),e=i(u=+u),r=i(l=+l),n=t===e?0:.5/(e-t),o=e===r?0:.5/(r-e),s=e<t?-1:1,h):[c,u,l]},h.clamp=function(t){return arguments.length?(p=!!t,h):p},h.interpolator=function(t){return arguments.length?(f=t,h):f},h.range=y(Bo),h.rangeRound=y(Lo),h.unknown=function(t){return arguments.length?(a=t,h):a},function(a){return i=a,t=a(c),e=a(u),r=a(l),n=t===e?0:.5/(e-t),o=e===r?0:.5/(r-e),s=e<t?-1:1,h}}function bu(){var t=si(mu()(Uo));return t.copy=function(){return su(t,bu())},dn.apply(t,arguments)}function gu(){var t=xi(mu()).domain([.1,1,10]);return t.copy=function(){return su(t,gu()).base(t.base())},dn.apply(t,arguments)}function xu(){var t=Si(mu());return t.copy=function(){return su(t,xu()).constant(t.constant())},dn.apply(t,arguments)}function wu(){var t=Ti(mu());return t.copy=function(){return su(t,wu()).exponent(t.exponent())},dn.apply(t,arguments)}function Ou(){return wu.apply(null,arguments).exponent(.5)}function ju(t,e){if((o=t.length)>1)for(var r,n,o,i=1,a=t[e[0]],c=a.length;i<o;++i)for(n=a,a=t[e[i]],r=0;r<c;++r)a[r][1]+=a[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}!function(t){La=function(t){var e=t.dateTime,r=t.date,n=t.time,o=t.periods,i=t.days,a=t.shortDays,c=t.months,u=t.shortMonths,l=Xa(o),s=Ha(o),f=Xa(i),p=Ha(i),h=Xa(a),y=Ha(a),d=Xa(c),v=Ha(c),m=Xa(u),b=Ha(u),g={a:function(t){return a[t.getDay()]},A:function(t){return i[t.getDay()]},b:function(t){return u[t.getMonth()]},B:function(t){return c[t.getMonth()]},c:null,d:dc,e:dc,f:xc,g:_c,G:Dc,H:vc,I:mc,j:bc,L:gc,m:wc,M:Oc,p:function(t){return o[+(t.getHours()>=12)]},q:function(t){return 1+~~(t.getMonth()/3)},Q:ru,s:nu,S:jc,u:Sc,U:Ac,V:Ec,w:kc,W:Tc,x:null,X:null,y:Mc,Y:Cc,Z:Ic,"%":eu},x={a:function(t){return a[t.getUTCDay()]},A:function(t){return i[t.getUTCDay()]},b:function(t){return u[t.getUTCMonth()]},B:function(t){return c[t.getUTCMonth()]},c:null,d:Nc,e:Nc,f:Fc,g:Zc,G:Qc,H:Rc,I:Bc,j:Lc,L:zc,m:Uc,M:Wc,p:function(t){return o[+(t.getUTCHours()>=12)]},q:function(t){return 1+~~(t.getUTCMonth()/3)},Q:ru,s:nu,S:$c,u:Kc,U:qc,V:Xc,w:Hc,W:Gc,x:null,X:null,y:Yc,Y:Jc,Z:tu,"%":eu},w={a:function(t,e,r){var n=h.exec(e.slice(r));return n?(t.w=y.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(t,e,r){var n=f.exec(e.slice(r));return n?(t.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(t,e,r){var n=m.exec(e.slice(r));return n?(t.m=b.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(t,e,r){var n=d.exec(e.slice(r));return n?(t.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(t,r,n){return S(t,e,r,n)},d:ic,e:ic,f:fc,g:ec,G:tc,H:cc,I:cc,j:ac,L:sc,m:oc,M:uc,p:function(t,e,r){var n=l.exec(e.slice(r));return n?(t.p=s.get(n[0].toLowerCase()),r+n[0].length):-1},q:nc,Q:hc,s:yc,S:lc,u:Ya,U:Za,V:Ja,w:Ga,W:Qa,x:function(t,e,n){return S(t,r,e,n)},X:function(t,e,r){return S(t,n,e,r)},y:ec,Y:tc,Z:rc,"%":pc};function O(t,e){return function(r){var n,o,i,a=[],c=-1,u=0,l=t.length;for(r instanceof Date||(r=new Date(+r));++c<l;)37===t.charCodeAt(c)&&(a.push(t.slice(u,c)),null!=(o=Ua[n=t.charAt(++c)])?n=t.charAt(++c):o="e"===n?" ":"0",(i=e[n])&&(n=i(r,o)),a.push(n),u=c+1);return a.push(t.slice(u,c)),a.join("")}}function j(t,e){return function(r){var n,o,i=Ba(1900,void 0,1);if(S(i,t,r+="",0)!=r.length)return null;if("Q"in i)return new Date(i.Q);if("s"in i)return new Date(1e3*i.s+("L"in i?i.L:0));if(e&&!("Z"in i)&&(i.Z=0),"p"in i&&(i.H=i.H%12+12*i.p),void 0===i.m&&(i.m="q"in i?i.q:0),"V"in i){if(i.V<1||i.V>53)return null;"w"in i||(i.w=1),"Z"in i?(o=(n=Ra(Ba(i.y,0,1))).getUTCDay(),n=o>4||0===o?xa.ceil(n):xa(n),n=ua.offset(n,7*(i.V-1)),i.y=n.getUTCFullYear(),i.m=n.getUTCMonth(),i.d=n.getUTCDate()+(i.w+6)%7):(o=(n=Na(Ba(i.y,0,1))).getDay(),n=o>4||0===o?pa.ceil(n):pa(n),n=ca.offset(n,7*(i.V-1)),i.y=n.getFullYear(),i.m=n.getMonth(),i.d=n.getDate()+(i.w+6)%7)}else("W"in i||"U"in i)&&("w"in i||(i.w="u"in i?i.u%7:"W"in i?1:0),o="Z"in i?Ra(Ba(i.y,0,1)).getUTCDay():Na(Ba(i.y,0,1)).getDay(),i.m=0,i.d="W"in i?(i.w+6)%7+7*i.W-(o+5)%7:i.w+7*i.U-(o+6)%7);return"Z"in i?(i.H+=i.Z/100|0,i.M+=i.Z%100,Ra(i)):Na(i)}}function S(t,e,r,n){for(var o,i,a=0,c=e.length,u=r.length;a<c;){if(n>=u)return-1;if(37===(o=e.charCodeAt(a++))){if(o=e.charAt(a++),!(i=w[o in Ua?e.charAt(a++):o])||(n=i(t,r,n))<0)return-1}else if(o!=r.charCodeAt(n++))return-1}return n}return g.x=O(r,g),g.X=O(n,g),g.c=O(e,g),x.x=O(r,x),x.X=O(n,x),x.c=O(e,x),{format:function(t){var e=O(t+="",g);return e.toString=function(){return t},e},parse:function(t){var e=j(t+="",!1);return e.toString=function(){return t},e},utcFormat:function(t){var e=O(t+="",x);return e.toString=function(){return t},e},utcParse:function(t){var e=j(t+="",!0);return e.toString=function(){return t},e}}}(t),za=La.format,La.parse,Fa=La.utcFormat,La.utcParse}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});Array.prototype.slice;function Su(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}function Au(t){for(var e=t.length,r=new Array(e);--e>=0;)r[e]=e;return r}function Pu(t,e){return t[e]}function Eu(t){const e=[];return e.key=t,e}var ku=o(4464),Tu=o.n(ku),Mu=o(4044),_u=o.n(Mu),Cu=o(7440),Du=o.n(Cu),Iu=o(948),Nu=o.n(Iu),Ru=o(7996),Bu=o.n(Ru);function Lu(t){return function(t){if(Array.isArray(t))return zu(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return zu(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return zu(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function zu(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Fu=function(t){return t},Uu={"@@functional/placeholder":!0},Wu=function(t){return t===Uu},$u=function(t){return function e(){return 0===arguments.length||1===arguments.length&&Wu(arguments.length<=0?void 0:arguments[0])?e:t.apply(void 0,arguments)}},Ku=function t(e,r){return 1===e?r:$u((function(){for(var n=arguments.length,o=new Array(n),i=0;i<n;i++)o[i]=arguments[i];var a=o.filter((function(t){return t!==Uu})).length;return a>=e?r.apply(void 0,o):t(e-a,$u((function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];var i=o.map((function(t){return Wu(t)?e.shift():t}));return r.apply(void 0,Lu(i).concat(e))})))}))},qu=function(t){return Ku(t.length,t)},Vu=function(t,e){for(var r=[],n=t;n<e;++n)r[n-t]=n;return r},Xu=qu((function(t,e){return Array.isArray(e)?e.map(t):Object.keys(e).map((function(t){return e[t]})).map(t)})),Hu=function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];if(!e.length)return Fu;var n=e.reverse(),o=n[0],i=n.slice(1);return function(){return i.reduce((function(t,e){return e(t)}),o.apply(void 0,arguments))}},Gu=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},Yu=function(t){var e=null,r=null;return function(){for(var n=arguments.length,o=new Array(n),i=0;i<n;i++)o[i]=arguments[i];return e&&o.every((function(t,r){return t===e[r]}))?r:(e=o,r=t.apply(void 0,o))}};var Zu=qu((function(t,e,r){var n=+t;return n+r*(+e-n)})),Ju=qu((function(t,e,r){var n=e-+t;return(r-t)/(n=n||1/0)})),Qu=qu((function(t,e,r){var n=e-+t;return n=n||1/0,Math.max(0,Math.min(1,(r-t)/n))}));const tl={rangeStep:function(t,e,r){for(var n=new(Bu())(t),o=0,i=[];n.lt(e)&&o<1e5;)i.push(n.toNumber()),n=n.add(r),o++;return i},getDigitCount:function(t){return 0===t?1:Math.floor(new(Bu())(t).abs().log(10).toNumber())+1},interpolateNumber:Zu,uninterpolateNumber:Ju,uninterpolateTruncation:Qu};function el(t){return function(t){if(Array.isArray(t))return ol(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||nl(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function rl(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if("undefined"==typeof Symbol||!(Symbol.iterator in Object(t)))return;var r=[],n=!0,o=!1,i=void 0;try{for(var a,c=t[Symbol.iterator]();!(n=(a=c.next()).done)&&(r.push(a.value),!e||r.length!==e);n=!0);}catch(t){o=!0,i=t}finally{try{n||null==c.return||c.return()}finally{if(o)throw i}}return r}(t,e)||nl(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nl(t,e){if(t){if("string"==typeof t)return ol(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?ol(t,e):void 0}}function ol(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function il(t){var e=rl(t,2),r=e[0],n=e[1],o=r,i=n;return r>n&&(o=n,i=r),[o,i]}function al(t,e,r){if(t.lte(0))return new(Bu())(0);var n=tl.getDigitCount(t.toNumber()),o=new(Bu())(10).pow(n),i=t.div(o),a=1!==n?.05:.1,c=new(Bu())(Math.ceil(i.div(a).toNumber())).add(r).mul(a).mul(o);return e?c:new(Bu())(Math.ceil(c))}function cl(t,e,r){var n=1,o=new(Bu())(t);if(!o.isint()&&r){var i=Math.abs(t);i<1?(n=new(Bu())(10).pow(tl.getDigitCount(t)-1),o=new(Bu())(Math.floor(o.div(n).toNumber())).mul(n)):i>1&&(o=new(Bu())(Math.floor(t)))}else 0===t?o=new(Bu())(Math.floor((e-1)/2)):r||(o=new(Bu())(Math.floor(t)));var a=Math.floor((e-1)/2);return Hu(Xu((function(t){return o.add(new(Bu())(t-a).mul(n)).toNumber()})),Vu)(0,e)}function ul(t,e,r,n){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((e-t)/(r-1)))return{step:new(Bu())(0),tickMin:new(Bu())(0),tickMax:new(Bu())(0)};var i,a=al(new(Bu())(e).sub(t).div(r-1),n,o);i=t<=0&&e>=0?new(Bu())(0):(i=new(Bu())(t).add(e).div(2)).sub(new(Bu())(i).mod(a));var c=Math.ceil(i.sub(t).div(a).toNumber()),u=Math.ceil(new(Bu())(e).sub(i).div(a).toNumber()),l=c+u+1;return l>r?ul(t,e,r,n,o+1):(l<r&&(u=e>0?u+(r-l):u,c=e>0?c:c+(r-l)),{step:a,tickMin:i.sub(new(Bu())(c).mul(a)),tickMax:i.add(new(Bu())(u).mul(a))})}var ll=Yu((function(t){var e=rl(t,2),r=e[0],n=e[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],a=Math.max(o,2),c=rl(il([r,n]),2),u=c[0],l=c[1];if(u===-1/0||l===1/0){var s=l===1/0?[u].concat(el(Vu(0,o-1).map((function(){return 1/0})))):[].concat(el(Vu(0,o-1).map((function(){return-1/0}))),[l]);return r>n?Gu(s):s}if(u===l)return cl(u,o,i);var f=ul(u,l,a,i),p=f.step,h=f.tickMin,y=f.tickMax,d=tl.rangeStep(h,y.add(new(Bu())(.1).mul(p)),p);return r>n?Gu(d):d})),sl=(Yu((function(t){var e=rl(t,2),r=e[0],n=e[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],a=Math.max(o,2),c=rl(il([r,n]),2),u=c[0],l=c[1];if(u===-1/0||l===1/0)return[r,n];if(u===l)return cl(u,o,i);var s=al(new(Bu())(l).sub(u).div(a-1),i,0),f=Hu(Xu((function(t){return new(Bu())(u).add(new(Bu())(t).mul(s)).toNumber()})),Vu)(0,a).filter((function(t){return t>=u&&t<=l}));return r>n?Gu(f):f})),Yu((function(t,e){var r=rl(t,2),n=r[0],o=r[1],i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],a=rl(il([n,o]),2),c=a[0],u=a[1];if(c===-1/0||u===1/0)return[n,o];if(c===u)return[c];var l=Math.max(e,2),s=al(new(Bu())(u).sub(c).div(l-1),i,0),f=[].concat(el(tl.rangeStep(new(Bu())(c),new(Bu())(u).sub(new(Bu())(.99).mul(s)),s)),[u]);return n>o?Gu(f):f}))),fl="Invariant failed";function pl(t,e){if(!t)throw new Error(fl)}var hl=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function yl(t){return yl="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},yl(t)}function dl(){return dl=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},dl.apply(this,arguments)}function vl(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return ml(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ml(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ml(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function bl(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function gl(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Al(n.key),n)}}function xl(t,e,r){return e=Ol(e),function(t,e){if(e&&("object"===yl(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,wl()?Reflect.construct(e,r||[],Ol(t).constructor):e.apply(t,r))}function wl(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(wl=function(){return!!t})()}function Ol(t){return Ol=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Ol(t)}function jl(t,e){return jl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},jl(t,e)}function Sl(t,e,r){return(e=Al(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Al(t){var e=function(t,e){if("object"!=yl(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=yl(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==yl(e)?e:e+""}var Pl=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),xl(this,e,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&jl(t,e)}(e,t),n=e,o=[{key:"render",value:function(){var t=this.props,e=t.offset,n=t.layout,o=t.width,i=t.dataKey,a=t.data,c=t.dataPointFormatter,u=t.xAxis,l=t.yAxis,s=bl(t,hl),f=nt(s,!1);"x"===this.props.direction&&"number"!==u.type&&pl(!1);var p=a.map((function(t){var a=c(t,i),s=a.x,p=a.y,h=a.value,y=a.errorVal;if(!y)return null;var d,v,m=[];if(Array.isArray(y)){var b=vl(y,2);d=b[0],v=b[1]}else d=v=y;if("vertical"===n){var g=u.scale,x=p+e,w=x+o,O=x-o,j=g(h-d),S=g(h+v);m.push({x1:S,y1:w,x2:S,y2:O}),m.push({x1:j,y1:x,x2:S,y2:x}),m.push({x1:j,y1:w,x2:j,y2:O})}else if("horizontal"===n){var A=l.scale,P=s+e,E=P-o,k=P+o,T=A(h-d),M=A(h+v);m.push({x1:E,y1:M,x2:k,y2:M}),m.push({x1:P,y1:T,x2:P,y2:M}),m.push({x1:E,y1:T,x2:k,y2:T})}return r().createElement(yt,dl({className:"recharts-errorBar",key:"bar-".concat(m.map((function(t){return"".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)})))},f),m.map((function(t){return r().createElement("line",dl({},t,{key:"line-".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))})))}));return r().createElement(yt,{className:"recharts-errorBars"},p)}}],o&&gl(n.prototype,o),i&&gl(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,o,i}(r().Component);function El(t){return El="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},El(t)}function kl(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Tl(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?kl(Object(r),!0).forEach((function(e){Ml(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):kl(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Ml(t,e,r){var n;return n=function(t,e){if("object"!=El(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=El(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==El(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}Sl(Pl,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"}),Sl(Pl,"displayName","ErrorBar");var _l=function(t){var e=t.children,r=t.formattedGraphicalItems,n=t.legendWidth,o=t.legendContent,i=Z(e,Ce);if(!i)return null;var a,c=Ce.defaultProps,u=void 0!==c?Tl(Tl({},c),i.props):{};return a=i.props&&i.props.payload?i.props&&i.props.payload:"children"===o?(r||[]).reduce((function(t,e){var r=e.item,n=e.props,o=n.sectors||n.data||[];return t.concat(o.map((function(t){return{type:i.props.iconType||r.props.legendType,value:t.name,color:t.fill,payload:t}})))}),[]):(r||[]).map((function(t){var e=t.item,r=e.type.defaultProps,n=void 0!==r?Tl(Tl({},r),e.props):{},o=n.dataKey,i=n.name,a=n.legendType;return{inactive:n.hide,dataKey:o,type:u.iconType||a||"square",color:Fl(e),value:i||o,payload:n}})),Tl(Tl(Tl({},u),Ce.getWithHeight(i,n)),{},{payload:a,item:i})};function Cl(t){return Cl="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Cl(t)}function Dl(t){return function(t){if(Array.isArray(t))return Il(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return Il(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Il(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Il(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Nl(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Rl(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Nl(Object(r),!0).forEach((function(e){Bl(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Nl(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Bl(t,e,r){var n;return n=function(t,e){if("object"!=Cl(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Cl(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==Cl(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Ll(t,e,r){return s()(t)||s()(e)?r:A(e)?u()(t,e,r):y()(e)?e(t):r}function zl(t,e,r,n){var o=Du()(t,(function(t){return Ll(t,e)}));if("number"===r){var i=o.filter((function(t){return S(t)||parseFloat(t)}));return i.length?[_u()(i),Tu()(i)]:[1/0,-1/0]}return(n?o.filter((function(t){return!s()(t)})):o).map((function(t){return A(t)||t instanceof Date?t:""}))}var Fl=function(t){var e,r,n=t.type.displayName,o=null!==(e=t.type)&&void 0!==e&&e.defaultProps?Rl(Rl({},t.type.defaultProps),t.props):t.props,i=o.stroke,a=o.fill;switch(n){case"Line":r=i;break;case"Area":case"Radar":r=i&&"none"!==i?i:a;break;default:r=a}return r},Ul=function(t,e,r,n,o){var i=Y(e.props.children,Pl).filter((function(t){return function(t,e,r){return!!s()(e)||("horizontal"===t?"yAxis"===e:"vertical"===t||"x"===r?"xAxis"===e:"y"!==r||"yAxis"===e)}(n,o,t.props.direction)}));if(i&&i.length){var a=i.map((function(t){return t.props.dataKey}));return t.reduce((function(t,e){var n=Ll(e,r);if(s()(n))return t;var o=Array.isArray(n)?[_u()(n),Tu()(n)]:[n,n],i=a.reduce((function(t,r){var n=Ll(e,r,0),i=o[0]-Math.abs(Array.isArray(n)?n[0]:n),a=o[1]+Math.abs(Array.isArray(n)?n[1]:n);return[Math.min(i,t[0]),Math.max(a,t[1])]}),[1/0,-1/0]);return[Math.min(i[0],t[0]),Math.max(i[1],t[1])]}),[1/0,-1/0])}return null},Wl=function(t,e,r,n,o){var i=e.map((function(e){var i=e.props.dataKey;return"number"===r&&i&&Ul(t,e,i,n)||zl(t,i,r,o)}));if("number"===r)return i.reduce((function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]}),[1/0,-1/0]);var a={};return i.reduce((function(t,e){for(var r=0,n=e.length;r<n;r++)a[e[r]]||(a[e[r]]=!0,t.push(e[r]));return t}),[])},$l=function(t,e){return"horizontal"===t&&"xAxis"===e||"vertical"===t&&"yAxis"===e||"centric"===t&&"angleAxis"===e||"radial"===t&&"radiusAxis"===e},Kl=function(t,e,r,n){if(n)return t.map((function(t){return t.coordinate}));var o,i,a=t.map((function(t){return t.coordinate===e&&(o=!0),t.coordinate===r&&(i=!0),t.coordinate}));return o||a.push(e),i||a.push(r),a},ql=function(t,e,r){if(!t)return null;var n=t.scale,o=t.duplicateDomain,i=t.type,a=t.range,c="scaleBand"===t.realScaleType?n.bandwidth()/2:2,u=(e||r)&&"category"===i&&n.bandwidth?n.bandwidth()/c:0;return u="angleAxis"===t.axisType&&(null==a?void 0:a.length)>=2?2*O(a[0]-a[1])*u:u,e&&(t.ticks||t.niceTicks)?(t.ticks||t.niceTicks).map((function(t){var e=o?o.indexOf(t):t;return{coordinate:n(e)+u,value:t,offset:u}})).filter((function(t){return!g()(t.coordinate)})):t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map((function(t,e){return{coordinate:n(t)+u,value:t,index:e,offset:u}})):n.ticks&&!r?n.ticks(t.tickCount).map((function(t){return{coordinate:n(t)+u,value:t,offset:u}})):n.domain().map((function(t,e){return{coordinate:n(t)+u,value:o?o[t]:t,index:e,offset:u}}))},Vl=new WeakMap,Xl=function(t,e){if("function"!=typeof e)return t;Vl.has(t)||Vl.set(t,new WeakMap);var r=Vl.get(t);if(r.has(e))return r.get(e);var n=function(){t.apply(void 0,arguments),e.apply(void 0,arguments)};return r.set(e,n),n},Hl=function(e,r,n){var o=e.scale,i=e.type,a=e.layout,c=e.axisType;if("auto"===o)return"radial"===a&&"radiusAxis"===c?{scale:jn(),realScaleType:"band"}:"radial"===a&&"angleAxis"===c?{scale:fi(),realScaleType:"linear"}:"category"===i&&r&&(r.indexOf("LineChart")>=0||r.indexOf("AreaChart")>=0||r.indexOf("ComposedChart")>=0&&!n)?{scale:An(),realScaleType:"point"}:"category"===i?{scale:jn(),realScaleType:"band"}:{scale:fi(),realScaleType:"linear"};if(p()(o)){var u="scale".concat(mt()(o));return{scale:(t[u]||An)(),realScaleType:t[u]?u:"point"}}return y()(o)?{scale:o}:{scale:An(),realScaleType:"point"}},Gl=1e-4,Yl=function(t){var e=t.domain();if(e&&!(e.length<=2)){var r=e.length,n=t.range(),o=Math.min(n[0],n[1])-Gl,i=Math.max(n[0],n[1])+Gl,a=t(e[0]),c=t(e[r-1]);(a<o||a>i||c<o||c>i)&&t.domain([e[0],e[r-1]])}},Zl=function(t,e){if(!t)return null;for(var r=0,n=t.length;r<n;r++)if(t[r].item===e)return t[r].position;return null},Jl=function(t,e){if(!e||2!==e.length||!S(e[0])||!S(e[1]))return t;var r=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]),o=[t[0],t[1]];return(!S(t[0])||t[0]<r)&&(o[0]=r),(!S(t[1])||t[1]>n)&&(o[1]=n),o[0]>n&&(o[0]=n),o[1]<r&&(o[1]=r),o},Ql={sign:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var o=0,i=0,a=0;a<e;++a){var c=g()(t[a][r][1])?t[a][r][0]:t[a][r][1];c>=0?(t[a][r][0]=o,t[a][r][1]=o+c,o=t[a][r][1]):(t[a][r][0]=i,t[a][r][1]=i+c,i=t[a][r][1])}},expand:function(t,e){if((n=t.length)>0){for(var r,n,o,i=0,a=t[0].length;i<a;++i){for(o=r=0;r<n;++r)o+=t[r][i][1]||0;if(o)for(r=0;r<n;++r)t[r][i][1]/=o}ju(t,e)}},none:ju,silhouette:function(t,e){if((r=t.length)>0){for(var r,n=0,o=t[e[0]],i=o.length;n<i;++n){for(var a=0,c=0;a<r;++a)c+=t[a][n][1]||0;o[n][1]+=o[n][0]=-c/2}ju(t,e)}},wiggle:function(t,e){if((o=t.length)>0&&(n=(r=t[e[0]]).length)>0){for(var r,n,o,i=0,a=1;a<n;++a){for(var c=0,u=0,l=0;c<o;++c){for(var s=t[e[c]],f=s[a][1]||0,p=(f-(s[a-1][1]||0))/2,h=0;h<c;++h){var y=t[e[h]];p+=(y[a][1]||0)-(y[a-1][1]||0)}u+=f,l+=p*f}r[a-1][1]+=r[a-1][0]=i,u&&(i-=l/u)}r[a-1][1]+=r[a-1][0]=i,ju(t,e)}},positive:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var o=0,i=0;i<e;++i){var a=g()(t[i][r][1])?t[i][r][0]:t[i][r][1];a>=0?(t[i][r][0]=o,t[i][r][1]=o+a,o=t[i][r][1]):(t[i][r][0]=0,t[i][r][1]=0)}}},ts=function(t,e,r){var n=e.map((function(t){return t.props.dataKey})),o=Ql[r],i=function(){var t=Ft([]),e=Au,r=ju,n=Pu;function o(o){var i,a,c=Array.from(t.apply(this,arguments),Eu),u=c.length,l=-1;for(const t of o)for(i=0,++l;i<u;++i)(c[i][l]=[0,+n(t,c[i].key,l,o)]).data=t;for(i=0,a=Su(e(c));i<u;++i)c[a[i]].index=i;return r(c,a),c}return o.keys=function(e){return arguments.length?(t="function"==typeof e?e:Ft(Array.from(e)),o):t},o.value=function(t){return arguments.length?(n="function"==typeof t?t:Ft(+t),o):n},o.order=function(t){return arguments.length?(e=null==t?Au:"function"==typeof t?t:Ft(Array.from(t)),o):e},o.offset=function(t){return arguments.length?(r=null==t?ju:t,o):r},o}().keys(n).value((function(t,e){return+Ll(t,e,0)})).order(Au).offset(o);return i(t)},es=function(t,e){var r=e.realScaleType,n=e.type,o=e.tickCount,i=e.originalDomain,a=e.allowDecimals,c=r||e.scale;if("auto"!==c&&"linear"!==c)return null;if(o&&"number"===n&&i&&("auto"===i[0]||"auto"===i[1])){var u=t.domain();if(!u.length)return null;var l=ll(u,o,a);return t.domain([_u()(l),Tu()(l)]),{niceTicks:l}}if(o&&"number"===n){var s=t.domain();return{niceTicks:sl(s,o,a)}}return null};function rs(t){var e=t.axis,r=t.ticks,n=t.bandSize,o=t.entry,i=t.index,a=t.dataKey;if("category"===e.type){if(!e.allowDuplicatedCategory&&e.dataKey&&!s()(o[e.dataKey])){var c=_(r,"value",o[e.dataKey]);if(c)return c.coordinate+n/2}return r[i]?r[i].coordinate+n/2:null}var u=Ll(o,s()(a)?e.dataKey:a);return s()(u)?null:e.scale(u)}var ns=function(t){var e=t.axis,r=t.ticks,n=t.offset,o=t.bandSize,i=t.entry,a=t.index;if("category"===e.type)return r[a]?r[a].coordinate+n:null;var c=Ll(i,e.dataKey,e.domain[a]);return s()(c)?null:e.scale(c)-o/2+n},os=function(t){var e=t.numericAxis,r=e.scale.domain();if("number"===e.type){var n=Math.min(r[0],r[1]),o=Math.max(r[0],r[1]);return n<=0&&o>=0?0:o<0?o:n}return r[0]},is=function(t,e,r){return Object.keys(t).reduce((function(n,o){var i=t[o].stackedData.reduce((function(t,n){var o=n.slice(e,r+1).reduce((function(t,e){return[_u()(e.concat([t[0]]).filter(S)),Tu()(e.concat([t[1]]).filter(S))]}),[1/0,-1/0]);return[Math.min(t[0],o[0]),Math.max(t[1],o[1])]}),[1/0,-1/0]);return[Math.min(i[0],n[0]),Math.max(i[1],n[1])]}),[1/0,-1/0]).map((function(t){return t===1/0||t===-1/0?0:t}))},as=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,cs=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,us=function(t,e,r){if(y()(t))return t(e,r);if(!Array.isArray(t))return e;var n=[];if(S(t[0]))n[0]=r?t[0]:Math.min(t[0],e[0]);else if(as.test(t[0])){var o=+as.exec(t[0])[1];n[0]=e[0]-o}else y()(t[0])?n[0]=t[0](e[0]):n[0]=e[0];if(S(t[1]))n[1]=r?t[1]:Math.max(t[1],e[1]);else if(cs.test(t[1])){var i=+cs.exec(t[1])[1];n[1]=e[1]+i}else y()(t[1])?n[1]=t[1](e[1]):n[1]=e[1];return n},ls=function(t,e,r){if(t&&t.scale&&t.scale.bandwidth){var n=t.scale.bandwidth();if(!r||n>0)return n}if(t&&e&&e.length>=2){for(var o=Ie()(e,(function(t){return t.coordinate})),i=1/0,a=1,c=o.length;a<c;a++){var u=o[a],l=o[a-1];i=Math.min((u.coordinate||0)-(l.coordinate||0),i)}return i===1/0?0:i}return r?void 0:0},ss=function(t,e,r){return t&&t.length?Nu()(t,u()(r,"type.defaultProps.domain"))?e:t:e},fs=function(t,e){var r=t.type.defaultProps?Rl(Rl({},t.type.defaultProps),t.props):t.props,n=r.dataKey,o=r.name,i=r.unit,a=r.formatter,c=r.tooltipType,u=r.chartType,l=r.hide;return Rl(Rl({},nt(t,!1)),{},{dataKey:n,unit:i,formatter:a,name:o||n,color:Fl(t),value:Ll(e,n),type:c,payload:e,chartType:u,hide:l})};function ps(t){return ps="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ps(t)}function hs(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function ys(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hs(Object(r),!0).forEach((function(e){ds(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hs(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function ds(t,e,r){var n;return n=function(t,e){if("object"!=ps(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ps(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==ps(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function vs(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return ms(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ms(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ms(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var bs=Math.PI/180,gs=function(t){return 180*t/Math.PI},xs=function(t,e,r,n){return{x:t+Math.cos(-bs*n)*r,y:e+Math.sin(-bs*n)*r}},ws=function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(t-(r.left||0)-(r.right||0)),Math.abs(e-(r.top||0)-(r.bottom||0)))/2},Os=function(t,e,r,n,o){var i=t.width,a=t.height,c=t.startAngle,u=t.endAngle,l=k(t.cx,i,i/2),f=k(t.cy,a,a/2),p=ws(i,a,r),h=k(t.innerRadius,p,0),y=k(t.outerRadius,p,.8*p);return Object.keys(e).reduce((function(t,r){var i,a=e[r],p=a.domain,d=a.reversed;if(s()(a.range))"angleAxis"===n?i=[c,u]:"radiusAxis"===n&&(i=[h,y]),d&&(i=[i[1],i[0]]);else{var v=vs(i=a.range,2);c=v[0],u=v[1]}var m=Hl(a,o),b=m.realScaleType,g=m.scale;g.domain(p).range(i),Yl(g);var x=es(g,ys(ys({},a),{},{realScaleType:b})),w=ys(ys(ys({},a),x),{},{range:i,radius:y,realScaleType:b,scale:g,cx:l,cy:f,innerRadius:h,outerRadius:y,startAngle:c,endAngle:u});return ys(ys({},t),{},ds({},r,w))}),{})},js=function(t,e){var r=t.x,n=t.y,o=e.cx,i=e.cy,a=function(t,e){var r=t.x,n=t.y,o=e.x,i=e.y;return Math.sqrt(Math.pow(r-o,2)+Math.pow(n-i,2))}({x:r,y:n},{x:o,y:i});if(a<=0)return{radius:a};var c=(r-o)/a,u=Math.acos(c);return n>i&&(u=2*Math.PI-u),{radius:a,angle:gs(u),angleInRadian:u}},Ss=function(t,e){var r=e.startAngle,n=e.endAngle,o=Math.floor(r/360),i=Math.floor(n/360);return t+360*Math.min(o,i)},As=function(t,e){var r=t.x,n=t.y,o=js({x:r,y:n},e),i=o.radius,a=o.angle,c=e.innerRadius,u=e.outerRadius;if(i<c||i>u)return!1;if(0===i)return!0;var l,s=function(t){var e=t.startAngle,r=t.endAngle,n=Math.floor(e/360),o=Math.floor(r/360),i=Math.min(n,o);return{startAngle:e-360*i,endAngle:r-360*i}}(e),f=s.startAngle,p=s.endAngle,h=a;if(f<=p){for(;h>p;)h-=360;for(;h<f;)h+=360;l=h>=f&&h<=p}else{for(;h>f;)h-=360;for(;h<p;)h+=360;l=h>=p&&h<=f}return l?ys(ys({},e),{},{radius:i,angle:Ss(h,e)}):null},Ps=function(t){return(0,e.isValidElement)(t)||y()(t)||"boolean"==typeof t?"":t.className};function Es(t){return Es="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Es(t)}var ks=["offset"];function Ts(t){return function(t){if(Array.isArray(t))return Ms(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return Ms(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ms(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ms(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function _s(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function Cs(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Ds(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Cs(Object(r),!0).forEach((function(e){Is(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Cs(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Is(t,e,r){var n;return n=function(t,e){if("object"!=Es(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Es(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==Es(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Ns(){return Ns=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Ns.apply(this,arguments)}var Rs=function(t){var e=t.value,r=t.formatter,n=s()(t.children)?e:t.children;return y()(r)?r(n):n},Bs=function(t,e,n){var o,i,c=t.position,u=t.viewBox,l=t.offset,f=t.className,p=u,h=p.cx,y=p.cy,d=p.innerRadius,v=p.outerRadius,m=p.startAngle,b=p.endAngle,g=p.clockWise,x=(d+v)/2,w=function(t,e){return O(e-t)*Math.min(Math.abs(e-t),360)}(m,b),j=w>=0?1:-1;"insideStart"===c?(o=m+j*l,i=g):"insideEnd"===c?(o=b-j*l,i=!g):"end"===c&&(o=b+j*l,i=g),i=w<=0?i:!i;var S=xs(h,y,x,o),A=xs(h,y,x,o+359*(i?1:-1)),P="M".concat(S.x,",").concat(S.y,"\n    A").concat(x,",").concat(x,",0,1,").concat(i?0:1,",\n    ").concat(A.x,",").concat(A.y),k=s()(t.id)?E("recharts-radial-line-"):t.id;return r().createElement("text",Ns({},n,{dominantBaseline:"central",className:a("recharts-radial-bar-label",f)}),r().createElement("defs",null,r().createElement("path",{id:k,d:P})),r().createElement("textPath",{xlinkHref:"#".concat(k)},e))},Ls=function(t){var e=t.viewBox,r=t.offset,n=t.position,o=e,i=o.cx,a=o.cy,c=o.innerRadius,u=o.outerRadius,l=(o.startAngle+o.endAngle)/2;if("outside"===n){var s=xs(i,a,u+r,l),f=s.x;return{x:f,y:s.y,textAnchor:f>=i?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"end"};var p=xs(i,a,(c+u)/2,l);return{x:p.x,y:p.y,textAnchor:"middle",verticalAnchor:"middle"}},zs=function(t){var e=t.viewBox,r=t.parentViewBox,n=t.offset,o=t.position,i=e,a=i.x,c=i.y,u=i.width,l=i.height,s=l>=0?1:-1,f=s*n,p=s>0?"end":"start",h=s>0?"start":"end",y=u>=0?1:-1,d=y*n,m=y>0?"end":"start",b=y>0?"start":"end";if("top"===o)return Ds(Ds({},{x:a+u/2,y:c-s*n,textAnchor:"middle",verticalAnchor:p}),r?{height:Math.max(c-r.y,0),width:u}:{});if("bottom"===o)return Ds(Ds({},{x:a+u/2,y:c+l+f,textAnchor:"middle",verticalAnchor:h}),r?{height:Math.max(r.y+r.height-(c+l),0),width:u}:{});if("left"===o){var g={x:a-d,y:c+l/2,textAnchor:m,verticalAnchor:"middle"};return Ds(Ds({},g),r?{width:Math.max(g.x-r.x,0),height:l}:{})}if("right"===o){var x={x:a+u+d,y:c+l/2,textAnchor:b,verticalAnchor:"middle"};return Ds(Ds({},x),r?{width:Math.max(r.x+r.width-x.x,0),height:l}:{})}var w=r?{width:u,height:l}:{};return"insideLeft"===o?Ds({x:a+d,y:c+l/2,textAnchor:b,verticalAnchor:"middle"},w):"insideRight"===o?Ds({x:a+u-d,y:c+l/2,textAnchor:m,verticalAnchor:"middle"},w):"insideTop"===o?Ds({x:a+u/2,y:c+f,textAnchor:"middle",verticalAnchor:h},w):"insideBottom"===o?Ds({x:a+u/2,y:c+l-f,textAnchor:"middle",verticalAnchor:p},w):"insideTopLeft"===o?Ds({x:a+d,y:c+f,textAnchor:b,verticalAnchor:h},w):"insideTopRight"===o?Ds({x:a+u-d,y:c+f,textAnchor:m,verticalAnchor:h},w):"insideBottomLeft"===o?Ds({x:a+d,y:c+l-f,textAnchor:b,verticalAnchor:p},w):"insideBottomRight"===o?Ds({x:a+u-d,y:c+l-f,textAnchor:m,verticalAnchor:p},w):v()(o)&&(S(o.x)||j(o.x))&&(S(o.y)||j(o.y))?Ds({x:a+k(o.x,u),y:c+k(o.y,l),textAnchor:"end",verticalAnchor:"end"},w):Ds({x:a+u/2,y:c+l/2,textAnchor:"middle",verticalAnchor:"middle"},w)},Fs=function(t){return"cx"in t&&S(t.cx)};function Us(t){var n,o=t.offset,i=Ds({offset:void 0===o?5:o},_s(t,ks)),c=i.viewBox,u=i.position,l=i.value,f=i.children,p=i.content,h=i.className,d=void 0===h?"":h,v=i.textBreakAll;if(!c||s()(l)&&s()(f)&&!(0,e.isValidElement)(p)&&!y()(p))return null;if((0,e.isValidElement)(p))return(0,e.cloneElement)(p,i);if(y()(p)){if(n=(0,e.createElement)(p,i),(0,e.isValidElement)(n))return n}else n=Rs(i);var m=Fs(c),b=nt(i,!0);if(m&&("insideStart"===u||"insideEnd"===u||"end"===u))return Bs(i,n,b);var g=m?Ls(i):zs(i);return r().createElement(hn,Ns({className:a("recharts-label",d)},b,g,{breakAll:v}),n)}Us.displayName="Label";var Ws=function(t){var e=t.cx,r=t.cy,n=t.angle,o=t.startAngle,i=t.endAngle,a=t.r,c=t.radius,u=t.innerRadius,l=t.outerRadius,s=t.x,f=t.y,p=t.top,h=t.left,y=t.width,d=t.height,v=t.clockWise,m=t.labelViewBox;if(m)return m;if(S(y)&&S(d)){if(S(s)&&S(f))return{x:s,y:f,width:y,height:d};if(S(p)&&S(h))return{x:p,y:h,width:y,height:d}}return S(s)&&S(f)?{x:s,y:f,width:0,height:0}:S(e)&&S(r)?{cx:e,cy:r,startAngle:o||n||0,endAngle:i||n||0,innerRadius:u||0,outerRadius:l||c||a||0,clockWise:v}:t.viewBox?t.viewBox:{}};Us.parseViewBox=Ws,Us.renderCallByParent=function(t,n){var o=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!t||!t.children&&o&&!t.label)return null;var i=t.children,a=Ws(t),c=Y(i,Us).map((function(t,r){return(0,e.cloneElement)(t,{viewBox:n||a,key:"label-".concat(r)})}));if(!o)return c;var u=function(t,n){return t?!0===t?r().createElement(Us,{key:"label-implicit",viewBox:n}):A(t)?r().createElement(Us,{key:"label-implicit",viewBox:n,value:t}):(0,e.isValidElement)(t)?t.type===Us?(0,e.cloneElement)(t,{key:"label-implicit",viewBox:n}):r().createElement(Us,{key:"label-implicit",content:t,viewBox:n}):y()(t)?r().createElement(Us,{key:"label-implicit",content:t,viewBox:n}):v()(t)?r().createElement(Us,Ns({viewBox:n},t,{key:"label-implicit"})):null:null}(t.label,n||a);return[u].concat(Ts(c))};var $s=o(2008),Ks=o.n($s);function qs(t){return qs="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},qs(t)}var Vs=["valueAccessor"],Xs=["data","dataKey","clockWise","id","textBreakAll"];function Hs(t){return function(t){if(Array.isArray(t))return Gs(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return Gs(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Gs(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Gs(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Ys(){return Ys=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Ys.apply(this,arguments)}function Zs(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Js(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Zs(Object(r),!0).forEach((function(e){Qs(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Zs(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Qs(t,e,r){var n;return n=function(t,e){if("object"!=qs(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=qs(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==qs(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tf(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var ef=function(t){return Array.isArray(t.value)?Ks()(t.value):t.value};function rf(t){var e=t.valueAccessor,n=void 0===e?ef:e,o=tf(t,Vs),i=o.data,a=o.dataKey,c=o.clockWise,u=o.id,l=o.textBreakAll,f=tf(o,Xs);return i&&i.length?r().createElement(yt,{className:"recharts-label-list"},i.map((function(t,e){var o=s()(a)?n(t,e):Ll(t&&t.payload,a),i=s()(u)?{}:{id:"".concat(u,"-").concat(e)};return r().createElement(Us,Ys({},nt(t,!0),f,i,{parentViewBox:t.parentViewBox,value:o,textBreakAll:l,viewBox:Us.parseViewBox(s()(c)?t:Js(Js({},t),{},{clockWise:c})),key:"label-".concat(e),index:e}))}))):null}rf.displayName="LabelList",rf.renderCallByParent=function(t,n){var o=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!t||!t.children&&o&&!t.label)return null;var i=Y(t.children,rf).map((function(t,r){return(0,e.cloneElement)(t,{data:n,key:"labelList-".concat(r)})}));return o?[function(t,e){return t?!0===t?r().createElement(rf,{key:"labelList-implicit",data:e}):r().isValidElement(t)||y()(t)?r().createElement(rf,{key:"labelList-implicit",data:e,content:t}):v()(t)?r().createElement(rf,Ys({data:e},t,{key:"labelList-implicit"})):null:null}(t.label,n)].concat(Hs(i)):i};var nf=["component"];function of(t){return of="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},of(t)}function af(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function cf(t){var n,o=t.component,i=af(t,nf);return(0,e.isValidElement)(o)?n=(0,e.cloneElement)(o,i):y()(o)?n=(0,e.createElement)(o,i):dt(!1,"Customized's props `component` must be React.element or Function, but got %s.",of(o)),r().createElement(yt,{className:"recharts-customized-wrapper"},n)}function uf(t){return uf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},uf(t)}function lf(){return lf=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},lf.apply(this,arguments)}function sf(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function ff(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sf(Object(r),!0).forEach((function(e){pf(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sf(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function pf(t,e,r){var n;return n=function(t,e){if("object"!=uf(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=uf(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==uf(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}cf.displayName="Customized";var hf=function(t){var e=t.cx,r=t.cy,n=t.radius,o=t.angle,i=t.sign,a=t.isExternal,c=t.cornerRadius,u=t.cornerIsExternal,l=c*(a?1:-1)+n,s=Math.asin(c/l)/bs,f=u?o:o+i*s,p=u?o-i*s:o;return{center:xs(e,r,l,f),circleTangency:xs(e,r,n,f),lineTangency:xs(e,r,l*Math.cos(s*bs),p),theta:s}},yf=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,o=t.outerRadius,i=t.startAngle,a=function(t,e){return O(e-t)*Math.min(Math.abs(e-t),359.999)}(i,t.endAngle),c=i+a,u=xs(e,r,o,i),l=xs(e,r,o,c),s="M ".concat(u.x,",").concat(u.y,"\n    A ").concat(o,",").concat(o,",0,\n    ").concat(+(Math.abs(a)>180),",").concat(+(i>c),",\n    ").concat(l.x,",").concat(l.y,"\n  ");if(n>0){var f=xs(e,r,n,i),p=xs(e,r,n,c);s+="L ".concat(p.x,",").concat(p.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(a)>180),",").concat(+(i<=c),",\n            ").concat(f.x,",").concat(f.y," Z")}else s+="L ".concat(e,",").concat(r," Z");return s},df={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},vf=function(t){var e=ff(ff({},df),t),n=e.cx,o=e.cy,i=e.innerRadius,c=e.outerRadius,u=e.cornerRadius,l=e.forceCornerRadius,s=e.cornerIsExternal,f=e.startAngle,p=e.endAngle,h=e.className;if(c<i||f===p)return null;var y,d=a("recharts-sector",h),v=c-i,m=k(u,v,0,!0);return y=m>0&&Math.abs(f-p)<360?function(t){var e=t.cx,r=t.cy,n=t.innerRadius,o=t.outerRadius,i=t.cornerRadius,a=t.forceCornerRadius,c=t.cornerIsExternal,u=t.startAngle,l=t.endAngle,s=O(l-u),f=hf({cx:e,cy:r,radius:o,angle:u,sign:s,cornerRadius:i,cornerIsExternal:c}),p=f.circleTangency,h=f.lineTangency,y=f.theta,d=hf({cx:e,cy:r,radius:o,angle:l,sign:-s,cornerRadius:i,cornerIsExternal:c}),v=d.circleTangency,m=d.lineTangency,b=d.theta,g=c?Math.abs(u-l):Math.abs(u-l)-y-b;if(g<0)return a?"M ".concat(h.x,",").concat(h.y,"\n        a").concat(i,",").concat(i,",0,0,1,").concat(2*i,",0\n        a").concat(i,",").concat(i,",0,0,1,").concat(2*-i,",0\n      "):yf({cx:e,cy:r,innerRadius:n,outerRadius:o,startAngle:u,endAngle:l});var x="M ".concat(h.x,",").concat(h.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(p.x,",").concat(p.y,"\n    A").concat(o,",").concat(o,",0,").concat(+(g>180),",").concat(+(s<0),",").concat(v.x,",").concat(v.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(m.x,",").concat(m.y,"\n  ");if(n>0){var w=hf({cx:e,cy:r,radius:n,angle:u,sign:s,isExternal:!0,cornerRadius:i,cornerIsExternal:c}),j=w.circleTangency,S=w.lineTangency,A=w.theta,P=hf({cx:e,cy:r,radius:n,angle:l,sign:-s,isExternal:!0,cornerRadius:i,cornerIsExternal:c}),E=P.circleTangency,k=P.lineTangency,T=P.theta,M=c?Math.abs(u-l):Math.abs(u-l)-A-T;if(M<0&&0===i)return"".concat(x,"L").concat(e,",").concat(r,"Z");x+="L".concat(k.x,",").concat(k.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(E.x,",").concat(E.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(M>180),",").concat(+(s>0),",").concat(j.x,",").concat(j.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(S.x,",").concat(S.y,"Z")}else x+="L".concat(e,",").concat(r,"Z");return x}({cx:n,cy:o,innerRadius:i,outerRadius:c,cornerRadius:Math.min(m,v/2),forceCornerRadius:l,cornerIsExternal:s,startAngle:f,endAngle:p}):yf({cx:n,cy:o,innerRadius:i,outerRadius:c,startAngle:f,endAngle:p}),r().createElement("path",lf({},nt(e,!0),{className:d,d:y,role:"img"}))};function mf(){}function bf(t,e,r){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+e)/6,(t._y0+4*t._y1+r)/6)}function gf(t){this._context=t}function xf(t){this._context=t}function wf(t){this._context=t}gf.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:bf(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:bf(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},xf.prototype={areaStart:mf,areaEnd:mf,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._x2=t,this._y2=e;break;case 1:this._point=2,this._x3=t,this._y3=e;break;case 2:this._point=3,this._x4=t,this._y4=e,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+e)/6);break;default:bf(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},wf.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+t)/6,n=(this._y0+4*this._y1+e)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:bf(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}};class Of{constructor(t,e){this._context=t,this._x=e}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,e,t,e):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+e)/2,t,this._y0,t,e)}this._x0=t,this._y0=e}}function jf(t){this._context=t}function Sf(t){this._context=t}function Af(t){return new Sf(t)}function Pf(t){return t<0?-1:1}function Ef(t,e,r){var n=t._x1-t._x0,o=e-t._x1,i=(t._y1-t._y0)/(n||o<0&&-0),a=(r-t._y1)/(o||n<0&&-0),c=(i*o+a*n)/(n+o);return(Pf(i)+Pf(a))*Math.min(Math.abs(i),Math.abs(a),.5*Math.abs(c))||0}function kf(t,e){var r=t._x1-t._x0;return r?(3*(t._y1-t._y0)/r-e)/2:e}function Tf(t,e,r){var n=t._x0,o=t._y0,i=t._x1,a=t._y1,c=(i-n)/3;t._context.bezierCurveTo(n+c,o+c*e,i-c,a-c*r,i,a)}function Mf(t){this._context=t}function _f(t){this._context=new Cf(t)}function Cf(t){this._context=t}function Df(t){this._context=t}function If(t){var e,r,n=t.length-1,o=new Array(n),i=new Array(n),a=new Array(n);for(o[0]=0,i[0]=2,a[0]=t[0]+2*t[1],e=1;e<n-1;++e)o[e]=1,i[e]=4,a[e]=4*t[e]+2*t[e+1];for(o[n-1]=2,i[n-1]=7,a[n-1]=8*t[n-1]+t[n],e=1;e<n;++e)r=o[e]/i[e-1],i[e]-=r,a[e]-=r*a[e-1];for(o[n-1]=a[n-1]/i[n-1],e=n-2;e>=0;--e)o[e]=(a[e]-o[e+1])/i[e];for(i[n-1]=(t[n]+o[n-1])/2,e=0;e<n-1;++e)i[e]=2*t[e+1]-o[e+1];return[o,i]}function Nf(t,e){this._context=t,this._t=e}function Rf(t){return t[0]}function Bf(t){return t[1]}function Lf(t,e){var r=Ft(!0),n=null,o=Af,i=null,a=Xt(c);function c(c){var u,l,s,f=(c=Su(c)).length,p=!1;for(null==n&&(i=o(s=a())),u=0;u<=f;++u)!(u<f&&r(l=c[u],u,c))===p&&((p=!p)?i.lineStart():i.lineEnd()),p&&i.point(+t(l,u,c),+e(l,u,c));if(s)return i=null,s+""||null}return t="function"==typeof t?t:void 0===t?Rf:Ft(t),e="function"==typeof e?e:void 0===e?Bf:Ft(e),c.x=function(e){return arguments.length?(t="function"==typeof e?e:Ft(+e),c):t},c.y=function(t){return arguments.length?(e="function"==typeof t?t:Ft(+t),c):e},c.defined=function(t){return arguments.length?(r="function"==typeof t?t:Ft(!!t),c):r},c.curve=function(t){return arguments.length?(o=t,null!=n&&(i=o(n)),c):o},c.context=function(t){return arguments.length?(null==t?n=i=null:i=o(n=t),c):n},c}function zf(t,e,r){var n=null,o=Ft(!0),i=null,a=Af,c=null,u=Xt(l);function l(l){var s,f,p,h,y,d=(l=Su(l)).length,v=!1,m=new Array(d),b=new Array(d);for(null==i&&(c=a(y=u())),s=0;s<=d;++s){if(!(s<d&&o(h=l[s],s,l))===v)if(v=!v)f=s,c.areaStart(),c.lineStart();else{for(c.lineEnd(),c.lineStart(),p=s-1;p>=f;--p)c.point(m[p],b[p]);c.lineEnd(),c.areaEnd()}v&&(m[s]=+t(h,s,l),b[s]=+e(h,s,l),c.point(n?+n(h,s,l):m[s],r?+r(h,s,l):b[s]))}if(y)return c=null,y+""||null}function s(){return Lf().defined(o).curve(a).context(i)}return t="function"==typeof t?t:void 0===t?Rf:Ft(+t),e="function"==typeof e?e:Ft(void 0===e?0:+e),r="function"==typeof r?r:void 0===r?Bf:Ft(+r),l.x=function(e){return arguments.length?(t="function"==typeof e?e:Ft(+e),n=null,l):t},l.x0=function(e){return arguments.length?(t="function"==typeof e?e:Ft(+e),l):t},l.x1=function(t){return arguments.length?(n=null==t?null:"function"==typeof t?t:Ft(+t),l):n},l.y=function(t){return arguments.length?(e="function"==typeof t?t:Ft(+t),r=null,l):e},l.y0=function(t){return arguments.length?(e="function"==typeof t?t:Ft(+t),l):e},l.y1=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:Ft(+t),l):r},l.lineX0=l.lineY0=function(){return s().x(t).y(e)},l.lineY1=function(){return s().x(t).y(r)},l.lineX1=function(){return s().x(n).y(e)},l.defined=function(t){return arguments.length?(o="function"==typeof t?t:Ft(!!t),l):o},l.curve=function(t){return arguments.length?(a=t,null!=i&&(c=a(i)),l):a},l.context=function(t){return arguments.length?(null==t?i=c=null:c=a(i=t),l):i},l}function Ff(t){return Ff="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ff(t)}function Uf(){return Uf=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Uf.apply(this,arguments)}function Wf(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function $f(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Wf(Object(r),!0).forEach((function(e){Kf(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Wf(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Kf(t,e,r){var n;return n=function(t,e){if("object"!=Ff(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Ff(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==Ff(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}jf.prototype={areaStart:mf,areaEnd:mf,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,e){t=+t,e=+e,this._point?this._context.lineTo(t,e):(this._point=1,this._context.moveTo(t,e))}},Sf.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._context.lineTo(t,e)}}},Mf.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:Tf(this,this._t0,kf(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){var r=NaN;if(e=+e,(t=+t)!==this._x1||e!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,Tf(this,kf(this,r=Ef(this,t,e)),r);break;default:Tf(this,this._t0,r=Ef(this,t,e))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e,this._t0=r}}},(_f.prototype=Object.create(Mf.prototype)).point=function(t,e){Mf.prototype.point.call(this,e,t)},Cf.prototype={moveTo:function(t,e){this._context.moveTo(e,t)},closePath:function(){this._context.closePath()},lineTo:function(t,e){this._context.lineTo(e,t)},bezierCurveTo:function(t,e,r,n,o,i){this._context.bezierCurveTo(e,t,n,r,i,o)}},Df.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,e=this._y,r=t.length;if(r)if(this._line?this._context.lineTo(t[0],e[0]):this._context.moveTo(t[0],e[0]),2===r)this._context.lineTo(t[1],e[1]);else for(var n=If(t),o=If(e),i=0,a=1;a<r;++i,++a)this._context.bezierCurveTo(n[0][i],o[0][i],n[1][i],o[1][i],t[a],e[a]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,e){this._x.push(+t),this._y.push(+e)}},Nf.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,e),this._context.lineTo(t,e);else{var r=this._x*(1-this._t)+t*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,e)}}this._x=t,this._y=e}};var qf={curveBasisClosed:function(t){return new xf(t)},curveBasisOpen:function(t){return new wf(t)},curveBasis:function(t){return new gf(t)},curveBumpX:function(t){return new Of(t,!0)},curveBumpY:function(t){return new Of(t,!1)},curveLinearClosed:function(t){return new jf(t)},curveLinear:Af,curveMonotoneX:function(t){return new Mf(t)},curveMonotoneY:function(t){return new _f(t)},curveNatural:function(t){return new Df(t)},curveStep:function(t){return new Nf(t,.5)},curveStepAfter:function(t){return new Nf(t,1)},curveStepBefore:function(t){return new Nf(t,0)}},Vf=function(t){return t.x===+t.x&&t.y===+t.y},Xf=function(t){return t.x},Hf=function(t){return t.y},Gf=function(t){var e,r=t.type,n=void 0===r?"linear":r,o=t.points,i=void 0===o?[]:o,a=t.baseLine,c=t.layout,u=t.connectNulls,l=void 0!==u&&u,s=function(t,e){if(y()(t))return t;var r="curve".concat(mt()(t));return"curveMonotone"!==r&&"curveBump"!==r||!e?qf[r]||Af:qf["".concat(r).concat("vertical"===e?"Y":"X")]}(n,c),f=l?i.filter((function(t){return Vf(t)})):i;if(Array.isArray(a)){var p=l?a.filter((function(t){return Vf(t)})):a,h=f.map((function(t,e){return $f($f({},t),{},{base:p[e]})}));return(e="vertical"===c?zf().y(Hf).x1(Xf).x0((function(t){return t.base.x})):zf().x(Xf).y1(Hf).y0((function(t){return t.base.y}))).defined(Vf).curve(s),e(h)}return(e="vertical"===c&&S(a)?zf().y(Hf).x1(Xf).x0(a):S(a)?zf().x(Xf).y1(Hf).y0(a):Lf().x(Xf).y(Hf)).defined(Vf).curve(s),e(f)},Yf=function(t){var r=t.className,n=t.points,o=t.path,i=t.pathRef;if(!(n&&n.length||o))return null;var c=n&&n.length?Gf(t):o;return e.createElement("path",Uf({},nt(t,!1),z(t),{className:a("recharts-curve",r),d:c,ref:i}))},Zf=o(9660),Jf=o.n(Zf),Qf=Object.getOwnPropertyNames,tp=Object.getOwnPropertySymbols,ep=Object.prototype.hasOwnProperty;function rp(t,e){return function(r,n,o){return t(r,n,o)&&e(r,n,o)}}function np(t){return function(e,r,n){if(!e||!r||"object"!=typeof e||"object"!=typeof r)return t(e,r,n);var o=n.cache,i=o.get(e),a=o.get(r);if(i&&a)return i===r&&a===e;o.set(e,r),o.set(r,e);var c=t(e,r,n);return o.delete(e),o.delete(r),c}}function op(t){return Qf(t).concat(tp(t))}var ip=Object.hasOwn||function(t,e){return ep.call(t,e)};function ap(t,e){return t||e?t===e:t===e||t!=t&&e!=e}var cp="_owner",up=Object.getOwnPropertyDescriptor,lp=Object.keys;function sp(t,e,r){var n=t.length;if(e.length!==n)return!1;for(;n-- >0;)if(!r.equals(t[n],e[n],n,n,t,e,r))return!1;return!0}function fp(t,e){return ap(t.getTime(),e.getTime())}function pp(t,e,r){if(t.size!==e.size)return!1;for(var n,o,i={},a=t.entries(),c=0;(n=a.next())&&!n.done;){for(var u=e.entries(),l=!1,s=0;(o=u.next())&&!o.done;){var f=n.value,p=f[0],h=f[1],y=o.value,d=y[0],v=y[1];l||i[s]||!(l=r.equals(p,d,c,s,t,e,r)&&r.equals(h,v,p,d,t,e,r))||(i[s]=!0),s++}if(!l)return!1;c++}return!0}function hp(t,e,r){var n,o=lp(t),i=o.length;if(lp(e).length!==i)return!1;for(;i-- >0;){if((n=o[i])===cp&&(t.$$typeof||e.$$typeof)&&t.$$typeof!==e.$$typeof)return!1;if(!ip(e,n)||!r.equals(t[n],e[n],n,n,t,e,r))return!1}return!0}function yp(t,e,r){var n,o,i,a=op(t),c=a.length;if(op(e).length!==c)return!1;for(;c-- >0;){if((n=a[c])===cp&&(t.$$typeof||e.$$typeof)&&t.$$typeof!==e.$$typeof)return!1;if(!ip(e,n))return!1;if(!r.equals(t[n],e[n],n,n,t,e,r))return!1;if(o=up(t,n),i=up(e,n),(o||i)&&(!o||!i||o.configurable!==i.configurable||o.enumerable!==i.enumerable||o.writable!==i.writable))return!1}return!0}function dp(t,e){return ap(t.valueOf(),e.valueOf())}function vp(t,e){return t.source===e.source&&t.flags===e.flags}function mp(t,e,r){if(t.size!==e.size)return!1;for(var n,o,i={},a=t.values();(n=a.next())&&!n.done;){for(var c=e.values(),u=!1,l=0;(o=c.next())&&!o.done;)u||i[l]||!(u=r.equals(n.value,o.value,n.value,o.value,t,e,r))||(i[l]=!0),l++;if(!u)return!1}return!0}function bp(t,e){var r=t.length;if(e.length!==r)return!1;for(;r-- >0;)if(t[r]!==e[r])return!1;return!0}var gp="[object Arguments]",xp="[object Boolean]",wp="[object Date]",Op="[object Map]",jp="[object Number]",Sp="[object Object]",Ap="[object RegExp]",Pp="[object Set]",Ep="[object String]",kp=Array.isArray,Tp="function"==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,Mp=Object.assign,_p=Object.prototype.toString.call.bind(Object.prototype.toString);var Cp=Dp();Dp({strict:!0}),Dp({circular:!0}),Dp({circular:!0,strict:!0}),Dp({createInternalComparator:function(){return ap}}),Dp({strict:!0,createInternalComparator:function(){return ap}}),Dp({circular:!0,createInternalComparator:function(){return ap}}),Dp({circular:!0,createInternalComparator:function(){return ap},strict:!0});function Dp(t){void 0===t&&(t={});var e,r=t.circular,n=void 0!==r&&r,o=t.createInternalComparator,i=t.createState,a=t.strict,c=void 0!==a&&a,u=function(t){var e=t.circular,r=t.createCustomConfig,n=t.strict,o={areArraysEqual:n?yp:sp,areDatesEqual:fp,areMapsEqual:n?rp(pp,yp):pp,areObjectsEqual:n?yp:hp,arePrimitiveWrappersEqual:dp,areRegExpsEqual:vp,areSetsEqual:n?rp(mp,yp):mp,areTypedArraysEqual:n?yp:bp};if(r&&(o=Mp({},o,r(o))),e){var i=np(o.areArraysEqual),a=np(o.areMapsEqual),c=np(o.areObjectsEqual),u=np(o.areSetsEqual);o=Mp({},o,{areArraysEqual:i,areMapsEqual:a,areObjectsEqual:c,areSetsEqual:u})}return o}(t),l=function(t){var e=t.areArraysEqual,r=t.areDatesEqual,n=t.areMapsEqual,o=t.areObjectsEqual,i=t.arePrimitiveWrappersEqual,a=t.areRegExpsEqual,c=t.areSetsEqual,u=t.areTypedArraysEqual;return function(t,l,s){if(t===l)return!0;if(null==t||null==l||"object"!=typeof t||"object"!=typeof l)return t!=t&&l!=l;var f=t.constructor;if(f!==l.constructor)return!1;if(f===Object)return o(t,l,s);if(kp(t))return e(t,l,s);if(null!=Tp&&Tp(t))return u(t,l,s);if(f===Date)return r(t,l,s);if(f===RegExp)return a(t,l,s);if(f===Map)return n(t,l,s);if(f===Set)return c(t,l,s);var p=_p(t);return p===wp?r(t,l,s):p===Ap?a(t,l,s):p===Op?n(t,l,s):p===Pp?c(t,l,s):p===Sp?"function"!=typeof t.then&&"function"!=typeof l.then&&o(t,l,s):p===gp?o(t,l,s):(p===xp||p===jp||p===Ep)&&i(t,l,s)}}(u),s=o?o(l):(e=l,function(t,r,n,o,i,a,c){return e(t,r,c)});return function(t){var e=t.circular,r=t.comparator,n=t.createState,o=t.equals,i=t.strict;if(n)return function(t,a){var c=n(),u=c.cache,l=void 0===u?e?new WeakMap:void 0:u,s=c.meta;return r(t,a,{cache:l,equals:o,meta:s,strict:i})};if(e)return function(t,e){return r(t,e,{cache:new WeakMap,equals:o,meta:void 0,strict:i})};var a={cache:void 0,equals:o,meta:void 0,strict:i};return function(t,e){return r(t,e,a)}}({circular:n,comparator:l,createState:i,equals:s,strict:c})}function Ip(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=-1;requestAnimationFrame((function n(o){r<0&&(r=o),o-r>e?(t(o),r=-1):function(t){"undefined"!=typeof requestAnimationFrame&&requestAnimationFrame(t)}(n)}))}function Np(t){return Np="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Np(t)}function Rp(t){return function(t){if(Array.isArray(t))return t}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return Bp(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Bp(t,e)}(t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Bp(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Lp(){var t=function(){return null},e=!1,r=function r(n){if(!e){if(Array.isArray(n)){if(!n.length)return;var o=Rp(n),i=o[0],a=o.slice(1);return"number"==typeof i?void Ip(r.bind(null,a),i):(r(i),void Ip(r.bind(null,a)))}"object"===Np(n)&&t(n),"function"==typeof n&&n()}};return{stop:function(){e=!0},start:function(t){e=!1,r(t)},subscribe:function(e){return t=e,function(){t=function(){return null}}}}}function zp(t){return zp="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},zp(t)}function Fp(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Up(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Fp(Object(r),!0).forEach((function(e){Wp(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Fp(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Wp(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==zp(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==zp(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===zp(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var $p=function(t){return t},Kp=function(t,e){return Object.keys(e).reduce((function(r,n){return Up(Up({},r),{},Wp({},n,t(n,e[n])))}),{})},qp=function(t,e,r){return t.map((function(t){return"".concat((n=t,n.replace(/([A-Z])/g,(function(t){return"-".concat(t.toLowerCase())})))," ").concat(e,"ms ").concat(r);var n})).join(",")};function Vp(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||Hp(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Xp(t){return function(t){if(Array.isArray(t))return Gp(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||Hp(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Hp(t,e){if(t){if("string"==typeof t)return Gp(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Gp(t,e):void 0}}function Gp(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Yp=1e-4,Zp=function(t,e){return[0,3*t,3*e-6*t,3*t-3*e+1]},Jp=function(t,e){return t.map((function(t,r){return t*Math.pow(e,r)})).reduce((function(t,e){return t+e}))},Qp=function(t,e){return function(r){var n=Zp(t,e);return Jp(n,r)}},th=function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=e[0],o=e[1],i=e[2],a=e[3];if(1===e.length)switch(e[0]){case"linear":n=0,o=0,i=1,a=1;break;case"ease":n=.25,o=.1,i=.25,a=1;break;case"ease-in":n=.42,o=0,i=1,a=1;break;case"ease-out":n=.42,o=0,i=.58,a=1;break;case"ease-in-out":n=0,o=0,i=.58,a=1;break;default:var c=e[0].split("(");if("cubic-bezier"===c[0]&&4===c[1].split(")")[0].split(",").length){var u=Vp(c[1].split(")")[0].split(",").map((function(t){return parseFloat(t)})),4);n=u[0],o=u[1],i=u[2],a=u[3]}}[n,i,o,a].every((function(t){return"number"==typeof t&&t>=0&&t<=1}));var l,s,f=Qp(n,i),p=Qp(o,a),h=(l=n,s=i,function(t){var e=Zp(l,s),r=[].concat(Xp(e.map((function(t,e){return t*e})).slice(1)),[0]);return Jp(r,t)}),y=function(t){return t>1?1:t<0?0:t},d=function(t){for(var e=t>1?1:t,r=e,n=0;n<8;++n){var o=f(r)-e,i=h(r);if(Math.abs(o-e)<Yp||i<Yp)return p(r);r=y(r-o/i)}return p(r)};return d.isStepper=!1,d},eh=function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=e[0];if("string"==typeof n)switch(n){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return th(n);case"spring":return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.stiff,r=void 0===e?100:e,n=t.damping,o=void 0===n?8:n,i=t.dt,a=void 0===i?17:i,c=function(t,e,n){var i=n+(-(t-e)*r-n*o)*a/1e3,c=n*a/1e3+t;return Math.abs(c-e)<Yp&&Math.abs(i)<Yp?[e,0]:[c,i]};return c.isStepper=!0,c.dt=a,c}();default:if("cubic-bezier"===n.split("(")[0])return th(n)}return"function"==typeof n?n:null};function rh(t){return rh="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},rh(t)}function nh(t){return function(t){if(Array.isArray(t))return lh(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||uh(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function oh(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function ih(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?oh(Object(r),!0).forEach((function(e){ah(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):oh(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function ah(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==rh(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==rh(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===rh(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ch(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||uh(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function uh(t,e){if(t){if("string"==typeof t)return lh(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?lh(t,e):void 0}}function lh(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var sh=function(t,e,r){return t+(e-t)*r},fh=function(t){return t.from!==t.to},ph=function t(e,r,n){var o=Kp((function(t,r){if(fh(r)){var n=ch(e(r.from,r.to,r.velocity),2),o=n[0],i=n[1];return ih(ih({},r),{},{from:o,velocity:i})}return r}),r);return n<1?Kp((function(t,e){return fh(e)?ih(ih({},e),{},{velocity:sh(e.velocity,o[t].velocity,n),from:sh(e.from,o[t].from,n)}):e}),r):t(e,o,n-1)};const hh=function(t,e,r,n,o){var i,a,c,u,l=(i=t,a=e,[Object.keys(i),Object.keys(a)].reduce((function(t,e){return t.filter((function(t){return e.includes(t)}))}))),s=l.reduce((function(r,n){return ih(ih({},r),{},ah({},n,[t[n],e[n]]))}),{}),f=l.reduce((function(r,n){return ih(ih({},r),{},ah({},n,{from:t[n],velocity:0,to:e[n]}))}),{}),p=-1,h=function(){return null};return h=r.isStepper?function(n){c||(c=n);var i=(n-c)/r.dt;f=ph(r,f,i),o(ih(ih(ih({},t),e),Kp((function(t,e){return e.from}),f))),c=n,Object.values(f).filter(fh).length&&(p=requestAnimationFrame(h))}:function(i){u||(u=i);var a=(i-u)/n,c=Kp((function(t,e){return sh.apply(void 0,nh(e).concat([r(a)]))}),s);if(o(ih(ih(ih({},t),e),c)),a<1)p=requestAnimationFrame(h);else{var l=Kp((function(t,e){return sh.apply(void 0,nh(e).concat([r(1)]))}),s);o(ih(ih(ih({},t),e),l))}},function(){return requestAnimationFrame(h),function(){cancelAnimationFrame(p)}}};function yh(t){return yh="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},yh(t)}var dh=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function vh(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r,n,o={},i=Object.keys(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||(o[r]=t[r]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function mh(t){return function(t){if(Array.isArray(t))return bh(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return bh(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return bh(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function bh(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function gh(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function xh(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?gh(Object(r),!0).forEach((function(e){wh(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):gh(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function wh(t,e,r){return(e=jh(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Oh(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,jh(n.key),n)}}function jh(t){var e=function(t,e){if("object"!==yh(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==yh(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===yh(e)?e:String(e)}function Sh(t,e){return Sh=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Sh(t,e)}function Ah(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=kh(t);if(e){var o=kh(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return Ph(this,r)}}function Ph(t,e){if(e&&("object"===yh(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return Eh(t)}function Eh(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function kh(t){return kh=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},kh(t)}var Th=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Sh(t,e)}(c,t);var n,o,i,a=Ah(c);function c(t,e){var r;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,c);var n=(r=a.call(this,t,e)).props,o=n.isActive,i=n.attributeName,u=n.from,l=n.to,s=n.steps,f=n.children,p=n.duration;if(r.handleStyleChange=r.handleStyleChange.bind(Eh(r)),r.changeStyle=r.changeStyle.bind(Eh(r)),!o||p<=0)return r.state={style:{}},"function"==typeof f&&(r.state={style:l}),Ph(r);if(s&&s.length)r.state={style:s[0].style};else if(u){if("function"==typeof f)return r.state={style:u},Ph(r);r.state={style:i?wh({},i,u):u}}else r.state={style:{}};return r}return n=c,(o=[{key:"componentDidMount",value:function(){var t=this.props,e=t.isActive,r=t.canBegin;this.mounted=!0,e&&r&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(t){var e=this.props,r=e.isActive,n=e.canBegin,o=e.attributeName,i=e.shouldReAnimate,a=e.to,c=e.from,u=this.state.style;if(n)if(r){if(!(Cp(t.to,a)&&t.canBegin&&t.isActive)){var l=!t.canBegin||!t.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var s=l||i?c:t.to;if(this.state&&u){var f={style:o?wh({},o,s):s};(o&&u[o]!==s||!o&&u!==s)&&this.setState(f)}this.runAnimation(xh(xh({},this.props),{},{from:s,begin:0}))}}else{var p={style:o?wh({},o,a):a};this.state&&u&&(o&&u[o]!==a||!o&&u!==a)&&this.setState(p)}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var t=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),t&&t()}},{key:"handleStyleChange",value:function(t){this.changeStyle(t)}},{key:"changeStyle",value:function(t){this.mounted&&this.setState({style:t})}},{key:"runJSAnimation",value:function(t){var e=this,r=t.from,n=t.to,o=t.duration,i=t.easing,a=t.begin,c=t.onAnimationEnd,u=t.onAnimationStart,l=hh(r,n,eh(i),o,this.changeStyle);this.manager.start([u,a,function(){e.stopJSAnimation=l()},o,c])}},{key:"runStepAnimation",value:function(t){var e=this,r=t.steps,n=t.begin,o=t.onAnimationStart,i=r[0],a=i.style,c=i.duration,u=void 0===c?0:c;return this.manager.start([o].concat(mh(r.reduce((function(t,n,o){if(0===o)return t;var i=n.duration,a=n.easing,c=void 0===a?"ease":a,u=n.style,l=n.properties,s=n.onAnimationEnd,f=o>0?r[o-1]:n,p=l||Object.keys(u);if("function"==typeof c||"spring"===c)return[].concat(mh(t),[e.runJSAnimation.bind(e,{from:f.style,to:u,duration:i,easing:c}),i]);var h=qp(p,i,c),y=xh(xh(xh({},f.style),u),{},{transition:h});return[].concat(mh(t),[y,i,s]).filter($p)}),[a,Math.max(u,n)])),[t.onAnimationEnd]))}},{key:"runAnimation",value:function(t){this.manager||(this.manager=Lp());var e=t.begin,r=t.duration,n=t.attributeName,o=t.to,i=t.easing,a=t.onAnimationStart,c=t.onAnimationEnd,u=t.steps,l=t.children,s=this.manager;if(this.unSubscribe=s.subscribe(this.handleStyleChange),"function"!=typeof i&&"function"!=typeof l&&"spring"!==i)if(u.length>1)this.runStepAnimation(t);else{var f=n?wh({},n,o):o,p=qp(Object.keys(f),r,i);s.start([a,e,xh(xh({},f),{},{transition:p}),r,c])}else this.runJSAnimation(t)}},{key:"render",value:function(){var t=this.props,n=t.children,o=(t.begin,t.duration),i=(t.attributeName,t.easing,t.isActive),a=(t.steps,t.from,t.to,t.canBegin,t.onAnimationEnd,t.shouldReAnimate,t.onAnimationReStart,vh(t,dh)),c=e.Children.count(n),u=this.state.style;if("function"==typeof n)return n(u);if(!i||0===c||o<=0)return n;var l=function(t){var r=t.props,n=r.style,o=void 0===n?{}:n,i=r.className;return(0,e.cloneElement)(t,xh(xh({},a),{},{style:xh(xh({},o),u),className:i}))};return 1===c?l(e.Children.only(n)):r().createElement("div",null,e.Children.map(n,(function(t){return l(t)})))}}])&&Oh(n.prototype,o),i&&Oh(n,i),Object.defineProperty(n,"prototype",{writable:!1}),c}(e.PureComponent);Th.displayName="Animate",Th.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},Th.propTypes={from:Jf().oneOfType([Jf().object,Jf().string]),to:Jf().oneOfType([Jf().object,Jf().string]),attributeName:Jf().string,duration:Jf().number,begin:Jf().number,easing:Jf().oneOfType([Jf().string,Jf().func]),steps:Jf().arrayOf(Jf().shape({duration:Jf().number.isRequired,style:Jf().object.isRequired,easing:Jf().oneOfType([Jf().oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),Jf().func]),properties:Jf().arrayOf("string"),onAnimationEnd:Jf().func})),children:Jf().oneOfType([Jf().node,Jf().func]),isActive:Jf().bool,canBegin:Jf().bool,onAnimationEnd:Jf().func,shouldReAnimate:Jf().bool,onAnimationStart:Jf().func,onAnimationReStart:Jf().func};const Mh=Th;function _h(t){return _h="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_h(t)}function Ch(){return Ch=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Ch.apply(this,arguments)}function Dh(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return Ih(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ih(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ih(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Nh(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Rh(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Nh(Object(r),!0).forEach((function(e){Bh(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Nh(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Bh(t,e,r){var n;return n=function(t,e){if("object"!=_h(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=_h(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==_h(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Lh=function(t,e,r,n,o){var i,a=Math.min(Math.abs(r)/2,Math.abs(n)/2),c=n>=0?1:-1,u=r>=0?1:-1,l=n>=0&&r>=0||n<0&&r<0?1:0;if(a>0&&o instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=o[f]>a?a:o[f];i="M".concat(t,",").concat(e+c*s[0]),s[0]>0&&(i+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(l,",").concat(t+u*s[0],",").concat(e)),i+="L ".concat(t+r-u*s[1],",").concat(e),s[1]>0&&(i+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(l,",\n        ").concat(t+r,",").concat(e+c*s[1])),i+="L ".concat(t+r,",").concat(e+n-c*s[2]),s[2]>0&&(i+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(l,",\n        ").concat(t+r-u*s[2],",").concat(e+n)),i+="L ".concat(t+u*s[3],",").concat(e+n),s[3]>0&&(i+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(l,",\n        ").concat(t,",").concat(e+n-c*s[3])),i+="Z"}else if(a>0&&o===+o&&o>0){var p=Math.min(a,o);i="M ".concat(t,",").concat(e+c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+u*p,",").concat(e,"\n            L ").concat(t+r-u*p,",").concat(e,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r,",").concat(e+c*p,"\n            L ").concat(t+r,",").concat(e+n-c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r-u*p,",").concat(e+n,"\n            L ").concat(t+u*p,",").concat(e+n,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t,",").concat(e+n-c*p," Z")}else i="M ".concat(t,",").concat(e," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return i},zh=function(t,e){if(!t||!e)return!1;var r=t.x,n=t.y,o=e.x,i=e.y,a=e.width,c=e.height;if(Math.abs(a)>0&&Math.abs(c)>0){var u=Math.min(o,o+a),l=Math.max(o,o+a),s=Math.min(i,i+c),f=Math.max(i,i+c);return r>=u&&r<=l&&n>=s&&n<=f}return!1},Fh={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},Uh=function(t){var n=Rh(Rh({},Fh),t),o=(0,e.useRef)(),i=Dh((0,e.useState)(-1),2),c=i[0],u=i[1];(0,e.useEffect)((function(){if(o.current&&o.current.getTotalLength)try{var t=o.current.getTotalLength();t&&u(t)}catch(t){}}),[]);var l=n.x,s=n.y,f=n.width,p=n.height,h=n.radius,y=n.className,d=n.animationEasing,v=n.animationDuration,m=n.animationBegin,b=n.isAnimationActive,g=n.isUpdateAnimationActive;if(l!==+l||s!==+s||f!==+f||p!==+p||0===f||0===p)return null;var x=a("recharts-rectangle",y);return g?r().createElement(Mh,{canBegin:c>0,from:{width:f,height:p,x:l,y:s},to:{width:f,height:p,x:l,y:s},duration:v,animationEasing:d,isActive:g},(function(t){var e=t.width,i=t.height,a=t.x,u=t.y;return r().createElement(Mh,{canBegin:c>0,from:"0px ".concat(-1===c?1:c,"px"),to:"".concat(c,"px 0px"),attributeName:"strokeDasharray",begin:m,duration:v,isActive:b,easing:d},r().createElement("path",Ch({},nt(n,!0),{className:x,d:Lh(a,u,e,i,h),ref:o})))})):r().createElement("path",Ch({},nt(n,!0),{className:x,d:Lh(l,s,f,p,h)}))},Wh=["points","className","baseLinePoints","connectNulls"];function $h(){return $h=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},$h.apply(this,arguments)}function Kh(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function qh(t){return function(t){if(Array.isArray(t))return Vh(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return Vh(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Vh(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Vh(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Xh=function(t){return t&&t.x===+t.x&&t.y===+t.y},Hh=function(t,e){var r=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=[[]];return t.forEach((function(t){Xh(t)?e[e.length-1].push(t):e[e.length-1].length>0&&e.push([])})),Xh(t[0])&&e[e.length-1].push(t[0]),e[e.length-1].length<=0&&(e=e.slice(0,-1)),e}(t);e&&(r=[r.reduce((function(t,e){return[].concat(qh(t),qh(e))}),[])]);var n=r.map((function(t){return t.reduce((function(t,e,r){return"".concat(t).concat(0===r?"M":"L").concat(e.x,",").concat(e.y)}),"")})).join("");return 1===r.length?"".concat(n,"Z"):n},Gh=function(t){var e=t.points,n=t.className,o=t.baseLinePoints,i=t.connectNulls,c=Kh(t,Wh);if(!e||!e.length)return null;var u=a("recharts-polygon",n);if(o&&o.length){var l=c.stroke&&"none"!==c.stroke,s=function(t,e,r){var n=Hh(t,r);return"".concat("Z"===n.slice(-1)?n.slice(0,-1):n,"L").concat(Hh(e.reverse(),r).slice(1))}(e,o,i);return r().createElement("g",{className:u},r().createElement("path",$h({},nt(c,!0),{fill:"Z"===s.slice(-1)?c.fill:"none",stroke:"none",d:s})),l?r().createElement("path",$h({},nt(c,!0),{fill:"none",d:Hh(e,i)})):null,l?r().createElement("path",$h({},nt(c,!0),{fill:"none",d:Hh(o,i)})):null)}var f=Hh(e,i);return r().createElement("path",$h({},nt(c,!0),{fill:"Z"===f.slice(-1)?c.fill:"none",className:u,d:f}))};function Yh(){return Yh=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Yh.apply(this,arguments)}var Zh=function(t){var r=t.cx,n=t.cy,o=t.r,i=t.className,c=a("recharts-dot",i);return r===+r&&n===+n&&o===+o?e.createElement("circle",Yh({},nt(t,!1),z(t),{className:c,cx:r,cy:n,r:o})):null};function Jh(t){return Jh="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Jh(t)}var Qh=["x","y","top","left","width","height","className"];function ty(){return ty=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ty.apply(this,arguments)}function ey(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function ry(t,e,r){var n;return n=function(t,e){if("object"!=Jh(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Jh(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==Jh(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ny(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var oy=function(t,e,r,n,o,i){return"M".concat(t,",").concat(o,"v").concat(n,"M").concat(i,",").concat(e,"h").concat(r)},iy=function(t){var e=t.x,n=void 0===e?0:e,o=t.y,i=void 0===o?0:o,c=t.top,u=void 0===c?0:c,l=t.left,s=void 0===l?0:l,f=t.width,p=void 0===f?0:f,h=t.height,y=void 0===h?0:h,d=t.className,v=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ey(Object(r),!0).forEach((function(e){ry(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ey(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}({x:n,y:i,top:u,left:s,width:p,height:y},ny(t,Qh));return S(n)&&S(i)&&S(p)&&S(y)&&S(u)&&S(s)?r().createElement("path",ty({},nt(v,!0),{className:a("recharts-cross",d),d:oy(n,i,p,y,u,s)})):null},ay=["cx","cy","innerRadius","outerRadius","gridType","radialLines"];function cy(t){return cy="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},cy(t)}function uy(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function ly(){return ly=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ly.apply(this,arguments)}function sy(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function fy(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sy(Object(r),!0).forEach((function(e){py(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sy(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function py(t,e,r){var n;return n=function(t,e){if("object"!=cy(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=cy(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==cy(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var hy=function(t,e,r,n){var o="";return n.forEach((function(n,i){var a=xs(e,r,t,n);o+=i?"L ".concat(a.x,",").concat(a.y):"M ".concat(a.x,",").concat(a.y)})),o+="Z"},yy=function(t){var e=t.cx,n=t.cy,o=t.innerRadius,i=t.outerRadius,a=t.polarAngles,c=t.radialLines;if(!a||!a.length||!c)return null;var u=fy({stroke:"#ccc"},nt(t,!1));return r().createElement("g",{className:"recharts-polar-grid-angle"},a.map((function(t){var a=xs(e,n,o,t),c=xs(e,n,i,t);return r().createElement("line",ly({},u,{key:"line-".concat(t),x1:a.x,y1:a.y,x2:c.x,y2:c.y}))})))},dy=function(t){var e=t.cx,n=t.cy,o=t.radius,i=t.index,c=fy(fy({stroke:"#ccc"},nt(t,!1)),{},{fill:"none"});return r().createElement("circle",ly({},c,{className:a("recharts-polar-grid-concentric-circle",t.className),key:"circle-".concat(i),cx:e,cy:n,r:o}))},vy=function(t){var e=t.radius,n=t.index,o=fy(fy({stroke:"#ccc"},nt(t,!1)),{},{fill:"none"});return r().createElement("path",ly({},o,{className:a("recharts-polar-grid-concentric-polygon",t.className),key:"path-".concat(n),d:hy(e,t.cx,t.cy,t.polarAngles)}))},my=function(t){var e=t.polarRadius,n=t.gridType;return e&&e.length?r().createElement("g",{className:"recharts-polar-grid-concentric"},e.map((function(e,o){var i=o;return"circle"===n?r().createElement(dy,ly({key:i},t,{radius:e,index:o})):r().createElement(vy,ly({key:i},t,{radius:e,index:o}))}))):null},by=function(t){var e=t.cx,n=void 0===e?0:e,o=t.cy,i=void 0===o?0:o,a=t.innerRadius,c=void 0===a?0:a,u=t.outerRadius,l=void 0===u?0:u,s=t.gridType,f=void 0===s?"polygon":s,p=t.radialLines,h=void 0===p||p,y=uy(t,ay);return l<=0?null:r().createElement("g",{className:"recharts-polar-grid"},r().createElement(yy,ly({cx:n,cy:i,innerRadius:c,outerRadius:l,gridType:f,radialLines:h},y)),r().createElement(my,ly({cx:n,cy:i,innerRadius:c,outerRadius:l,gridType:f,radialLines:h},y)))};by.displayName="PolarGrid";var gy=o(6564),xy=o.n(gy),wy=o(908),Oy=o.n(wy),jy=["cx","cy","angle","ticks","axisLine"],Sy=["ticks","tick","angle","tickFormatter","stroke"];function Ay(t){return Ay="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ay(t)}function Py(){return Py=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Py.apply(this,arguments)}function Ey(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function ky(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Ey(Object(r),!0).forEach((function(e){Ny(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ey(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Ty(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function My(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Ry(n.key),n)}}function _y(t,e,r){return e=Dy(e),function(t,e){if(e&&("object"===Ay(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Cy()?Reflect.construct(e,r||[],Dy(t).constructor):e.apply(t,r))}function Cy(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(Cy=function(){return!!t})()}function Dy(t){return Dy=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Dy(t)}function Iy(t,e){return Iy=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Iy(t,e)}function Ny(t,e,r){return(e=Ry(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Ry(t){var e=function(t,e){if("object"!=Ay(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Ay(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Ay(e)?e:e+""}var By=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),_y(this,e,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Iy(t,e)}(e,t),n=e,o=[{key:"getTickValueCoord",value:function(t){var e=t.coordinate,r=this.props,n=r.angle,o=r.cx,i=r.cy;return xs(o,i,e,n)}},{key:"getTickTextAnchor",value:function(){var t;switch(this.props.orientation){case"left":t="end";break;case"right":t="start";break;default:t="middle"}return t}},{key:"getViewBox",value:function(){var t=this.props,e=t.cx,r=t.cy,n=t.angle,o=t.ticks,i=xy()(o,(function(t){return t.coordinate||0}));return{cx:e,cy:r,startAngle:n,endAngle:n,innerRadius:Oy()(o,(function(t){return t.coordinate||0})).coordinate||0,outerRadius:i.coordinate||0}}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.cx,n=t.cy,o=t.angle,i=t.ticks,a=t.axisLine,c=Ty(t,jy),u=i.reduce((function(t,e){return[Math.min(t[0],e.coordinate),Math.max(t[1],e.coordinate)]}),[1/0,-1/0]),l=xs(e,n,u[0],o),s=xs(e,n,u[1],o),f=ky(ky(ky({},nt(c,!1)),{},{fill:"none"},nt(a,!1)),{},{x1:l.x,y1:l.y,x2:s.x,y2:s.y});return r().createElement("line",Py({className:"recharts-polar-radius-axis-line"},f))}},{key:"renderTicks",value:function(){var t=this,n=this.props,o=n.ticks,i=n.tick,c=n.angle,u=n.tickFormatter,l=n.stroke,s=Ty(n,Sy),f=this.getTickTextAnchor(),p=nt(s,!1),h=nt(i,!1),y=o.map((function(n,o){var s=t.getTickValueCoord(n),y=ky(ky(ky(ky({textAnchor:f,transform:"rotate(".concat(90-c,", ").concat(s.x,", ").concat(s.y,")")},p),{},{stroke:"none",fill:l},h),{},{index:o},s),{},{payload:n});return r().createElement(yt,Py({className:a("recharts-polar-radius-axis-tick",Ps(i)),key:"tick-".concat(n.coordinate)},F(t.props,n,o)),e.renderTickItem(i,y,u?u(n.value,o):n.value))}));return r().createElement(yt,{className:"recharts-polar-radius-axis-ticks"},y)}},{key:"render",value:function(){var t=this.props,e=t.ticks,n=t.axisLine,o=t.tick;return e&&e.length?r().createElement(yt,{className:a("recharts-polar-radius-axis",this.props.className)},n&&this.renderAxisLine(),o&&this.renderTicks(),Us.renderCallByParent(this.props,this.getViewBox())):null}}],i=[{key:"renderTickItem",value:function(t,e,n){return r().isValidElement(t)?r().cloneElement(t,e):y()(t)?t(e):r().createElement(hn,Py({},e,{className:"recharts-polar-radius-axis-tick-value"}),n)}}],o&&My(n.prototype,o),i&&My(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,o,i}(e.PureComponent);function Ly(t){return Ly="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ly(t)}function zy(){return zy=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},zy.apply(this,arguments)}function Fy(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Uy(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Fy(Object(r),!0).forEach((function(e){Xy(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Fy(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Wy(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Hy(n.key),n)}}function $y(t,e,r){return e=qy(e),function(t,e){if(e&&("object"===Ly(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Ky()?Reflect.construct(e,r||[],qy(t).constructor):e.apply(t,r))}function Ky(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(Ky=function(){return!!t})()}function qy(t){return qy=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},qy(t)}function Vy(t,e){return Vy=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Vy(t,e)}function Xy(t,e,r){return(e=Hy(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Hy(t){var e=function(t,e){if("object"!=Ly(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Ly(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Ly(e)?e:e+""}Ny(By,"displayName","PolarRadiusAxis"),Ny(By,"axisType","radiusAxis"),Ny(By,"defaultProps",{type:"number",radiusAxisId:0,cx:0,cy:0,angle:0,orientation:"right",stroke:"#ccc",axisLine:!0,tick:!0,tickCount:5,allowDataOverflow:!1,scale:"auto",allowDuplicatedCategory:!0});var Gy=Math.PI/180,Yy=1e-5,Zy=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),$y(this,e,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Vy(t,e)}(e,t),n=e,o=[{key:"getTickLineCoord",value:function(t){var e=this.props,r=e.cx,n=e.cy,o=e.radius,i=e.orientation,a=e.tickSize||8,c=xs(r,n,o,t.coordinate),u=xs(r,n,o+("inner"===i?-1:1)*a,t.coordinate);return{x1:c.x,y1:c.y,x2:u.x,y2:u.y}}},{key:"getTickTextAnchor",value:function(t){var e=this.props.orientation,r=Math.cos(-t.coordinate*Gy);return r>Yy?"outer"===e?"start":"end":r<-Yy?"outer"===e?"end":"start":"middle"}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.cx,n=t.cy,o=t.radius,i=t.axisLine,a=t.axisLineType,c=Uy(Uy({},nt(this.props,!1)),{},{fill:"none"},nt(i,!1));if("circle"===a)return r().createElement(Zh,zy({className:"recharts-polar-angle-axis-line"},c,{cx:e,cy:n,r:o}));var u=this.props.ticks.map((function(t){return xs(e,n,o,t.coordinate)}));return r().createElement(Gh,zy({className:"recharts-polar-angle-axis-line"},c,{points:u}))}},{key:"renderTicks",value:function(){var t=this,n=this.props,o=n.ticks,i=n.tick,c=n.tickLine,u=n.tickFormatter,l=n.stroke,s=nt(this.props,!1),f=nt(i,!1),p=Uy(Uy({},s),{},{fill:"none"},nt(c,!1)),h=o.map((function(n,o){var h=t.getTickLineCoord(n),y=Uy(Uy(Uy({textAnchor:t.getTickTextAnchor(n)},s),{},{stroke:"none",fill:l},f),{},{index:o,payload:n,x:h.x2,y:h.y2});return r().createElement(yt,zy({className:a("recharts-polar-angle-axis-tick",Ps(i)),key:"tick-".concat(n.coordinate)},F(t.props,n,o)),c&&r().createElement("line",zy({className:"recharts-polar-angle-axis-tick-line"},p,h)),i&&e.renderTickItem(i,y,u?u(n.value,o):n.value))}));return r().createElement(yt,{className:"recharts-polar-angle-axis-ticks"},h)}},{key:"render",value:function(){var t=this.props,e=t.ticks,n=t.radius,o=t.axisLine;return n<=0||!e||!e.length?null:r().createElement(yt,{className:a("recharts-polar-angle-axis",this.props.className)},o&&this.renderAxisLine(),this.renderTicks())}}],i=[{key:"renderTickItem",value:function(t,e,n){return r().isValidElement(t)?r().cloneElement(t,e):y()(t)?t(e):r().createElement(hn,zy({},e,{className:"recharts-polar-angle-axis-tick-value"}),n)}}],o&&Wy(n.prototype,o),i&&Wy(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,o,i}(e.PureComponent);Xy(Zy,"displayName","PolarAngleAxis"),Xy(Zy,"axisType","angleAxis"),Xy(Zy,"defaultProps",{type:"category",angleAxisId:0,scale:"auto",cx:0,cy:0,orientation:"outer",axisLine:!0,tickLine:!0,tickSize:8,tick:!0,hide:!1,allowDuplicatedCategory:!0});var Jy=o(308),Qy=o.n(Jy),td=o(7e3),ed=o.n(td);function rd(t){return rd="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},rd(t)}function nd(){return nd=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},nd.apply(this,arguments)}function od(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return id(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return id(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function id(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function ad(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function cd(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ad(Object(r),!0).forEach((function(e){ud(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ad(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function ud(t,e,r){var n;return n=function(t,e){if("object"!=rd(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=rd(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==rd(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var ld,sd=function(t,e,r,n,o){var i,a=r-n;return i="M ".concat(t,",").concat(e),i+="L ".concat(t+r,",").concat(e),i+="L ".concat(t+r-a/2,",").concat(e+o),i+="L ".concat(t+r-a/2-n,",").concat(e+o),i+="L ".concat(t,",").concat(e," Z")},fd={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},pd=function(t){var n=cd(cd({},fd),t),o=(0,e.useRef)(),i=od((0,e.useState)(-1),2),c=i[0],u=i[1];(0,e.useEffect)((function(){if(o.current&&o.current.getTotalLength)try{var t=o.current.getTotalLength();t&&u(t)}catch(t){}}),[]);var l=n.x,s=n.y,f=n.upperWidth,p=n.lowerWidth,h=n.height,y=n.className,d=n.animationEasing,v=n.animationDuration,m=n.animationBegin,b=n.isUpdateAnimationActive;if(l!==+l||s!==+s||f!==+f||p!==+p||h!==+h||0===f&&0===p||0===h)return null;var g=a("recharts-trapezoid",y);return b?r().createElement(Mh,{canBegin:c>0,from:{upperWidth:0,lowerWidth:0,height:h,x:l,y:s},to:{upperWidth:f,lowerWidth:p,height:h,x:l,y:s},duration:v,animationEasing:d,isActive:b},(function(t){var e=t.upperWidth,i=t.lowerWidth,a=t.height,u=t.x,l=t.y;return r().createElement(Mh,{canBegin:c>0,from:"0px ".concat(-1===c?1:c,"px"),to:"".concat(c,"px 0px"),attributeName:"strokeDasharray",begin:m,duration:v,easing:d},r().createElement("path",nd({},nt(n,!0),{className:g,d:sd(u,l,e,i,a),ref:o})))})):r().createElement("g",null,r().createElement("path",nd({},nt(n,!0),{className:g,d:sd(l,s,f,p,h)})))},hd=["option","shapeType","propTransformer","activeClassName","isActive"];function yd(t){return yd="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},yd(t)}function dd(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function vd(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function md(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?vd(Object(r),!0).forEach((function(e){bd(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):vd(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function bd(t,e,r){var n;return n=function(t,e){if("object"!=yd(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=yd(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==yd(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function gd(t,e){return md(md({},e),t)}function xd(t){var e=t.shapeType,n=t.elementProps;switch(e){case"rectangle":return r().createElement(Uh,n);case"trapezoid":return r().createElement(pd,n);case"sector":return r().createElement(vf,n);case"symbols":if(function(t,e){return"symbols"===t}(e))return r().createElement(ne,n);break;default:return null}}function wd(t){return(0,e.isValidElement)(t)?t.props:t}function Od(t){var n,o=t.option,i=t.shapeType,a=t.propTransformer,c=void 0===a?gd:a,u=t.activeClassName,l=void 0===u?"recharts-active-shape":u,s=t.isActive,f=dd(t,hd);if((0,e.isValidElement)(o))n=(0,e.cloneElement)(o,md(md({},f),wd(o)));else if(y()(o))n=o(f);else if(Qy()(o)&&!ed()(o)){var p=c(o,f);n=r().createElement(xd,{shapeType:i,elementProps:p})}else{var h=f;n=r().createElement(xd,{shapeType:i,elementProps:h})}return s?r().createElement(yt,{className:l},n):n}function jd(t,e){return null!=e&&"trapezoids"in t.props}function Sd(t,e){return null!=e&&"sectors"in t.props}function Ad(t,e){return null!=e&&"points"in t.props}function Pd(t,e){var r,n,o=t.x===(null==e||null===(r=e.labelViewBox)||void 0===r?void 0:r.x)||t.x===e.x,i=t.y===(null==e||null===(n=e.labelViewBox)||void 0===n?void 0:n.y)||t.y===e.y;return o&&i}function Ed(t,e){var r=t.endAngle===e.endAngle,n=t.startAngle===e.startAngle;return r&&n}function kd(t,e){var r=t.x===e.x,n=t.y===e.y,o=t.z===e.z;return r&&n&&o}function Td(t){var e=t.activeTooltipItem,r=t.graphicalItem,n=t.itemData,o=function(t,e){var r;return jd(t,e)?r="trapezoids":Sd(t,e)?r="sectors":Ad(t,e)&&(r="points"),r}(r,e),i=function(t,e){var r,n;return jd(t,e)?null===(r=e.tooltipPayload)||void 0===r||null===(r=r[0])||void 0===r||null===(r=r.payload)||void 0===r?void 0:r.payload:Sd(t,e)?null===(n=e.tooltipPayload)||void 0===n||null===(n=n[0])||void 0===n||null===(n=n.payload)||void 0===n?void 0:n.payload:Ad(t,e)?e.payload:{}}(r,e),a=n.filter((function(t,n){var a=Nu()(i,t),c=r.props[o].filter((function(t){var n=function(t,e){var r;return jd(t,e)?r=Pd:Sd(t,e)?r=Ed:Ad(t,e)&&(r=kd),r}(r,e);return n(t,e)})),u=r.props[o].indexOf(c[c.length-1]);return a&&n===u}));return n.indexOf(a[a.length-1])}function Md(t){return Md="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Md(t)}function _d(){return _d=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},_d.apply(this,arguments)}function Cd(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Dd(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Cd(Object(r),!0).forEach((function(e){zd(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Cd(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Id(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Fd(n.key),n)}}function Nd(t,e,r){return e=Bd(e),function(t,e){if(e&&("object"===Md(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Rd()?Reflect.construct(e,r||[],Bd(t).constructor):e.apply(t,r))}function Rd(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(Rd=function(){return!!t})()}function Bd(t){return Bd=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Bd(t)}function Ld(t,e){return Ld=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Ld(t,e)}function zd(t,e,r){return(e=Fd(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Fd(t){var e=function(t,e){if("object"!=Md(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Md(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Md(e)?e:e+""}var Ud=function(t){function e(t){var r;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),zd(r=Nd(this,e,[t]),"pieRef",null),zd(r,"sectorRefs",[]),zd(r,"id",E("recharts-pie-")),zd(r,"handleAnimationEnd",(function(){var t=r.props.onAnimationEnd;r.setState({isAnimationFinished:!0}),y()(t)&&t()})),zd(r,"handleAnimationStart",(function(){var t=r.props.onAnimationStart;r.setState({isAnimationFinished:!1}),y()(t)&&t()})),r.state={isAnimationFinished:!t.isAnimationActive,prevIsAnimationActive:t.isAnimationActive,prevAnimationId:t.animationId,sectorToFocus:0},r}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Ld(t,e)}(e,t),n=e,i=[{key:"getDerivedStateFromProps",value:function(t,e){return e.prevIsAnimationActive!==t.isAnimationActive?{prevIsAnimationActive:t.isAnimationActive,prevAnimationId:t.animationId,curSectors:t.sectors,prevSectors:[],isAnimationFinished:!0}:t.isAnimationActive&&t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curSectors:t.sectors,prevSectors:e.curSectors,isAnimationFinished:!0}:t.sectors!==e.curSectors?{curSectors:t.sectors,isAnimationFinished:!0}:null}},{key:"getTextAnchor",value:function(t,e){return t>e?"start":t<e?"end":"middle"}},{key:"renderLabelLineItem",value:function(t,e,n){if(r().isValidElement(t))return r().cloneElement(t,e);if(y()(t))return t(e);var o=a("recharts-pie-label-line","boolean"!=typeof t?t.className:"");return r().createElement(Yf,_d({},e,{key:n,type:"linear",className:o}))}},{key:"renderLabelItem",value:function(t,e,n){if(r().isValidElement(t))return r().cloneElement(t,e);var o=n;if(y()(t)&&(o=t(e),r().isValidElement(o)))return o;var i=a("recharts-pie-label-text","boolean"==typeof t||y()(t)?"":t.className);return r().createElement(hn,_d({},e,{alignmentBaseline:"middle",className:i}),o)}}],(o=[{key:"isActiveIndex",value:function(t){var e=this.props.activeIndex;return Array.isArray(e)?-1!==e.indexOf(t):t===e}},{key:"hasActiveIndex",value:function(){var t=this.props.activeIndex;return Array.isArray(t)?0!==t.length:t||0===t}},{key:"renderLabels",value:function(t){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var n=this.props,o=n.label,i=n.labelLine,a=n.dataKey,c=n.valueKey,u=nt(this.props,!1),l=nt(o,!1),f=nt(i,!1),p=o&&o.offsetRadius||20,h=t.map((function(t,n){var h=(t.startAngle+t.endAngle)/2,y=xs(t.cx,t.cy,t.outerRadius+p,h),d=Dd(Dd(Dd(Dd({},u),t),{},{stroke:"none"},l),{},{index:n,textAnchor:e.getTextAnchor(y.x,t.cx)},y),v=Dd(Dd(Dd(Dd({},u),t),{},{fill:"none",stroke:t.fill},f),{},{index:n,points:[xs(t.cx,t.cy,t.outerRadius,h),y]}),m=a;return s()(a)&&s()(c)?m="value":s()(a)&&(m=c),r().createElement(yt,{key:"label-".concat(t.startAngle,"-").concat(t.endAngle,"-").concat(t.midAngle,"-").concat(n)},i&&e.renderLabelLineItem(i,v,"line"),e.renderLabelItem(o,d,Ll(t,m)))}));return r().createElement(yt,{className:"recharts-pie-labels"},h)}},{key:"renderSectorsStatically",value:function(t){var e=this,n=this.props,o=n.activeShape,i=n.blendStroke,a=n.inactiveShape;return t.map((function(n,c){if(0===(null==n?void 0:n.startAngle)&&0===(null==n?void 0:n.endAngle)&&1!==t.length)return null;var u=e.isActiveIndex(c),l=a&&e.hasActiveIndex()?a:null,s=u?o:l,f=Dd(Dd({},n),{},{stroke:i?n.fill:n.stroke,tabIndex:-1});return r().createElement(yt,_d({ref:function(t){t&&!e.sectorRefs.includes(t)&&e.sectorRefs.push(t)},tabIndex:-1,className:"recharts-pie-sector"},F(e.props,n,c),{key:"sector-".concat(null==n?void 0:n.startAngle,"-").concat(null==n?void 0:n.endAngle,"-").concat(n.midAngle,"-").concat(c)}),r().createElement(Od,_d({option:s,isActive:u,shapeType:"sector"},f)))}))}},{key:"renderSectorsWithAnimation",value:function(){var t=this,e=this.props,n=e.sectors,o=e.isAnimationActive,i=e.animationBegin,a=e.animationDuration,c=e.animationEasing,l=e.animationId,s=this.state,f=s.prevSectors,p=s.prevIsAnimationActive;return r().createElement(Mh,{begin:i,duration:a,isActive:o,easing:c,from:{t:0},to:{t:1},key:"pie-".concat(l,"-").concat(p),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},(function(e){var o=e.t,i=[],a=(n&&n[0]).startAngle;return n.forEach((function(t,e){var r=f&&f[e],n=e>0?u()(t,"paddingAngle",0):0;if(r){var c=M(r.endAngle-r.startAngle,t.endAngle-t.startAngle),l=Dd(Dd({},t),{},{startAngle:a+n,endAngle:a+c(o)+n});i.push(l),a=l.endAngle}else{var s=t.endAngle,p=t.startAngle,h=M(0,s-p)(o),y=Dd(Dd({},t),{},{startAngle:a+n,endAngle:a+h+n});i.push(y),a=y.endAngle}})),r().createElement(yt,null,t.renderSectorsStatically(i))}))}},{key:"attachKeyboardHandlers",value:function(t){var e=this;t.onkeydown=function(t){if(!t.altKey)switch(t.key){case"ArrowLeft":var r=++e.state.sectorToFocus%e.sectorRefs.length;e.sectorRefs[r].focus(),e.setState({sectorToFocus:r});break;case"ArrowRight":var n=--e.state.sectorToFocus<0?e.sectorRefs.length-1:e.state.sectorToFocus%e.sectorRefs.length;e.sectorRefs[n].focus(),e.setState({sectorToFocus:n});break;case"Escape":e.sectorRefs[e.state.sectorToFocus].blur(),e.setState({sectorToFocus:0})}}}},{key:"renderSectors",value:function(){var t=this.props,e=t.sectors,r=t.isAnimationActive,n=this.state.prevSectors;return!(r&&e&&e.length)||n&&Nu()(n,e)?this.renderSectorsStatically(e):this.renderSectorsWithAnimation()}},{key:"componentDidMount",value:function(){this.pieRef&&this.attachKeyboardHandlers(this.pieRef)}},{key:"render",value:function(){var t=this,e=this.props,n=e.hide,o=e.sectors,i=e.className,c=e.label,u=e.cx,l=e.cy,s=e.innerRadius,f=e.outerRadius,p=e.isAnimationActive,h=this.state.isAnimationFinished;if(n||!o||!o.length||!S(u)||!S(l)||!S(s)||!S(f))return null;var y=a("recharts-pie",i);return r().createElement(yt,{tabIndex:this.props.rootTabIndex,className:y,ref:function(e){t.pieRef=e}},this.renderSectors(),c&&this.renderLabels(o),Us.renderCallByParent(this.props,null,!1),(!p||h)&&rf.renderCallByParent(this.props,o,!1))}}])&&Id(n.prototype,o),i&&Id(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,o,i}(e.PureComponent);ld=Ud,zd(Ud,"displayName","Pie"),zd(Ud,"defaultProps",{stroke:"#fff",fill:"#808080",legendType:"rect",cx:"50%",cy:"50%",startAngle:0,endAngle:360,innerRadius:0,outerRadius:"80%",paddingAngle:0,labelLine:!0,hide:!1,minAngle:0,isAnimationActive:!cr.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",blendStroke:!1,rootTabIndex:0}),zd(Ud,"parseDeltaAngle",(function(t,e){return O(e-t)*Math.min(Math.abs(e-t),360)})),zd(Ud,"getRealPieData",(function(t){var e=t.data,r=t.children,n=nt(t,!1),o=Y(r,Tr);return e&&e.length?e.map((function(t,e){return Dd(Dd(Dd({payload:t},n),t),o&&o[e]&&o[e].props)})):o&&o.length?o.map((function(t){return Dd(Dd({},n),t.props)})):[]})),zd(Ud,"parseCoordinateOfPie",(function(t,e){var r=e.top,n=e.left,o=e.width,i=e.height,a=ws(o,i);return{cx:n+k(t.cx,o,o/2),cy:r+k(t.cy,i,i/2),innerRadius:k(t.innerRadius,a,0),outerRadius:k(t.outerRadius,a,.8*a),maxRadius:t.maxRadius||Math.sqrt(o*o+i*i)/2}})),zd(Ud,"getComposedData",(function(t){var e=t.item,r=t.offset,n=void 0!==e.type.defaultProps?Dd(Dd({},e.type.defaultProps),e.props):e.props,o=ld.getRealPieData(n);if(!o||!o.length)return null;var i=n.cornerRadius,a=n.startAngle,c=n.endAngle,u=n.paddingAngle,l=n.dataKey,f=n.nameKey,p=n.valueKey,h=n.tooltipType,y=Math.abs(n.minAngle),d=ld.parseCoordinateOfPie(n,r),v=ld.parseDeltaAngle(a,c),m=Math.abs(v),b=l;s()(l)&&s()(p)?(dt(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),b="value"):s()(l)&&(dt(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),b=p);var g,x,w=o.filter((function(t){return 0!==Ll(t,b,0)})).length,j=m-w*y-(m>=360?w:w-1)*u,A=o.reduce((function(t,e){var r=Ll(e,b,0);return t+(S(r)?r:0)}),0);A>0&&(g=o.map((function(t,e){var r,n=Ll(t,b,0),o=Ll(t,f,e),c=(S(n)?n:0)/A,l=(r=e?x.endAngle+O(v)*u*(0!==n?1:0):a)+O(v)*((0!==n?y:0)+c*j),s=(r+l)/2,p=(d.innerRadius+d.outerRadius)/2,m=[{name:o,value:n,payload:t,dataKey:b,type:h}],g=xs(d.cx,d.cy,p,s);return x=Dd(Dd(Dd({percent:c,cornerRadius:i,name:o,tooltipPayload:m,midAngle:s,middleRadius:p,tooltipPosition:g},t),d),{},{value:Ll(t,b),startAngle:r,endAngle:l,payload:t,paddingAngle:O(v)*u})})));return Dd(Dd({},d),{},{sectors:g,data:o})}));var Wd=o(9540),$d=o.n(Wd),Kd=["key"];function qd(t){return qd="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},qd(t)}function Vd(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function Xd(){return Xd=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Xd.apply(this,arguments)}function Hd(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Gd(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Hd(Object(r),!0).forEach((function(e){ev(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Hd(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Yd(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,rv(n.key),n)}}function Zd(t,e,r){return e=Qd(e),function(t,e){if(e&&("object"===qd(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Jd()?Reflect.construct(e,r||[],Qd(t).constructor):e.apply(t,r))}function Jd(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(Jd=function(){return!!t})()}function Qd(t){return Qd=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Qd(t)}function tv(t,e){return tv=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},tv(t,e)}function ev(t,e,r){return(e=rv(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function rv(t){var e=function(t,e){if("object"!=qd(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=qd(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==qd(e)?e:e+""}var nv=function(t){function e(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return ev(t=Zd(this,e,[].concat(n)),"state",{isAnimationFinished:!1}),ev(t,"handleAnimationEnd",(function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),y()(e)&&e()})),ev(t,"handleAnimationStart",(function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),y()(e)&&e()})),ev(t,"handleMouseEnter",(function(e){var r=t.props.onMouseEnter;r&&r(t.props,e)})),ev(t,"handleMouseLeave",(function(e){var r=t.props.onMouseLeave;r&&r(t.props,e)})),t}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&tv(t,e)}(e,t),n=e,i=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curPoints:t.points,prevPoints:e.curPoints}:t.points!==e.curPoints?{curPoints:t.points}:null}},{key:"renderDotItem",value:function(t,e){var n;if(r().isValidElement(t))n=r().cloneElement(t,e);else if(y()(t))n=t(e);else{var o=e.key,i=Vd(e,Kd);n=r().createElement(Zh,Xd({},i,{key:o,className:a("recharts-radar-dot","boolean"!=typeof t?t.className:"")}))}return n}}],(o=[{key:"renderDots",value:function(t){var n=this.props,o=n.dot,i=n.dataKey,a=nt(this.props,!1),c=nt(o,!0),u=t.map((function(t,r){var n=Gd(Gd(Gd({key:"dot-".concat(r),r:3},a),c),{},{dataKey:i,cx:t.x,cy:t.y,index:r,payload:t});return e.renderDotItem(o,n)}));return r().createElement(yt,{className:"recharts-radar-dots"},u)}},{key:"renderPolygonStatically",value:function(t){var e,n=this.props,o=n.shape,i=n.dot,a=n.isRange,c=n.baseLinePoints,u=n.connectNulls;return e=r().isValidElement(o)?r().cloneElement(o,Gd(Gd({},this.props),{},{points:t})):y()(o)?o(Gd(Gd({},this.props),{},{points:t})):r().createElement(Gh,Xd({},nt(this.props,!0),{onMouseEnter:this.handleMouseEnter,onMouseLeave:this.handleMouseLeave,points:t,baseLinePoints:a?c:null,connectNulls:u})),r().createElement(yt,{className:"recharts-radar-polygon"},e,i?this.renderDots(t):null)}},{key:"renderPolygonWithAnimation",value:function(){var t=this,e=this.props,n=e.points,o=e.isAnimationActive,i=e.animationBegin,a=e.animationDuration,c=e.animationEasing,u=e.animationId,l=this.state.prevPoints;return r().createElement(Mh,{begin:i,duration:a,isActive:o,easing:c,from:{t:0},to:{t:1},key:"radar-".concat(u),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},(function(e){var r=e.t,o=l&&l.length/n.length,i=n.map((function(t,e){var n=l&&l[Math.floor(e*o)];if(n){var i=M(n.x,t.x),a=M(n.y,t.y);return Gd(Gd({},t),{},{x:i(r),y:a(r)})}var c=M(t.cx,t.x),u=M(t.cy,t.y);return Gd(Gd({},t),{},{x:c(r),y:u(r)})}));return t.renderPolygonStatically(i)}))}},{key:"renderPolygon",value:function(){var t=this.props,e=t.points,r=t.isAnimationActive,n=t.isRange,o=this.state.prevPoints;return!(r&&e&&e.length)||n||o&&Nu()(o,e)?this.renderPolygonStatically(e):this.renderPolygonWithAnimation()}},{key:"render",value:function(){var t=this.props,e=t.hide,n=t.className,o=t.points,i=t.isAnimationActive;if(e||!o||!o.length)return null;var c=this.state.isAnimationFinished,u=a("recharts-radar",n);return r().createElement(yt,{className:u},this.renderPolygon(),(!i||c)&&rf.renderCallByParent(this.props,o))}}])&&Yd(n.prototype,o),i&&Yd(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,o,i}(e.PureComponent);function ov(t){return ov="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ov(t)}function iv(){return iv=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},iv.apply(this,arguments)}function av(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function cv(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?av(Object(r),!0).forEach((function(e){uv(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):av(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function uv(t,e,r){var n;return n=function(t,e){if("object"!=ov(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ov(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==ov(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function lv(t){return"string"==typeof t?parseInt(t,10):t}function sv(t,e){var r="".concat(e.cx||t.cx),n=Number(r),o="".concat(e.cy||t.cy),i=Number(o);return cv(cv(cv({},e),t),{},{cx:n,cy:i})}function fv(t){return r().createElement(Od,iv({shapeType:"sector",propTransformer:sv},t))}ev(nv,"displayName","Radar"),ev(nv,"defaultProps",{angleAxisId:0,radiusAxisId:0,hide:!1,activeDot:!0,dot:!1,legendType:"rect",isAnimationActive:!cr.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"ease"}),ev(nv,"getComposedData",(function(t){var e=t.radiusAxis,r=t.angleAxis,n=t.displayedData,o=t.dataKey,i=t.bandSize,a=r.cx,c=r.cy,u=!1,l=[],f="number"!==r.type&&null!=i?i:0;n.forEach((function(t,n){var i=Ll(t,r.dataKey,n),p=Ll(t,o),h=r.scale(i)+f,y=Array.isArray(p)?Ks()(p):p,d=s()(y)?void 0:e.scale(y);Array.isArray(p)&&p.length>=2&&(u=!0),l.push(Gd(Gd({},xs(a,c,d,h)),{},{name:i,value:p,cx:a,cy:c,radius:d,angle:h,payload:t}))}));var p=[];return u&&l.forEach((function(t){if(Array.isArray(t.value)){var r=$d()(t.value),n=s()(r)?void 0:e.scale(r);p.push(Gd(Gd({},t),{},{radius:n},xs(a,c,n,t.angle)))}else p.push(t)})),{points:l,isRange:u,baseLinePoints:p}}));var pv=["shape","activeShape","activeIndex","cornerRadius"],hv=["value","background"];function yv(t){return yv="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},yv(t)}function dv(){return dv=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},dv.apply(this,arguments)}function vv(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function mv(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?vv(Object(r),!0).forEach((function(e){Sv(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):vv(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function bv(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function gv(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Av(n.key),n)}}function xv(t,e,r){return e=Ov(e),function(t,e){if(e&&("object"===yv(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,wv()?Reflect.construct(e,r||[],Ov(t).constructor):e.apply(t,r))}function wv(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(wv=function(){return!!t})()}function Ov(t){return Ov=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Ov(t)}function jv(t,e){return jv=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},jv(t,e)}function Sv(t,e,r){return(e=Av(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Av(t){var e=function(t,e){if("object"!=yv(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=yv(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==yv(e)?e:e+""}var Pv=function(t){function e(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return Sv(t=xv(this,e,[].concat(n)),"state",{isAnimationFinished:!1}),Sv(t,"handleAnimationEnd",(function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),y()(e)&&e()})),Sv(t,"handleAnimationStart",(function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),y()(e)&&e()})),t}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&jv(t,e)}(e,t),n=e,i=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curData:t.data,prevData:e.curData}:t.data!==e.curData?{curData:t.data}:null}}],(o=[{key:"getDeltaAngle",value:function(){var t=this.props,e=t.startAngle,r=t.endAngle;return O(r-e)*Math.min(Math.abs(r-e),360)}},{key:"renderSectorsStatically",value:function(t){var e=this,n=this.props,o=n.shape,i=n.activeShape,a=n.activeIndex,c=n.cornerRadius,u=bv(n,pv),l=nt(u,!1);return t.map((function(t,n){var s=n===a,f=mv(mv(mv(mv({},l),{},{cornerRadius:lv(c)},t),F(e.props,t,n)),{},{className:"recharts-radial-bar-sector ".concat(t.className),forceCornerRadius:u.forceCornerRadius,cornerIsExternal:u.cornerIsExternal,isActive:s,option:s?i:o});return r().createElement(fv,dv({},f,{key:"sector-".concat(n)}))}))}},{key:"renderSectorsWithAnimation",value:function(){var t=this,e=this.props,n=e.data,o=e.isAnimationActive,i=e.animationBegin,a=e.animationDuration,c=e.animationEasing,u=e.animationId,l=this.state.prevData;return r().createElement(Mh,{begin:i,duration:a,isActive:o,easing:c,from:{t:0},to:{t:1},key:"radialBar-".concat(u),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},(function(e){var o=e.t,i=n.map((function(t,e){var r=l&&l[e];if(r){var n=M(r.startAngle,t.startAngle),i=M(r.endAngle,t.endAngle);return mv(mv({},t),{},{startAngle:n(o),endAngle:i(o)})}var a=t.endAngle,c=t.startAngle,u=M(c,a);return mv(mv({},t),{},{endAngle:u(o)})}));return r().createElement(yt,null,t.renderSectorsStatically(i))}))}},{key:"renderSectors",value:function(){var t=this.props,e=t.data,r=t.isAnimationActive,n=this.state.prevData;return!(r&&e&&e.length)||n&&Nu()(n,e)?this.renderSectorsStatically(e):this.renderSectorsWithAnimation()}},{key:"renderBackground",value:function(t){var e=this,n=this.props.cornerRadius,o=nt(this.props.background,!1);return t.map((function(t,i){t.value;var c=t.background,u=bv(t,hv);if(!c)return null;var l=mv(mv(mv(mv(mv({cornerRadius:lv(n)},u),{},{fill:"#eee"},c),o),F(e.props,t,i)),{},{index:i,className:a("recharts-radial-bar-background-sector",null==o?void 0:o.className),option:c,isActive:!1});return r().createElement(fv,dv({},l,{key:"sector-".concat(i)}))}))}},{key:"render",value:function(){var t=this.props,e=t.hide,n=t.data,o=t.className,i=t.background,c=t.isAnimationActive;if(e||!n||!n.length)return null;var u=this.state.isAnimationFinished,l=a("recharts-area",o);return r().createElement(yt,{className:l},i&&r().createElement(yt,{className:"recharts-radial-bar-background"},this.renderBackground(n)),r().createElement(yt,{className:"recharts-radial-bar-sectors"},this.renderSectors()),(!c||u)&&rf.renderCallByParent(mv({},this.props),n))}}])&&gv(n.prototype,o),i&&gv(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,o,i}(e.PureComponent);Sv(Pv,"displayName","RadialBar"),Sv(Pv,"defaultProps",{angleAxisId:0,radiusAxisId:0,minPointSize:0,hide:!1,legendType:"rect",data:[],isAnimationActive:!cr.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"ease",forceCornerRadius:!1,cornerIsExternal:!1}),Sv(Pv,"getComposedData",(function(t){var e=t.item,r=t.props,n=t.radiusAxis,o=t.radiusAxisTicks,i=t.angleAxis,a=t.angleAxisTicks,c=t.displayedData,u=t.dataKey,l=t.stackedData,s=t.barPosition,f=t.bandSize,p=t.dataStartIndex,h=Zl(s,e);if(!h)return null;var y=i.cx,d=i.cy,v=r.layout,m=e.props,b=m.children,g=m.minPointSize,x="radial"===v?i:n,w=l?x.scale.domain():null,j=os({numericAxis:x}),S=Y(b,Tr),A=c.map((function(t,c){var s,m,b,x,A,P;if(l?s=Jl(l[p+c],w):(s=Ll(t,u),Array.isArray(s)||(s=[j,s])),"radial"===v){m=ns({axis:n,ticks:o,bandSize:f,offset:h.offset,entry:t,index:c}),A=i.scale(s[1]),x=i.scale(s[0]),b=m+h.size;var E=A-x;if(Math.abs(g)>0&&Math.abs(E)<Math.abs(g))A+=O(E||g)*(Math.abs(g)-Math.abs(E));P={background:{cx:y,cy:d,innerRadius:m,outerRadius:b,startAngle:r.startAngle,endAngle:r.endAngle}}}else{m=n.scale(s[0]),b=n.scale(s[1]),A=(x=ns({axis:i,ticks:a,bandSize:f,offset:h.offset,entry:t,index:c}))+h.size;var k=b-m;if(Math.abs(g)>0&&Math.abs(k)<Math.abs(g))b+=O(k||g)*(Math.abs(g)-Math.abs(k))}return mv(mv(mv(mv({},t),P),{},{payload:t,value:l?s:s[1],cx:y,cy:d,innerRadius:m,outerRadius:b,startAngle:x,endAngle:A},S&&S[c]&&S[c].props),{},{tooltipPayload:[fs(e,t)],tooltipPosition:xs(y,d,(m+b)/2,(x+A)/2)})}));return{data:A,layout:v}}));var Ev=o(9136),kv=o.n(Ev);function Tv(t){return Tv="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Tv(t)}function Mv(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function _v(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Mv(Object(r),!0).forEach((function(e){Cv(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Mv(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Cv(t,e,r){var n;return n=function(t,e){if("object"!=Tv(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Tv(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==Tv(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Dv=["Webkit","Moz","O","ms"];function Iv(t){return Iv="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Iv(t)}function Nv(){return Nv=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Nv.apply(this,arguments)}function Rv(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Bv(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Rv(Object(r),!0).forEach((function(e){$v(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Rv(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Lv(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Kv(n.key),n)}}function zv(t,e,r){return e=Uv(e),function(t,e){if(e&&("object"===Iv(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Fv()?Reflect.construct(e,r||[],Uv(t).constructor):e.apply(t,r))}function Fv(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(Fv=function(){return!!t})()}function Uv(t){return Uv=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Uv(t)}function Wv(t,e){return Wv=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Wv(t,e)}function $v(t,e,r){return(e=Kv(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Kv(t){var e=function(t,e){if("object"!=Iv(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Iv(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Iv(e)?e:e+""}var qv=function(t){return t.changedTouches&&!!t.changedTouches.length},Vv=function(t){function n(t){var e;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,n),$v(e=zv(this,n,[t]),"handleDrag",(function(t){e.leaveTimer&&(clearTimeout(e.leaveTimer),e.leaveTimer=null),e.state.isTravellerMoving?e.handleTravellerMove(t):e.state.isSlideMoving&&e.handleSlideDrag(t)})),$v(e,"handleTouchMove",(function(t){null!=t.changedTouches&&t.changedTouches.length>0&&e.handleDrag(t.changedTouches[0])})),$v(e,"handleDragEnd",(function(){e.setState({isTravellerMoving:!1,isSlideMoving:!1},(function(){var t=e.props,r=t.endIndex,n=t.onDragEnd,o=t.startIndex;null==n||n({endIndex:r,startIndex:o})})),e.detachDragEndListener()})),$v(e,"handleLeaveWrapper",(function(){(e.state.isTravellerMoving||e.state.isSlideMoving)&&(e.leaveTimer=window.setTimeout(e.handleDragEnd,e.props.leaveTimeOut))})),$v(e,"handleEnterSlideOrTraveller",(function(){e.setState({isTextActive:!0})})),$v(e,"handleLeaveSlideOrTraveller",(function(){e.setState({isTextActive:!1})})),$v(e,"handleSlideDragStart",(function(t){var r=qv(t)?t.changedTouches[0]:t;e.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:r.pageX}),e.attachDragEndListener()})),e.travellerDragStartHandlers={startX:e.handleTravellerDragStart.bind(e,"startX"),endX:e.handleTravellerDragStart.bind(e,"endX")},e.state={},e}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Wv(t,e)}(n,t),o=n,i=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(t){var e=t.startX,r=t.endX,o=this.state.scaleValues,i=this.props,a=i.gap,c=i.data.length-1,u=Math.min(e,r),l=Math.max(e,r),s=n.getIndexInRange(o,u),f=n.getIndexInRange(o,l);return{startIndex:s-s%a,endIndex:f===c?c:f-f%a}}},{key:"getTextOfTick",value:function(t){var e=this.props,r=e.data,n=e.tickFormatter,o=e.dataKey,i=Ll(r[t],o,t);return y()(n)?n(i,t):i}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(t){var e=this.state,r=e.slideMoveStartX,n=e.startX,o=e.endX,i=this.props,a=i.x,c=i.width,u=i.travellerWidth,l=i.startIndex,s=i.endIndex,f=i.onChange,p=t.pageX-r;p>0?p=Math.min(p,a+c-u-o,a+c-u-n):p<0&&(p=Math.max(p,a-n,a-o));var h=this.getIndex({startX:n+p,endX:o+p});h.startIndex===l&&h.endIndex===s||!f||f(h),this.setState({startX:n+p,endX:o+p,slideMoveStartX:t.pageX})}},{key:"handleTravellerDragStart",value:function(t,e){var r=qv(e)?e.changedTouches[0]:e;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:t,brushMoveStartX:r.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(t){var e=this.state,r=e.brushMoveStartX,n=e.movingTravellerId,o=e.endX,i=e.startX,a=this.state[n],c=this.props,u=c.x,l=c.width,s=c.travellerWidth,f=c.onChange,p=c.gap,h=c.data,y={startX:this.state.startX,endX:this.state.endX},d=t.pageX-r;d>0?d=Math.min(d,u+l-s-a):d<0&&(d=Math.max(d,u-a)),y[n]=a+d;var v=this.getIndex(y),m=v.startIndex,b=v.endIndex;this.setState($v($v({},n,a+d),"brushMoveStartX",t.pageX),(function(){var t;f&&(t=h.length-1,("startX"===n&&(o>i?m%p==0:b%p==0)||o<i&&b===t||"endX"===n&&(o>i?b%p==0:m%p==0)||o>i&&b===t)&&f(v))}))}},{key:"handleTravellerMoveKeyboard",value:function(t,e){var r=this,n=this.state,o=n.scaleValues,i=n.startX,a=n.endX,c=this.state[e],u=o.indexOf(c);if(-1!==u){var l=u+t;if(!(-1===l||l>=o.length)){var s=o[l];"startX"===e&&s>=a||"endX"===e&&s<=i||this.setState($v({},e,s),(function(){r.props.onChange(r.getIndex({startX:r.state.startX,endX:r.state.endX}))}))}}}},{key:"renderBackground",value:function(){var t=this.props,e=t.x,n=t.y,o=t.width,i=t.height,a=t.fill,c=t.stroke;return r().createElement("rect",{stroke:c,fill:a,x:e,y:n,width:o,height:i})}},{key:"renderPanorama",value:function(){var t=this.props,n=t.x,o=t.y,i=t.width,a=t.height,c=t.data,u=t.children,l=t.padding,s=e.Children.only(u);return s?r().cloneElement(s,{x:n,y:o,width:i,height:a,margin:l,compact:!0,data:c}):null}},{key:"renderTravellerLayer",value:function(t,e){var o,i,a=this,c=this.props,u=c.y,l=c.travellerWidth,s=c.height,f=c.traveller,p=c.ariaLabel,h=c.data,y=c.startIndex,d=c.endIndex,v=Math.max(t,this.props.x),m=Bv(Bv({},nt(this.props,!1)),{},{x:v,y:u,width:l,height:s}),b=p||"Min value: ".concat(null===(o=h[y])||void 0===o?void 0:o.name,", Max value: ").concat(null===(i=h[d])||void 0===i?void 0:i.name);return r().createElement(yt,{tabIndex:0,role:"slider","aria-label":b,"aria-valuenow":t,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[e],onTouchStart:this.travellerDragStartHandlers[e],onKeyDown:function(t){["ArrowLeft","ArrowRight"].includes(t.key)&&(t.preventDefault(),t.stopPropagation(),a.handleTravellerMoveKeyboard("ArrowRight"===t.key?1:-1,e))},onFocus:function(){a.setState({isTravellerFocused:!0})},onBlur:function(){a.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},n.renderTraveller(f,m))}},{key:"renderSlide",value:function(t,e){var n=this.props,o=n.y,i=n.height,a=n.stroke,c=n.travellerWidth,u=Math.min(t,e)+c,l=Math.max(Math.abs(e-t)-c,0);return r().createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:a,fillOpacity:.2,x:u,y:o,width:l,height:i})}},{key:"renderText",value:function(){var t=this.props,e=t.startIndex,n=t.endIndex,o=t.y,i=t.height,a=t.travellerWidth,c=t.stroke,u=this.state,l=u.startX,s=u.endX,f={pointerEvents:"none",fill:c};return r().createElement(yt,{className:"recharts-brush-texts"},r().createElement(hn,Nv({textAnchor:"end",verticalAnchor:"middle",x:Math.min(l,s)-5,y:o+i/2},f),this.getTextOfTick(e)),r().createElement(hn,Nv({textAnchor:"start",verticalAnchor:"middle",x:Math.max(l,s)+a+5,y:o+i/2},f),this.getTextOfTick(n)))}},{key:"render",value:function(){var t=this.props,e=t.data,n=t.className,o=t.children,i=t.x,c=t.y,u=t.width,l=t.height,s=t.alwaysShowText,f=this.state,p=f.startX,h=f.endX,y=f.isTextActive,d=f.isSlideMoving,v=f.isTravellerMoving,m=f.isTravellerFocused;if(!e||!e.length||!S(i)||!S(c)||!S(u)||!S(l)||u<=0||l<=0)return null;var b=a("recharts-brush",n),g=1===r().Children.count(o),x=function(t,e){if(!t)return null;var r=t.replace(/(\w)/,(function(t){return t.toUpperCase()})),n=Dv.reduce((function(t,n){return _v(_v({},t),{},Cv({},n+r,e))}),{});return n[t]=e,n}("userSelect","none");return r().createElement(yt,{className:b,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:x},this.renderBackground(),g&&this.renderPanorama(),this.renderSlide(p,h),this.renderTravellerLayer(p,"startX"),this.renderTravellerLayer(h,"endX"),(y||d||v||m||s)&&this.renderText())}}],c=[{key:"renderDefaultTraveller",value:function(t){var e=t.x,n=t.y,o=t.width,i=t.height,a=t.stroke,c=Math.floor(n+i/2)-1;return r().createElement(r().Fragment,null,r().createElement("rect",{x:e,y:n,width:o,height:i,fill:a,stroke:"none"}),r().createElement("line",{x1:e+1,y1:c,x2:e+o-1,y2:c,fill:"none",stroke:"#fff"}),r().createElement("line",{x1:e+1,y1:c+2,x2:e+o-1,y2:c+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(t,e){return r().isValidElement(t)?r().cloneElement(t,e):y()(t)?t(e):n.renderDefaultTraveller(e)}},{key:"getDerivedStateFromProps",value:function(t,e){var r=t.data,n=t.width,o=t.x,i=t.travellerWidth,a=t.updateId,c=t.startIndex,u=t.endIndex;if(r!==e.prevData||a!==e.prevUpdateId)return Bv({prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n},r&&r.length?function(t){var e=t.data,r=t.startIndex,n=t.endIndex,o=t.x,i=t.width,a=t.travellerWidth;if(!e||!e.length)return{};var c=e.length,u=An().domain(kv()(0,c)).range([o,o+i-a]),l=u.domain().map((function(t){return u(t)}));return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:u(r),endX:u(n),scale:u,scaleValues:l}}({data:r,width:n,x:o,travellerWidth:i,startIndex:c,endIndex:u}):{scale:null,scaleValues:null});if(e.scale&&(n!==e.prevWidth||o!==e.prevX||i!==e.prevTravellerWidth)){e.scale.range([o,o+n-i]);var l=e.scale.domain().map((function(t){return e.scale(t)}));return{prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n,startX:e.scale(t.startIndex),endX:e.scale(t.endIndex),scaleValues:l}}return null}},{key:"getIndexInRange",value:function(t,e){for(var r=0,n=t.length-1;n-r>1;){var o=Math.floor((r+n)/2);t[o]>e?n=o:r=o}return e>=t[n]?n:r}}],i&&Lv(o.prototype,i),c&&Lv(o,c),Object.defineProperty(o,"prototype",{writable:!1}),o;var o,i,c}(e.PureComponent);$v(Vv,"displayName","Brush"),$v(Vv,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var Xv=o(9500),Hv=o.n(Xv),Gv=function(t,e){var r=t.alwaysShow,n=t.ifOverflow;return r&&(n="extendDomain"),n===e},Yv=o(6452),Zv=o.n(Yv),Jv=o(164),Qv=o.n(Jv),tm=["x","y"];function em(t){return em="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},em(t)}function rm(){return rm=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},rm.apply(this,arguments)}function nm(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function om(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?nm(Object(r),!0).forEach((function(e){im(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):nm(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function im(t,e,r){var n;return n=function(t,e){if("object"!=em(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=em(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==em(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function am(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function cm(t,e){var r=t.x,n=t.y,o=am(t,tm),i="".concat(r),a=parseInt(i,10),c="".concat(n),u=parseInt(c,10),l="".concat(e.height||o.height),s=parseInt(l,10),f="".concat(e.width||o.width),p=parseInt(f,10);return om(om(om(om(om({},e),o),a?{x:a}:{}),u?{y:u}:{}),{},{height:s,width:p,name:e.name,radius:e.radius})}function um(t){return r().createElement(Od,rm({shapeType:"rectangle",propTransformer:cm,activeClassName:"recharts-active-bar"},t))}var lm,sm=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return function(r,n){if("number"==typeof t)return t;var o=S(r)||function(t){return s()(t)}(r);return o?t(r,n):(o||pl(!1),e)}},fm=["value","background"];function pm(t){return pm="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},pm(t)}function hm(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function ym(){return ym=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ym.apply(this,arguments)}function dm(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function vm(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?dm(Object(r),!0).forEach((function(e){Om(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):dm(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function mm(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,jm(n.key),n)}}function bm(t,e,r){return e=xm(e),function(t,e){if(e&&("object"===pm(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,gm()?Reflect.construct(e,r||[],xm(t).constructor):e.apply(t,r))}function gm(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(gm=function(){return!!t})()}function xm(t){return xm=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},xm(t)}function wm(t,e){return wm=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},wm(t,e)}function Om(t,e,r){return(e=jm(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function jm(t){var e=function(t,e){if("object"!=pm(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=pm(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==pm(e)?e:e+""}var Sm=function(t){function e(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return Om(t=bm(this,e,[].concat(n)),"state",{isAnimationFinished:!1}),Om(t,"id",E("recharts-bar-")),Om(t,"handleAnimationEnd",(function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),e&&e()})),Om(t,"handleAnimationStart",(function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),e&&e()})),t}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&wm(t,e)}(e,t),n=e,o=[{key:"renderRectanglesStatically",value:function(t){var e=this,n=this.props,o=n.shape,i=n.dataKey,a=n.activeIndex,c=n.activeBar,u=nt(this.props,!1);return t&&t.map((function(t,n){var l=n===a,s=l?c:o,f=vm(vm(vm({},u),t),{},{isActive:l,option:s,index:n,dataKey:i,onAnimationStart:e.handleAnimationStart,onAnimationEnd:e.handleAnimationEnd});return r().createElement(yt,ym({className:"recharts-bar-rectangle"},F(e.props,t,n),{key:"rectangle-".concat(null==t?void 0:t.x,"-").concat(null==t?void 0:t.y,"-").concat(null==t?void 0:t.value,"-").concat(n)}),r().createElement(um,f))}))}},{key:"renderRectanglesWithAnimation",value:function(){var t=this,e=this.props,n=e.data,o=e.layout,i=e.isAnimationActive,a=e.animationBegin,c=e.animationDuration,u=e.animationEasing,l=e.animationId,s=this.state.prevData;return r().createElement(Mh,{begin:a,duration:c,isActive:i,easing:u,from:{t:0},to:{t:1},key:"bar-".concat(l),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},(function(e){var i=e.t,a=n.map((function(t,e){var r=s&&s[e];if(r){var n=M(r.x,t.x),a=M(r.y,t.y),c=M(r.width,t.width),u=M(r.height,t.height);return vm(vm({},t),{},{x:n(i),y:a(i),width:c(i),height:u(i)})}if("horizontal"===o){var l=M(0,t.height)(i);return vm(vm({},t),{},{y:t.y+t.height-l,height:l})}var f=M(0,t.width)(i);return vm(vm({},t),{},{width:f})}));return r().createElement(yt,null,t.renderRectanglesStatically(a))}))}},{key:"renderRectangles",value:function(){var t=this.props,e=t.data,r=t.isAnimationActive,n=this.state.prevData;return!(r&&e&&e.length)||n&&Nu()(n,e)?this.renderRectanglesStatically(e):this.renderRectanglesWithAnimation()}},{key:"renderBackground",value:function(){var t=this,e=this.props,n=e.data,o=e.dataKey,i=e.activeIndex,a=nt(this.props.background,!1);return n.map((function(e,n){e.value;var c=e.background,u=hm(e,fm);if(!c)return null;var l=vm(vm(vm(vm(vm({},u),{},{fill:"#eee"},c),a),F(t.props,e,n)),{},{onAnimationStart:t.handleAnimationStart,onAnimationEnd:t.handleAnimationEnd,dataKey:o,index:n,className:"recharts-bar-background-rectangle"});return r().createElement(um,ym({key:"background-bar-".concat(n),option:t.props.background,isActive:n===i},l))}))}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var n=this.props,o=n.data,i=n.xAxis,a=n.yAxis,c=n.layout,u=Y(n.children,Pl);if(!u)return null;var l="vertical"===c?o[0].height/2:o[0].width/2,s=function(t,e){var r=Array.isArray(t.value)?t.value[1]:t.value;return{x:t.x,y:t.y,value:r,errorVal:Ll(t,e)}},f={clipPath:t?"url(#clipPath-".concat(e,")"):null};return r().createElement(yt,f,u.map((function(t){return r().cloneElement(t,{key:"error-bar-".concat(e,"-").concat(t.props.dataKey),data:o,xAxis:i,yAxis:a,layout:c,offset:l,dataPointFormatter:s})})))}},{key:"render",value:function(){var t=this.props,e=t.hide,n=t.data,o=t.className,i=t.xAxis,c=t.yAxis,u=t.left,l=t.top,f=t.width,p=t.height,h=t.isAnimationActive,y=t.background,d=t.id;if(e||!n||!n.length)return null;var v=this.state.isAnimationFinished,m=a("recharts-bar",o),b=i&&i.allowDataOverflow,g=c&&c.allowDataOverflow,x=b||g,w=s()(d)?this.id:d;return r().createElement(yt,{className:m},b||g?r().createElement("defs",null,r().createElement("clipPath",{id:"clipPath-".concat(w)},r().createElement("rect",{x:b?u:u-f/2,y:g?l:l-p/2,width:b?f:2*f,height:g?p:2*p}))):null,r().createElement(yt,{className:"recharts-bar-rectangles",clipPath:x?"url(#clipPath-".concat(w,")"):null},y?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(x,w),(!h||v)&&rf.renderCallByParent(this.props,n))}}],i=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curData:t.data,prevData:e.curData}:t.data!==e.curData?{curData:t.data}:null}}],o&&mm(n.prototype,o),i&&mm(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,o,i}(e.PureComponent);function Am(t){return Am="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Am(t)}function Pm(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Mm(n.key),n)}}function Em(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function km(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Em(Object(r),!0).forEach((function(e){Tm(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Em(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Tm(t,e,r){return(e=Mm(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Mm(t){var e=function(t,e){if("object"!=Am(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Am(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Am(e)?e:e+""}lm=Sm,Om(Sm,"displayName","Bar"),Om(Sm,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!cr.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"}),Om(Sm,"getComposedData",(function(t){var e=t.props,r=t.item,n=t.barPosition,o=t.bandSize,i=t.xAxis,a=t.yAxis,c=t.xAxisTicks,u=t.yAxisTicks,l=t.stackedData,s=t.dataStartIndex,f=t.displayedData,p=t.offset,h=Zl(n,r);if(!h)return null;var y=e.layout,d=r.type.defaultProps,v=void 0!==d?vm(vm({},d),r.props):r.props,m=v.dataKey,b=v.children,g=v.minPointSize,x="horizontal"===y?a:i,w=l?x.scale.domain():null,j=os({numericAxis:x}),S=Y(b,Tr),A=f.map((function(t,e){var n,f,p,d,v,b;l?n=Jl(l[s+e],w):(n=Ll(t,m),Array.isArray(n)||(n=[j,n]));var x=sm(g,lm.defaultProps.minPointSize)(n[1],e);if("horizontal"===y){var A,P=[a.scale(n[0]),a.scale(n[1])],E=P[0],k=P[1];f=ns({axis:i,ticks:c,bandSize:o,offset:h.offset,entry:t,index:e}),p=null!==(A=null!=k?k:E)&&void 0!==A?A:void 0,d=h.size;var T=E-k;if(v=Number.isNaN(T)?0:T,b={x:f,y:a.y,width:d,height:a.height},Math.abs(x)>0&&Math.abs(v)<Math.abs(x)){var M=O(v||x)*(Math.abs(x)-Math.abs(v));p-=M,v+=M}}else{var _=[i.scale(n[0]),i.scale(n[1])],C=_[0],D=_[1];if(f=C,p=ns({axis:a,ticks:u,bandSize:o,offset:h.offset,entry:t,index:e}),d=D-C,v=h.size,b={x:i.x,y:p,width:i.width,height:v},Math.abs(x)>0&&Math.abs(d)<Math.abs(x))d+=O(d||x)*(Math.abs(x)-Math.abs(d))}return vm(vm(vm({},t),{},{x:f,y:p,width:d,height:v,value:l?n:n[1],payload:t,background:b},S&&S[e]&&S[e].props),{},{tooltipPayload:[fs(r,t)],tooltipPosition:{x:f+d/2,y:p+v/2}})}));return vm({data:A,layout:y},p)}));var _m=function(t,e,r,n,o){var i=t.width,a=t.height,c=t.layout,u=t.children,l=Object.keys(e),s={left:r.left,leftMirror:r.left,right:i-r.right,rightMirror:i-r.right,top:r.top,topMirror:r.top,bottom:a-r.bottom,bottomMirror:a-r.bottom},f=!!Z(u,Sm);return l.reduce((function(i,a){var u,l,p,h,y,d=e[a],v=d.orientation,m=d.domain,b=d.padding,g=void 0===b?{}:b,x=d.mirror,w=d.reversed,O="".concat(v).concat(x?"Mirror":"");if("number"===d.type&&("gap"===d.padding||"no-gap"===d.padding)){var j=m[1]-m[0],S=1/0,A=d.categoricalDomain.sort(C);if(A.forEach((function(t,e){e>0&&(S=Math.min((t||0)-(A[e-1]||0),S))})),Number.isFinite(S)){var P=S/j,E="vertical"===d.layout?r.height:r.width;if("gap"===d.padding&&(u=P*E/2),"no-gap"===d.padding){var T=k(t.barCategoryGap,P*E),M=P*E/2;u=M-T-(M-T)/E*T}}}l="xAxis"===n?[r.left+(g.left||0)+(u||0),r.left+r.width-(g.right||0)-(u||0)]:"yAxis"===n?"horizontal"===c?[r.top+r.height-(g.bottom||0),r.top+(g.top||0)]:[r.top+(g.top||0)+(u||0),r.top+r.height-(g.bottom||0)-(u||0)]:d.range,w&&(l=[l[1],l[0]]);var _=Hl(d,o,f),D=_.scale,I=_.realScaleType;D.domain(m).range(l),Yl(D);var N=es(D,km(km({},d),{},{realScaleType:I}));"xAxis"===n?(y="top"===v&&!x||"bottom"===v&&x,p=r.left,h=s[O]-y*d.height):"yAxis"===n&&(y="left"===v&&!x||"right"===v&&x,p=s[O]-y*d.width,h=r.top);var R=km(km(km({},d),N),{},{realScaleType:I,x:p,y:h,scale:D,width:"xAxis"===n?r.width:d.width,height:"yAxis"===n?r.height:d.height});return R.bandSize=ls(R,N),d.hide||"xAxis"!==n?d.hide||(s[O]+=(y?-1:1)*R.width):s[O]+=(y?-1:1)*R.height,km(km({},i),{},Tm({},a,R))}),{})},Cm=function(t,e){var r=t.x,n=t.y,o=e.x,i=e.y;return{x:Math.min(r,o),y:Math.min(n,i),width:Math.abs(o-r),height:Math.abs(i-n)}},Dm=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.scale=e}return e=t,r=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.bandAware,n=e.position;if(void 0!==t){if(n)switch(n){case"start":default:return this.scale(t);case"middle":var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+o;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(t)+i}if(r){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+a}return this.scale(t)}}},{key:"isInRange",value:function(t){var e=this.range(),r=e[0],n=e[e.length-1];return r<=n?t>=r&&t<=n:t>=n&&t<=r}}],n=[{key:"create",value:function(e){return new t(e)}}],r&&Pm(e.prototype,r),n&&Pm(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,r,n}();Tm(Dm,"EPS",1e-4);var Im=function(t){var e=Object.keys(t).reduce((function(e,r){return km(km({},e),{},Tm({},r,Dm.create(t[r])))}),{});return km(km({},e),{},{apply:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.bandAware,o=r.position;return Zv()(t,(function(t,r){return e[r].apply(t,{bandAware:n,position:o})}))},isInRange:function(t){return Qv()(t,(function(t,r){return e[r].isInRange(t)}))}})};var Nm=function(t){var e=t.width,r=t.height,n=function(t){return(t%180+180)%180}(arguments.length>1&&void 0!==arguments[1]?arguments[1]:0),o=n*Math.PI/180,i=Math.atan(r/e),a=o>i&&o<Math.PI-i?r/Math.sin(o):e/Math.cos(o);return Math.abs(a)},Rm=o(7120),Bm=o.n(Rm),Lm=o(1576),zm=o.n(Lm)()((function(t){return{x:t.left,y:t.top,width:t.width,height:t.height}}),(function(t){return["l",t.left,"t",t.top,"w",t.width,"h",t.height].join("")}));var Fm=(0,e.createContext)(void 0),Um=(0,e.createContext)(void 0),Wm=(0,e.createContext)(void 0),$m=(0,e.createContext)({}),Km=(0,e.createContext)(void 0),qm=(0,e.createContext)(0),Vm=(0,e.createContext)(0),Xm=function(t){var e=t.state,n=e.xAxisMap,o=e.yAxisMap,i=e.offset,a=t.clipPathId,c=t.children,u=t.width,l=t.height,s=zm(i);return r().createElement(Fm.Provider,{value:n},r().createElement(Um.Provider,{value:o},r().createElement($m.Provider,{value:i},r().createElement(Wm.Provider,{value:s},r().createElement(Km.Provider,{value:a},r().createElement(qm.Provider,{value:l},r().createElement(Vm.Provider,{value:u},c)))))))};var Hm=function(t){var r=(0,e.useContext)(Fm);null==r&&pl(!1);var n=r[t];return null==n&&pl(!1),n},Gm=function(){var t=(0,e.useContext)(Fm);return T(t)},Ym=function(){var t=(0,e.useContext)(Um);return Bm()(t,(function(t){return Qv()(t.domain,Number.isFinite)}))||T(t)},Zm=function(t){var r=(0,e.useContext)(Um);null==r&&pl(!1);var n=r[t];return null==n&&pl(!1),n},Jm=function(){return(0,e.useContext)($m)},Qm=function(){return(0,e.useContext)(Vm)},tb=function(){return(0,e.useContext)(qm)};function eb(t){return eb="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},eb(t)}function rb(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,sb(n.key),n)}}function nb(t,e,r){return e=ib(e),function(t,e){if(e&&("object"===eb(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,ob()?Reflect.construct(e,r||[],ib(t).constructor):e.apply(t,r))}function ob(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(ob=function(){return!!t})()}function ib(t){return ib=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},ib(t)}function ab(t,e){return ab=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},ab(t,e)}function cb(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function ub(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?cb(Object(r),!0).forEach((function(e){lb(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):cb(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function lb(t,e,r){return(e=sb(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function sb(t){var e=function(t,e){if("object"!=eb(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=eb(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==eb(e)?e:e+""}function fb(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return pb(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return pb(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function pb(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function hb(){return hb=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},hb.apply(this,arguments)}function yb(t){var n=t.x,o=t.y,i=t.segment,c=t.xAxisId,u=t.yAxisId,l=t.shape,s=t.className,f=t.alwaysShow,p=(0,e.useContext)(Km),h=Hm(c),d=Zm(u),v=(0,e.useContext)(Wm);if(!p||!v)return null;dt(void 0===f,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var m=function(t,e,r,n,o,i,a,c,u){var l=o.x,s=o.y,f=o.width,p=o.height;if(r){var h=u.y,y=t.y.apply(h,{position:i});if(Gv(u,"discard")&&!t.y.isInRange(y))return null;var d=[{x:l+f,y},{x:l,y}];return"left"===c?d.reverse():d}if(e){var v=u.x,m=t.x.apply(v,{position:i});if(Gv(u,"discard")&&!t.x.isInRange(m))return null;var b=[{x:m,y:s+p},{x:m,y:s}];return"top"===a?b.reverse():b}if(n){var g=u.segment.map((function(e){return t.apply(e,{position:i})}));return Gv(u,"discard")&&Hv()(g,(function(e){return!t.isInRange(e)}))?null:g}return null}(Im({x:h.scale,y:d.scale}),A(n),A(o),i&&2===i.length,v,t.position,h.orientation,d.orientation,t);if(!m)return null;var b=fb(m,2),g=b[0],x=g.x,w=g.y,O=b[1],j=O.x,S=O.y,P=ub(ub({clipPath:Gv(t,"hidden")?"url(#".concat(p,")"):void 0},nt(t,!0)),{},{x1:x,y1:w,x2:j,y2:S});return r().createElement(yt,{className:a("recharts-reference-line",s)},function(t,e){return r().isValidElement(t)?r().cloneElement(t,e):y()(t)?t(e):r().createElement("line",hb({},e,{className:"recharts-reference-line-line"}))}(l,P),Us.renderCallByParent(t,function(t){var e=t.x1,r=t.y1,n=t.x2,o=t.y2;return Cm({x:e,y:r},{x:n,y:o})}({x1:x,y1:w,x2:j,y2:S})))}var db=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),nb(this,e,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&ab(t,e)}(e,t),n=e,(o=[{key:"render",value:function(){return r().createElement(yb,this.props)}}])&&rb(n.prototype,o),i&&rb(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,o,i}(r().Component);function vb(){return vb=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},vb.apply(this,arguments)}function mb(t){return mb="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},mb(t)}function bb(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function gb(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?bb(Object(r),!0).forEach((function(e){Ab(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):bb(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function xb(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Pb(n.key),n)}}function wb(t,e,r){return e=jb(e),function(t,e){if(e&&("object"===mb(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Ob()?Reflect.construct(e,r||[],jb(t).constructor):e.apply(t,r))}function Ob(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(Ob=function(){return!!t})()}function jb(t){return jb=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},jb(t)}function Sb(t,e){return Sb=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Sb(t,e)}function Ab(t,e,r){return(e=Pb(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Pb(t){var e=function(t,e){if("object"!=mb(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=mb(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==mb(e)?e:e+""}lb(db,"displayName","ReferenceLine"),lb(db,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});var Eb=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),wb(this,e,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Sb(t,e)}(e,t),n=e,o=[{key:"render",value:function(){var t=this.props,n=t.x,o=t.y,i=t.r,c=t.alwaysShow,u=t.clipPathId,l=A(n),s=A(o);if(dt(void 0===c,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!l||!s)return null;var f=function(t){var e=t.x,r=t.y,n=t.xAxis,o=t.yAxis,i=Im({x:n.scale,y:o.scale}),a=i.apply({x:e,y:r},{bandAware:!0});return Gv(t,"discard")&&!i.isInRange(a)?null:a}(this.props);if(!f)return null;var p=f.x,h=f.y,y=this.props,d=y.shape,v=y.className,m=gb(gb({clipPath:Gv(this.props,"hidden")?"url(#".concat(u,")"):void 0},nt(this.props,!0)),{},{cx:p,cy:h});return r().createElement(yt,{className:a("recharts-reference-dot",v)},e.renderDot(d,m),Us.renderCallByParent(this.props,{x:p-i,y:h-i,width:2*i,height:2*i}))}}],o&&xb(n.prototype,o),i&&xb(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,o,i}(r().Component);function kb(){return kb=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},kb.apply(this,arguments)}function Tb(t){return Tb="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Tb(t)}function Mb(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function _b(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Mb(Object(r),!0).forEach((function(e){Bb(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Mb(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Cb(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Lb(n.key),n)}}function Db(t,e,r){return e=Nb(e),function(t,e){if(e&&("object"===Tb(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Ib()?Reflect.construct(e,r||[],Nb(t).constructor):e.apply(t,r))}function Ib(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(Ib=function(){return!!t})()}function Nb(t){return Nb=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Nb(t)}function Rb(t,e){return Rb=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Rb(t,e)}function Bb(t,e,r){return(e=Lb(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Lb(t){var e=function(t,e){if("object"!=Tb(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Tb(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Tb(e)?e:e+""}Ab(Eb,"displayName","ReferenceDot"),Ab(Eb,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1}),Ab(Eb,"renderDot",(function(t,e){return r().isValidElement(t)?r().cloneElement(t,e):y()(t)?t(e):r().createElement(Zh,vb({},e,{cx:e.cx,cy:e.cy,className:"recharts-reference-dot-dot"}))}));var zb=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),Db(this,e,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Rb(t,e)}(e,t),n=e,(o=[{key:"render",value:function(){var t=this.props,n=t.x1,o=t.x2,i=t.y1,c=t.y2,u=t.className,l=t.alwaysShow,s=t.clipPathId;dt(void 0===l,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var f=A(n),p=A(o),h=A(i),y=A(c),d=this.props.shape;if(!(f||p||h||y||d))return null;var v=function(t,e,r,n,o){var i=o.x1,a=o.x2,c=o.y1,u=o.y2,l=o.xAxis,s=o.yAxis;if(!l||!s)return null;var f=Im({x:l.scale,y:s.scale}),p={x:t?f.x.apply(i,{position:"start"}):f.x.rangeMin,y:r?f.y.apply(c,{position:"start"}):f.y.rangeMin},h={x:e?f.x.apply(a,{position:"end"}):f.x.rangeMax,y:n?f.y.apply(u,{position:"end"}):f.y.rangeMax};return!Gv(o,"discard")||f.isInRange(p)&&f.isInRange(h)?Cm(p,h):null}(f,p,h,y,this.props);if(!v&&!d)return null;var m=Gv(this.props,"hidden")?"url(#".concat(s,")"):void 0;return r().createElement(yt,{className:a("recharts-reference-area",u)},e.renderRect(d,_b(_b({clipPath:m},nt(this.props,!0)),v)),Us.renderCallByParent(this.props,v))}}])&&Cb(n.prototype,o),i&&Cb(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,o,i}(r().Component);function Fb(t,e,r){if(e<1)return[];if(1===e&&void 0===r)return t;for(var n=[],o=0;o<t.length;o+=e){if(void 0!==r&&!0!==r(t[o]))return;n.push(t[o])}return n}function Ub(t,e,r,n,o){if(t*e<t*n||t*e>t*o)return!1;var i=r();return t*(e-t*i/2-n)>=0&&t*(e+t*i/2-o)<=0}function Wb(t){return Wb="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Wb(t)}function $b(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Kb(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?$b(Object(r),!0).forEach((function(e){qb(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):$b(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function qb(t,e,r){var n;return n=function(t,e){if("object"!=Wb(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Wb(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==Wb(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Vb(t,e,r){var n=t.tick,o=t.ticks,i=t.viewBox,a=t.minTickGap,c=t.orientation,u=t.interval,l=t.tickFormatter,s=t.unit,f=t.angle;if(!o||!o.length||!n)return[];if(S(u)||cr.isSsr)return function(t,e){return Fb(t,e+1)}(o,"number"==typeof u&&S(u)?u:0);var p=[],h="top"===c||"bottom"===c?"width":"height",d=s&&"width"===h?Br(s,{fontSize:e,letterSpacing:r}):{width:0,height:0},v=function(t,n){var o=y()(l)?l(t.value,n):t.value;return"width"===h?function(t,e,r){var n={width:t.width+e.width,height:t.height+e.height};return Nm(n,r)}(Br(o,{fontSize:e,letterSpacing:r}),d,f):Br(o,{fontSize:e,letterSpacing:r})[h]},m=o.length>=2?O(o[1].coordinate-o[0].coordinate):1,b=function(t,e,r){var n="width"===r,o=t.x,i=t.y,a=t.width,c=t.height;return 1===e?{start:n?o:i,end:n?o+a:i+c}:{start:n?o+a:i+c,end:n?o:i}}(i,m,h);return"equidistantPreserveStart"===u?function(t,e,r,n,o){for(var i,a=(n||[]).slice(),c=e.start,u=e.end,l=0,s=1,f=c,p=function(){var e=null==n?void 0:n[l];if(void 0===e)return{v:Fb(n,s)};var i,a=l,p=function(){return void 0===i&&(i=r(e,a)),i},h=e.coordinate,y=0===l||Ub(t,h,p,f,u);y||(l=0,f=c,s+=1),y&&(f=h+t*(p()/2+o),l+=s)};s<=a.length;)if(i=p())return i.v;return[]}(m,b,v,o,a):(p="preserveStart"===u||"preserveStartEnd"===u?function(t,e,r,n,o,i){var a=(n||[]).slice(),c=a.length,u=e.start,l=e.end;if(i){var s=n[c-1],f=r(s,c-1),p=t*(s.coordinate+t*f/2-l);a[c-1]=s=Kb(Kb({},s),{},{tickCoord:p>0?s.coordinate-p*t:s.coordinate}),Ub(t,s.tickCoord,(function(){return f}),u,l)&&(l=s.tickCoord-t*(f/2+o),a[c-1]=Kb(Kb({},s),{},{isShow:!0}))}for(var h=i?c-1:c,y=function(e){var n,i=a[e],c=function(){return void 0===n&&(n=r(i,e)),n};if(0===e){var s=t*(i.coordinate-t*c()/2-u);a[e]=i=Kb(Kb({},i),{},{tickCoord:s<0?i.coordinate-s*t:i.coordinate})}else a[e]=i=Kb(Kb({},i),{},{tickCoord:i.coordinate});Ub(t,i.tickCoord,c,u,l)&&(u=i.tickCoord+t*(c()/2+o),a[e]=Kb(Kb({},i),{},{isShow:!0}))},d=0;d<h;d++)y(d);return a}(m,b,v,o,a,"preserveStartEnd"===u):function(t,e,r,n,o){for(var i=(n||[]).slice(),a=i.length,c=e.start,u=e.end,l=function(e){var n,l=i[e],s=function(){return void 0===n&&(n=r(l,e)),n};if(e===a-1){var f=t*(l.coordinate+t*s()/2-u);i[e]=l=Kb(Kb({},l),{},{tickCoord:f>0?l.coordinate-f*t:l.coordinate})}else i[e]=l=Kb(Kb({},l),{},{tickCoord:l.coordinate});Ub(t,l.tickCoord,s,c,u)&&(u=l.tickCoord-t*(s()/2+o),i[e]=Kb(Kb({},l),{},{isShow:!0}))},s=a-1;s>=0;s--)l(s);return i}(m,b,v,o,a),p.filter((function(t){return t.isShow})))}Bb(zb,"displayName","ReferenceArea"),Bb(zb,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1}),Bb(zb,"renderRect",(function(t,e){return r().isValidElement(t)?r().cloneElement(t,e):y()(t)?t(e):r().createElement(Uh,kb({},e,{className:"recharts-reference-area-rect"}))}));var Xb=["viewBox"],Hb=["viewBox"],Gb=["ticks"];function Yb(t){return Yb="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Yb(t)}function Zb(){return Zb=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Zb.apply(this,arguments)}function Jb(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Qb(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Jb(Object(r),!0).forEach((function(e){ag(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Jb(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function tg(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function eg(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,cg(n.key),n)}}function rg(t,e,r){return e=og(e),function(t,e){if(e&&("object"===Yb(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,ng()?Reflect.construct(e,r||[],og(t).constructor):e.apply(t,r))}function ng(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(ng=function(){return!!t})()}function og(t){return og=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},og(t)}function ig(t,e){return ig=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},ig(t,e)}function ag(t,e,r){return(e=cg(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function cg(t){var e=function(t,e){if("object"!=Yb(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Yb(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Yb(e)?e:e+""}var ug=function(t){function e(t){var r;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),(r=rg(this,e,[t])).state={fontSize:"",letterSpacing:""},r}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&ig(t,e)}(e,t),n=e,o=[{key:"shouldComponentUpdate",value:function(t,e){var r=t.viewBox,n=tg(t,Xb),o=this.props,i=o.viewBox,a=tg(o,Hb);return!D(r,i)||!D(n,a)||!D(e,this.state)}},{key:"componentDidMount",value:function(){var t=this.layerReference;if(t){var e=t.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];e&&this.setState({fontSize:window.getComputedStyle(e).fontSize,letterSpacing:window.getComputedStyle(e).letterSpacing})}}},{key:"getTickLineCoord",value:function(t){var e,r,n,o,i,a,c=this.props,u=c.x,l=c.y,s=c.width,f=c.height,p=c.orientation,h=c.tickSize,y=c.mirror,d=c.tickMargin,v=y?-1:1,m=t.tickSize||h,b=S(t.tickCoord)?t.tickCoord:t.coordinate;switch(p){case"top":e=r=t.coordinate,a=(n=(o=l+ +!y*f)-v*m)-v*d,i=b;break;case"left":n=o=t.coordinate,i=(e=(r=u+ +!y*s)-v*m)-v*d,a=b;break;case"right":n=o=t.coordinate,i=(e=(r=u+ +y*s)+v*m)+v*d,a=b;break;default:e=r=t.coordinate,a=(n=(o=l+ +y*f)+v*m)+v*d,i=b}return{line:{x1:e,y1:n,x2:r,y2:o},tick:{x:i,y:a}}}},{key:"getTickTextAnchor",value:function(){var t,e=this.props,r=e.orientation,n=e.mirror;switch(r){case"left":t=n?"start":"end";break;case"right":t=n?"end":"start";break;default:t="middle"}return t}},{key:"getTickVerticalAnchor",value:function(){var t=this.props,e=t.orientation,r=t.mirror,n="end";switch(e){case"left":case"right":n="middle";break;case"top":n=r?"start":"end";break;default:n=r?"end":"start"}return n}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.x,n=t.y,o=t.width,i=t.height,c=t.orientation,l=t.mirror,s=t.axisLine,f=Qb(Qb(Qb({},nt(this.props,!1)),nt(s,!1)),{},{fill:"none"});if("top"===c||"bottom"===c){var p=+("top"===c&&!l||"bottom"===c&&l);f=Qb(Qb({},f),{},{x1:e,y1:n+p*i,x2:e+o,y2:n+p*i})}else{var h=+("left"===c&&!l||"right"===c&&l);f=Qb(Qb({},f),{},{x1:e+h*o,y1:n,x2:e+h*o,y2:n+i})}return r().createElement("line",Zb({},f,{className:a("recharts-cartesian-axis-line",u()(s,"className"))}))}},{key:"renderTicks",value:function(t,n,o){var i=this,c=this.props,l=c.tickLine,s=c.stroke,f=c.tick,p=c.tickFormatter,h=c.unit,d=Vb(Qb(Qb({},this.props),{},{ticks:t}),n,o),v=this.getTickTextAnchor(),m=this.getTickVerticalAnchor(),b=nt(this.props,!1),g=nt(f,!1),x=Qb(Qb({},b),{},{fill:"none"},nt(l,!1)),w=d.map((function(t,n){var o=i.getTickLineCoord(t),c=o.line,w=o.tick,O=Qb(Qb(Qb(Qb({textAnchor:v,verticalAnchor:m},b),{},{stroke:"none",fill:s},g),w),{},{index:n,payload:t,visibleTicksCount:d.length,tickFormatter:p});return r().createElement(yt,Zb({className:"recharts-cartesian-axis-tick",key:"tick-".concat(t.value,"-").concat(t.coordinate,"-").concat(t.tickCoord)},F(i.props,t,n)),l&&r().createElement("line",Zb({},x,c,{className:a("recharts-cartesian-axis-tick-line",u()(l,"className"))})),f&&e.renderTickItem(f,O,"".concat(y()(p)?p(t.value,n):t.value).concat(h||"")))}));return r().createElement("g",{className:"recharts-cartesian-axis-ticks"},w)}},{key:"render",value:function(){var t=this,e=this.props,n=e.axisLine,o=e.width,i=e.height,c=e.ticksGenerator,u=e.className;if(e.hide)return null;var l=this.props,s=l.ticks,f=tg(l,Gb),p=s;return y()(c)&&(p=s&&s.length>0?c(this.props):c(f)),o<=0||i<=0||!p||!p.length?null:r().createElement(yt,{className:a("recharts-cartesian-axis",u),ref:function(e){t.layerReference=e}},n&&this.renderAxisLine(),this.renderTicks(p,this.state.fontSize,this.state.letterSpacing),Us.renderCallByParent(this.props))}}],i=[{key:"renderTickItem",value:function(t,e,n){var o=a(e.className,"recharts-cartesian-axis-tick-value");return r().isValidElement(t)?r().cloneElement(t,Qb(Qb({},e),{},{className:o})):y()(t)?t(Qb(Qb({},e),{},{className:o})):r().createElement(hn,Zb({},e,{className:"recharts-cartesian-axis-tick-value"}),n)}}],o&&eg(n.prototype,o),i&&eg(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,o,i}(e.Component);ag(ug,"displayName","CartesianAxis"),ag(ug,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var lg=["x1","y1","x2","y2","key"],sg=["offset"];function fg(t){return fg="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},fg(t)}function pg(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function hg(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?pg(Object(r),!0).forEach((function(e){yg(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):pg(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function yg(t,e,r){var n;return n=function(t,e){if("object"!=fg(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=fg(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==fg(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function dg(){return dg=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},dg.apply(this,arguments)}function vg(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var mg=function(t){var e=t.fill;if(!e||"none"===e)return null;var n=t.fillOpacity,o=t.x,i=t.y,a=t.width,c=t.height,u=t.ry;return r().createElement("rect",{x:o,y:i,ry:u,width:a,height:c,stroke:"none",fill:e,fillOpacity:n,className:"recharts-cartesian-grid-bg"})};function bg(t,e){var n;if(r().isValidElement(t))n=r().cloneElement(t,e);else if(y()(t))n=t(e);else{var o=e.x1,i=e.y1,a=e.x2,c=e.y2,u=e.key,l=vg(e,lg),s=nt(l,!1),f=(s.offset,vg(s,sg));n=r().createElement("line",dg({},f,{x1:o,y1:i,x2:a,y2:c,fill:"none",key:u}))}return n}function gg(t){var e=t.x,n=t.width,o=t.horizontal,i=void 0===o||o,a=t.horizontalPoints;if(!i||!a||!a.length)return null;var c=a.map((function(r,o){var a=hg(hg({},t),{},{x1:e,y1:r,x2:e+n,y2:r,key:"line-".concat(o),index:o});return bg(i,a)}));return r().createElement("g",{className:"recharts-cartesian-grid-horizontal"},c)}function xg(t){var e=t.y,n=t.height,o=t.vertical,i=void 0===o||o,a=t.verticalPoints;if(!i||!a||!a.length)return null;var c=a.map((function(r,o){var a=hg(hg({},t),{},{x1:r,y1:e,x2:r,y2:e+n,key:"line-".concat(o),index:o});return bg(i,a)}));return r().createElement("g",{className:"recharts-cartesian-grid-vertical"},c)}function wg(t){var e=t.horizontalFill,n=t.fillOpacity,o=t.x,i=t.y,a=t.width,c=t.height,u=t.horizontalPoints,l=t.horizontal;if(!(void 0===l||l)||!e||!e.length)return null;var s=u.map((function(t){return Math.round(t+i-i)})).sort((function(t,e){return t-e}));i!==s[0]&&s.unshift(0);var f=s.map((function(t,u){var l=!s[u+1]?i+c-t:s[u+1]-t;if(l<=0)return null;var f=u%e.length;return r().createElement("rect",{key:"react-".concat(u),y:t,x:o,height:l,width:a,stroke:"none",fill:e[f],fillOpacity:n,className:"recharts-cartesian-grid-bg"})}));return r().createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function Og(t){var e=t.vertical,n=void 0===e||e,o=t.verticalFill,i=t.fillOpacity,a=t.x,c=t.y,u=t.width,l=t.height,s=t.verticalPoints;if(!n||!o||!o.length)return null;var f=s.map((function(t){return Math.round(t+a-a)})).sort((function(t,e){return t-e}));a!==f[0]&&f.unshift(0);var p=f.map((function(t,e){var n=!f[e+1]?a+u-t:f[e+1]-t;if(n<=0)return null;var s=e%o.length;return r().createElement("rect",{key:"react-".concat(e),x:t,y:c,width:n,height:l,stroke:"none",fill:o[s],fillOpacity:i,className:"recharts-cartesian-grid-bg"})}));return r().createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},p)}var jg=function(t,e){var r=t.xAxis,n=t.width,o=t.height,i=t.offset;return Kl(Vb(hg(hg(hg({},ug.defaultProps),r),{},{ticks:ql(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.left,i.left+i.width,e)},Sg=function(t,e){var r=t.yAxis,n=t.width,o=t.height,i=t.offset;return Kl(Vb(hg(hg(hg({},ug.defaultProps),r),{},{ticks:ql(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.top,i.top+i.height,e)},Ag={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function Pg(t){var e,n,o,i,a,c,u=Qm(),l=tb(),s=Jm(),f=hg(hg({},t),{},{stroke:null!==(e=t.stroke)&&void 0!==e?e:Ag.stroke,fill:null!==(n=t.fill)&&void 0!==n?n:Ag.fill,horizontal:null!==(o=t.horizontal)&&void 0!==o?o:Ag.horizontal,horizontalFill:null!==(i=t.horizontalFill)&&void 0!==i?i:Ag.horizontalFill,vertical:null!==(a=t.vertical)&&void 0!==a?a:Ag.vertical,verticalFill:null!==(c=t.verticalFill)&&void 0!==c?c:Ag.verticalFill,x:S(t.x)?t.x:s.left,y:S(t.y)?t.y:s.top,width:S(t.width)?t.width:s.width,height:S(t.height)?t.height:s.height}),p=f.x,h=f.y,d=f.width,v=f.height,m=f.syncWithTicks,b=f.horizontalValues,g=f.verticalValues,x=Gm(),w=Ym();if(!S(d)||d<=0||!S(v)||v<=0||!S(p)||p!==+p||!S(h)||h!==+h)return null;var O=f.verticalCoordinatesGenerator||jg,j=f.horizontalCoordinatesGenerator||Sg,A=f.horizontalPoints,P=f.verticalPoints;if((!A||!A.length)&&y()(j)){var E=b&&b.length,k=j({yAxis:w?hg(hg({},w),{},{ticks:E?b:w.ticks}):void 0,width:u,height:l,offset:s},!!E||m);dt(Array.isArray(k),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(fg(k),"]")),Array.isArray(k)&&(A=k)}if((!P||!P.length)&&y()(O)){var T=g&&g.length,M=O({xAxis:x?hg(hg({},x),{},{ticks:T?g:x.ticks}):void 0,width:u,height:l,offset:s},!!T||m);dt(Array.isArray(M),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(fg(M),"]")),Array.isArray(M)&&(P=M)}return r().createElement("g",{className:"recharts-cartesian-grid"},r().createElement(mg,{fill:f.fill,fillOpacity:f.fillOpacity,x:f.x,y:f.y,width:f.width,height:f.height,ry:f.ry}),r().createElement(gg,dg({},f,{offset:s,horizontalPoints:A,xAxis:x,yAxis:w})),r().createElement(xg,dg({},f,{offset:s,verticalPoints:P,xAxis:x,yAxis:w})),r().createElement(wg,dg({},f,{horizontalPoints:A})),r().createElement(Og,dg({},f,{verticalPoints:P})))}Pg.displayName="CartesianGrid";var Eg=["type","layout","connectNulls","ref"],kg=["key"];function Tg(t){return Tg="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Tg(t)}function Mg(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function _g(){return _g=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},_g.apply(this,arguments)}function Cg(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Dg(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Cg(Object(r),!0).forEach((function(e){Ug(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Cg(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Ig(t){return function(t){if(Array.isArray(t))return Ng(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return Ng(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ng(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ng(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Rg(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Wg(n.key),n)}}function Bg(t,e,r){return e=zg(e),function(t,e){if(e&&("object"===Tg(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Lg()?Reflect.construct(e,r||[],zg(t).constructor):e.apply(t,r))}function Lg(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(Lg=function(){return!!t})()}function zg(t){return zg=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},zg(t)}function Fg(t,e){return Fg=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Fg(t,e)}function Ug(t,e,r){return(e=Wg(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Wg(t){var e=function(t,e){if("object"!=Tg(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Tg(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Tg(e)?e:e+""}var $g=function(t){function e(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return Ug(t=Bg(this,e,[].concat(n)),"state",{isAnimationFinished:!0,totalLength:0}),Ug(t,"generateSimpleStrokeDasharray",(function(t,e){return"".concat(e,"px ").concat(t-e,"px")})),Ug(t,"getStrokeDasharray",(function(r,n,o){var i=o.reduce((function(t,e){return t+e}));if(!i)return t.generateSimpleStrokeDasharray(n,r);for(var a=Math.floor(r/i),c=r%i,u=n-r,l=[],s=0,f=0;s<o.length;f+=o[s],++s)if(f+o[s]>c){l=[].concat(Ig(o.slice(0,s)),[c-f]);break}var p=l.length%2==0?[0,u]:[u];return[].concat(Ig(e.repeat(o,a)),Ig(l),p).map((function(t){return"".concat(t,"px")})).join(", ")})),Ug(t,"id",E("recharts-line-")),Ug(t,"pathRef",(function(e){t.mainCurve=e})),Ug(t,"handleAnimationEnd",(function(){t.setState({isAnimationFinished:!0}),t.props.onAnimationEnd&&t.props.onAnimationEnd()})),Ug(t,"handleAnimationStart",(function(){t.setState({isAnimationFinished:!1}),t.props.onAnimationStart&&t.props.onAnimationStart()})),t}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Fg(t,e)}(e,t),n=e,o=[{key:"componentDidMount",value:function(){if(this.props.isAnimationActive){var t=this.getTotalLength();this.setState({totalLength:t})}}},{key:"componentDidUpdate",value:function(){if(this.props.isAnimationActive){var t=this.getTotalLength();t!==this.state.totalLength&&this.setState({totalLength:t})}}},{key:"getTotalLength",value:function(){var t=this.mainCurve;try{return t&&t.getTotalLength&&t.getTotalLength()||0}catch(t){return 0}}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var n=this.props,o=n.points,i=n.xAxis,a=n.yAxis,c=n.layout,u=Y(n.children,Pl);if(!u)return null;var l=function(t,e){return{x:t.x,y:t.y,value:t.value,errorVal:Ll(t.payload,e)}},s={clipPath:t?"url(#clipPath-".concat(e,")"):null};return r().createElement(yt,s,u.map((function(t){return r().cloneElement(t,{key:"bar-".concat(t.props.dataKey),data:o,xAxis:i,yAxis:a,layout:c,dataPointFormatter:l})})))}},{key:"renderDots",value:function(t,n,o){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var i=this.props,a=i.dot,c=i.points,u=i.dataKey,l=nt(this.props,!1),s=nt(a,!0),f=c.map((function(t,r){var n=Dg(Dg(Dg({key:"dot-".concat(r),r:3},l),s),{},{index:r,cx:t.x,cy:t.y,value:t.value,dataKey:u,payload:t.payload,points:c});return e.renderDotItem(a,n)})),p={clipPath:t?"url(#clipPath-".concat(n?"":"dots-").concat(o,")"):null};return r().createElement(yt,_g({className:"recharts-line-dots",key:"dots"},p),f)}},{key:"renderCurveStatically",value:function(t,e,n,o){var i=this.props,a=i.type,c=i.layout,u=i.connectNulls,l=(i.ref,Mg(i,Eg)),s=Dg(Dg(Dg({},nt(l,!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:e?"url(#clipPath-".concat(n,")"):null,points:t},o),{},{type:a,layout:c,connectNulls:u});return r().createElement(Yf,_g({},s,{pathRef:this.pathRef}))}},{key:"renderCurveWithAnimation",value:function(t,e){var n=this,o=this.props,i=o.points,a=o.strokeDasharray,c=o.isAnimationActive,u=o.animationBegin,l=o.animationDuration,s=o.animationEasing,f=o.animationId,p=o.animateNewValues,h=o.width,y=o.height,d=this.state,v=d.prevPoints,m=d.totalLength;return r().createElement(Mh,{begin:u,duration:l,isActive:c,easing:s,from:{t:0},to:{t:1},key:"line-".concat(f),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},(function(r){var o=r.t;if(v){var c=v.length/i.length,u=i.map((function(t,e){var r=Math.floor(e*c);if(v[r]){var n=v[r],i=M(n.x,t.x),a=M(n.y,t.y);return Dg(Dg({},t),{},{x:i(o),y:a(o)})}if(p){var u=M(2*h,t.x),l=M(y/2,t.y);return Dg(Dg({},t),{},{x:u(o),y:l(o)})}return Dg(Dg({},t),{},{x:t.x,y:t.y})}));return n.renderCurveStatically(u,t,e)}var l,s=M(0,m)(o);if(a){var f="".concat(a).split(/[,\s]+/gim).map((function(t){return parseFloat(t)}));l=n.getStrokeDasharray(s,m,f)}else l=n.generateSimpleStrokeDasharray(m,s);return n.renderCurveStatically(i,t,e,{strokeDasharray:l})}))}},{key:"renderCurve",value:function(t,e){var r=this.props,n=r.points,o=r.isAnimationActive,i=this.state,a=i.prevPoints,c=i.totalLength;return o&&n&&n.length&&(!a&&c>0||!Nu()(a,n))?this.renderCurveWithAnimation(t,e):this.renderCurveStatically(n,t,e)}},{key:"render",value:function(){var t,e=this.props,n=e.hide,o=e.dot,i=e.points,c=e.className,u=e.xAxis,l=e.yAxis,f=e.top,p=e.left,h=e.width,y=e.height,d=e.isAnimationActive,v=e.id;if(n||!i||!i.length)return null;var m=this.state.isAnimationFinished,b=1===i.length,g=a("recharts-line",c),x=u&&u.allowDataOverflow,w=l&&l.allowDataOverflow,O=x||w,j=s()(v)?this.id:v,S=null!==(t=nt(o,!1))&&void 0!==t?t:{r:3,strokeWidth:2},A=S.r,P=void 0===A?3:A,E=S.strokeWidth,k=void 0===E?2:E,T=(et(o)?o:{}).clipDot,M=void 0===T||T,_=2*P+k;return r().createElement(yt,{className:g},x||w?r().createElement("defs",null,r().createElement("clipPath",{id:"clipPath-".concat(j)},r().createElement("rect",{x:x?p:p-h/2,y:w?f:f-y/2,width:x?h:2*h,height:w?y:2*y})),!M&&r().createElement("clipPath",{id:"clipPath-dots-".concat(j)},r().createElement("rect",{x:p-_/2,y:f-_/2,width:h+_,height:y+_}))):null,!b&&this.renderCurve(O,j),this.renderErrorBar(O,j),(b||o)&&this.renderDots(O,M,j),(!d||m)&&rf.renderCallByParent(this.props,i))}}],i=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curPoints:t.points,prevPoints:e.curPoints}:t.points!==e.curPoints?{curPoints:t.points}:null}},{key:"repeat",value:function(t,e){for(var r=t.length%2!=0?[].concat(Ig(t),[0]):t,n=[],o=0;o<e;++o)n=[].concat(Ig(n),Ig(r));return n}},{key:"renderDotItem",value:function(t,e){var n;if(r().isValidElement(t))n=r().cloneElement(t,e);else if(y()(t))n=t(e);else{var o=e.key,i=Mg(e,kg),c=a("recharts-line-dot","boolean"!=typeof t?t.className:"");n=r().createElement(Zh,_g({key:o},i,{className:c}))}return n}}],o&&Rg(n.prototype,o),i&&Rg(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,o,i}(e.PureComponent);Ug($g,"displayName","Line"),Ug($g,"defaultProps",{xAxisId:0,yAxisId:0,connectNulls:!1,activeDot:!0,dot:!0,legendType:"line",stroke:"#3182bd",strokeWidth:1,fill:"#fff",points:[],isAnimationActive:!cr.isSsr,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",hide:!1,label:!1}),Ug($g,"getComposedData",(function(t){var e=t.props,r=t.xAxis,n=t.yAxis,o=t.xAxisTicks,i=t.yAxisTicks,a=t.dataKey,c=t.bandSize,u=t.displayedData,l=t.offset,f=e.layout,p=u.map((function(t,e){var u=Ll(t,a);return"horizontal"===f?{x:rs({axis:r,ticks:o,bandSize:c,entry:t,index:e}),y:s()(u)?null:n.scale(u),value:u,payload:t}:{x:s()(u)?null:r.scale(u),y:rs({axis:n,ticks:i,bandSize:c,entry:t,index:e}),value:u,payload:t}}));return Dg({points:p,layout:f},l)}));var Kg,qg=["layout","type","stroke","connectNulls","isRange","ref"],Vg=["key"];function Xg(t){return Xg="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Xg(t)}function Hg(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function Gg(){return Gg=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Gg.apply(this,arguments)}function Yg(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Zg(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Yg(Object(r),!0).forEach((function(e){nx(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Yg(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Jg(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ox(n.key),n)}}function Qg(t,e,r){return e=ex(e),function(t,e){if(e&&("object"===Xg(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,tx()?Reflect.construct(e,r||[],ex(t).constructor):e.apply(t,r))}function tx(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(tx=function(){return!!t})()}function ex(t){return ex=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},ex(t)}function rx(t,e){return rx=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},rx(t,e)}function nx(t,e,r){return(e=ox(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ox(t){var e=function(t,e){if("object"!=Xg(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Xg(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Xg(e)?e:e+""}var ix=function(t){function e(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return nx(t=Qg(this,e,[].concat(n)),"state",{isAnimationFinished:!0}),nx(t,"id",E("recharts-area-")),nx(t,"handleAnimationEnd",(function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),y()(e)&&e()})),nx(t,"handleAnimationStart",(function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),y()(e)&&e()})),t}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&rx(t,e)}(e,t),n=e,o=[{key:"renderDots",value:function(t,n,o){var i=this.props.isAnimationActive,a=this.state.isAnimationFinished;if(i&&!a)return null;var c=this.props,u=c.dot,l=c.points,s=c.dataKey,f=nt(this.props,!1),p=nt(u,!0),h=l.map((function(t,r){var n=Zg(Zg(Zg({key:"dot-".concat(r),r:3},f),p),{},{index:r,cx:t.x,cy:t.y,dataKey:s,value:t.value,payload:t.payload,points:l});return e.renderDotItem(u,n)})),y={clipPath:t?"url(#clipPath-".concat(n?"":"dots-").concat(o,")"):null};return r().createElement(yt,Gg({className:"recharts-area-dots"},y),h)}},{key:"renderHorizontalRect",value:function(t){var e=this.props,n=e.baseLine,o=e.points,i=e.strokeWidth,a=o[0].x,c=o[o.length-1].x,u=t*Math.abs(a-c),l=Tu()(o.map((function(t){return t.y||0})));return S(n)&&"number"==typeof n?l=Math.max(n,l):n&&Array.isArray(n)&&n.length&&(l=Math.max(Tu()(n.map((function(t){return t.y||0}))),l)),S(l)?r().createElement("rect",{x:a<c?a:a-u,y:0,width:u,height:Math.floor(l+(i?parseInt("".concat(i),10):1))}):null}},{key:"renderVerticalRect",value:function(t){var e=this.props,n=e.baseLine,o=e.points,i=e.strokeWidth,a=o[0].y,c=o[o.length-1].y,u=t*Math.abs(a-c),l=Tu()(o.map((function(t){return t.x||0})));return S(n)&&"number"==typeof n?l=Math.max(n,l):n&&Array.isArray(n)&&n.length&&(l=Math.max(Tu()(n.map((function(t){return t.x||0}))),l)),S(l)?r().createElement("rect",{x:0,y:a<c?a:a-u,width:l+(i?parseInt("".concat(i),10):1),height:Math.floor(u)}):null}},{key:"renderClipRect",value:function(t){return"vertical"===this.props.layout?this.renderVerticalRect(t):this.renderHorizontalRect(t)}},{key:"renderAreaStatically",value:function(t,e,n,o){var i=this.props,a=i.layout,c=i.type,u=i.stroke,l=i.connectNulls,s=i.isRange,f=(i.ref,Hg(i,qg));return r().createElement(yt,{clipPath:n?"url(#clipPath-".concat(o,")"):null},r().createElement(Yf,Gg({},nt(f,!0),{points:t,connectNulls:l,type:c,baseLine:e,layout:a,stroke:"none",className:"recharts-area-area"})),"none"!==u&&r().createElement(Yf,Gg({},nt(this.props,!1),{className:"recharts-area-curve",layout:a,type:c,connectNulls:l,fill:"none",points:t})),"none"!==u&&s&&r().createElement(Yf,Gg({},nt(this.props,!1),{className:"recharts-area-curve",layout:a,type:c,connectNulls:l,fill:"none",points:e})))}},{key:"renderAreaWithAnimation",value:function(t,e){var n=this,o=this.props,i=o.points,a=o.baseLine,c=o.isAnimationActive,u=o.animationBegin,l=o.animationDuration,f=o.animationEasing,p=o.animationId,h=this.state,y=h.prevPoints,d=h.prevBaseLine;return r().createElement(Mh,{begin:u,duration:l,isActive:c,easing:f,from:{t:0},to:{t:1},key:"area-".concat(p),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},(function(o){var c=o.t;if(y){var u,l=y.length/i.length,f=i.map((function(t,e){var r=Math.floor(e*l);if(y[r]){var n=y[r],o=M(n.x,t.x),i=M(n.y,t.y);return Zg(Zg({},t),{},{x:o(c),y:i(c)})}return t}));return u=S(a)&&"number"==typeof a?M(d,a)(c):s()(a)||g()(a)?M(d,0)(c):a.map((function(t,e){var r=Math.floor(e*l);if(d[r]){var n=d[r],o=M(n.x,t.x),i=M(n.y,t.y);return Zg(Zg({},t),{},{x:o(c),y:i(c)})}return t})),n.renderAreaStatically(f,u,t,e)}return r().createElement(yt,null,r().createElement("defs",null,r().createElement("clipPath",{id:"animationClipPath-".concat(e)},n.renderClipRect(c))),r().createElement(yt,{clipPath:"url(#animationClipPath-".concat(e,")")},n.renderAreaStatically(i,a,t,e)))}))}},{key:"renderArea",value:function(t,e){var r=this.props,n=r.points,o=r.baseLine,i=r.isAnimationActive,a=this.state,c=a.prevPoints,u=a.prevBaseLine,l=a.totalLength;return i&&n&&n.length&&(!c&&l>0||!Nu()(c,n)||!Nu()(u,o))?this.renderAreaWithAnimation(t,e):this.renderAreaStatically(n,o,t,e)}},{key:"render",value:function(){var t,e=this.props,n=e.hide,o=e.dot,i=e.points,c=e.className,u=e.top,l=e.left,f=e.xAxis,p=e.yAxis,h=e.width,y=e.height,d=e.isAnimationActive,v=e.id;if(n||!i||!i.length)return null;var m=this.state.isAnimationFinished,b=1===i.length,g=a("recharts-area",c),x=f&&f.allowDataOverflow,w=p&&p.allowDataOverflow,O=x||w,j=s()(v)?this.id:v,S=null!==(t=nt(o,!1))&&void 0!==t?t:{r:3,strokeWidth:2},A=S.r,P=void 0===A?3:A,E=S.strokeWidth,k=void 0===E?2:E,T=(et(o)?o:{}).clipDot,M=void 0===T||T,_=2*P+k;return r().createElement(yt,{className:g},x||w?r().createElement("defs",null,r().createElement("clipPath",{id:"clipPath-".concat(j)},r().createElement("rect",{x:x?l:l-h/2,y:w?u:u-y/2,width:x?h:2*h,height:w?y:2*y})),!M&&r().createElement("clipPath",{id:"clipPath-dots-".concat(j)},r().createElement("rect",{x:l-_/2,y:u-_/2,width:h+_,height:y+_}))):null,b?null:this.renderArea(O,j),(o||b)&&this.renderDots(O,M,j),(!d||m)&&rf.renderCallByParent(this.props,i))}}],i=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curPoints:t.points,curBaseLine:t.baseLine,prevPoints:e.curPoints,prevBaseLine:e.curBaseLine}:t.points!==e.curPoints||t.baseLine!==e.curBaseLine?{curPoints:t.points,curBaseLine:t.baseLine}:null}}],o&&Jg(n.prototype,o),i&&Jg(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,o,i}(e.PureComponent);function ax(t){return ax="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ax(t)}function cx(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,hx(n.key),n)}}function ux(t,e,r){return e=sx(e),function(t,e){if(e&&("object"===ax(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,lx()?Reflect.construct(e,r||[],sx(t).constructor):e.apply(t,r))}function lx(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(lx=function(){return!!t})()}function sx(t){return sx=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},sx(t)}function fx(t,e){return fx=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},fx(t,e)}function px(t,e,r){return(e=hx(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function hx(t){var e=function(t,e){if("object"!=ax(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ax(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ax(e)?e:e+""}Kg=ix,nx(ix,"displayName","Area"),nx(ix,"defaultProps",{stroke:"#3182bd",fill:"#3182bd",fillOpacity:.6,xAxisId:0,yAxisId:0,legendType:"line",connectNulls:!1,points:[],dot:!1,activeDot:!0,hide:!1,isAnimationActive:!cr.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"ease"}),nx(ix,"getBaseValue",(function(t,e,r,n){var o=t.layout,i=t.baseValue,a=e.props.baseValue,c=null!=a?a:i;if(S(c)&&"number"==typeof c)return c;var u="horizontal"===o?n:r,l=u.scale.domain();if("number"===u.type){var s=Math.max(l[0],l[1]),f=Math.min(l[0],l[1]);return"dataMin"===c?f:"dataMax"===c||s<0?s:Math.max(Math.min(l[0],l[1]),0)}return"dataMin"===c?l[0]:"dataMax"===c?l[1]:l[0]})),nx(ix,"getComposedData",(function(t){var e,r=t.props,n=t.item,o=t.xAxis,i=t.yAxis,a=t.xAxisTicks,c=t.yAxisTicks,u=t.bandSize,l=t.dataKey,s=t.stackedData,f=t.dataStartIndex,p=t.displayedData,h=t.offset,y=r.layout,d=s&&s.length,v=Kg.getBaseValue(r,n,o,i),m="horizontal"===y,b=!1,g=p.map((function(t,e){var r;d?r=s[f+e]:(r=Ll(t,l),Array.isArray(r)?b=!0:r=[v,r]);var n=null==r[1]||d&&null==Ll(t,l);return m?{x:rs({axis:o,ticks:a,bandSize:u,entry:t,index:e}),y:n?null:i.scale(r[1]),value:r,payload:t}:{x:n?null:o.scale(r[1]),y:rs({axis:i,ticks:c,bandSize:u,entry:t,index:e}),value:r,payload:t}}));return e=d||b?g.map((function(t){var e=Array.isArray(t.value)?t.value[0]:null;return m?{x:t.x,y:null!=e&&null!=t.y?i.scale(e):null}:{x:null!=e?o.scale(e):null,y:t.y}})):m?i.scale(v):o.scale(v),Zg({points:g,baseLine:e,layout:y,isRange:b},h)})),nx(ix,"renderDotItem",(function(t,e){var n;if(r().isValidElement(t))n=r().cloneElement(t,e);else if(y()(t))n=t(e);else{var o=a("recharts-area-dot","boolean"!=typeof t?t.className:""),i=e.key,c=Hg(e,Vg);n=r().createElement(Zh,Gg({},c,{key:i,className:o}))}return n}));var yx=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),ux(this,e,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&fx(t,e)}(e,t),r=e,(n=[{key:"render",value:function(){return null}}])&&cx(r.prototype,n),o&&cx(r,o),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n,o}(e.Component);px(yx,"displayName","ZAxis"),px(yx,"defaultProps",{zAxisId:0,range:[64,64],scale:"auto",type:"number"});var dx=["option","isActive"];function vx(){return vx=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},vx.apply(this,arguments)}function mx(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function bx(t){var r=t.option,n=t.isActive,o=mx(t,dx);return"string"==typeof r?e.createElement(Od,vx({option:e.createElement(ne,vx({type:r},o)),isActive:n,shapeType:"symbols"},o)):e.createElement(Od,vx({option:r,isActive:n,shapeType:"symbols"},o))}function gx(t){return gx="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},gx(t)}function xx(){return xx=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},xx.apply(this,arguments)}function wx(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Ox(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?wx(Object(r),!0).forEach((function(e){kx(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):wx(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function jx(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Tx(n.key),n)}}function Sx(t,e,r){return e=Px(e),function(t,e){if(e&&("object"===gx(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Ax()?Reflect.construct(e,r||[],Px(t).constructor):e.apply(t,r))}function Ax(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(Ax=function(){return!!t})()}function Px(t){return Px=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Px(t)}function Ex(t,e){return Ex=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Ex(t,e)}function kx(t,e,r){return(e=Tx(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Tx(t){var e=function(t,e){if("object"!=gx(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=gx(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==gx(e)?e:e+""}var Mx=function(t){function e(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return kx(t=Sx(this,e,[].concat(n)),"state",{isAnimationFinished:!1}),kx(t,"handleAnimationEnd",(function(){t.setState({isAnimationFinished:!0})})),kx(t,"handleAnimationStart",(function(){t.setState({isAnimationFinished:!1})})),kx(t,"id",E("recharts-scatter-")),t}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Ex(t,e)}(e,t),n=e,o=[{key:"renderSymbolsStatically",value:function(t){var e=this,n=this.props,o=n.shape,i=n.activeShape,a=n.activeIndex,c=nt(this.props,!1);return t.map((function(t,n){var u=a===n,l=u?i:o,s=Ox(Ox({},c),t);return r().createElement(yt,xx({className:"recharts-scatter-symbol",key:"symbol-".concat(null==t?void 0:t.cx,"-").concat(null==t?void 0:t.cy,"-").concat(null==t?void 0:t.size,"-").concat(n)},F(e.props,t,n),{role:"img"}),r().createElement(bx,xx({option:l,isActive:u,key:"symbol-".concat(n)},s)))}))}},{key:"renderSymbolsWithAnimation",value:function(){var t=this,e=this.props,n=e.points,o=e.isAnimationActive,i=e.animationBegin,a=e.animationDuration,c=e.animationEasing,u=e.animationId,l=this.state.prevPoints;return r().createElement(Mh,{begin:i,duration:a,isActive:o,easing:c,from:{t:0},to:{t:1},key:"pie-".concat(u),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},(function(e){var o=e.t,i=n.map((function(t,e){var r=l&&l[e];if(r){var n=M(r.cx,t.cx),i=M(r.cy,t.cy),a=M(r.size,t.size);return Ox(Ox({},t),{},{cx:n(o),cy:i(o),size:a(o)})}var c=M(0,t.size);return Ox(Ox({},t),{},{size:c(o)})}));return r().createElement(yt,null,t.renderSymbolsStatically(i))}))}},{key:"renderSymbols",value:function(){var t=this.props,e=t.points,r=t.isAnimationActive,n=this.state.prevPoints;return!(r&&e&&e.length)||n&&Nu()(n,e)?this.renderSymbolsStatically(e):this.renderSymbolsWithAnimation()}},{key:"renderErrorBar",value:function(){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var t=this.props,e=t.points,n=t.xAxis,o=t.yAxis,i=Y(t.children,Pl);return i?i.map((function(t,i){var a=t.props,c=a.direction,u=a.dataKey;return r().cloneElement(t,{key:"".concat(c,"-").concat(u,"-").concat(e[i]),data:e,xAxis:n,yAxis:o,layout:"x"===c?"vertical":"horizontal",dataPointFormatter:function(t,e){return{x:t.cx,y:t.cy,value:"x"===c?+t.node.x:+t.node.y,errorVal:Ll(t,e)}}})})):null}},{key:"renderLine",value:function(){var t,e,n=this.props,o=n.points,i=n.line,a=n.lineType,c=n.lineJointType,u=nt(this.props,!1),l=nt(i,!1);if("joint"===a)t=o.map((function(t){return{x:t.cx,y:t.cy}}));else if("fitting"===a){var s=function(t){if(!t||!t.length)return null;for(var e=t.length,r=0,n=0,o=0,i=0,a=1/0,c=-1/0,u=0,l=0,s=0;s<e;s++)r+=u=t[s].cx||0,n+=l=t[s].cy||0,o+=u*l,i+=u*u,a=Math.min(a,u),c=Math.max(c,u);var f=e*i!=r*r?(e*o-r*n)/(e*i-r*r):0;return{xmin:a,xmax:c,a:f,b:(n-f*r)/e}}(o),f=s.xmin,p=s.xmax,h=s.a,d=s.b,v=function(t){return h*t+d};t=[{x:f,y:v(f)},{x:p,y:v(p)}]}var m=Ox(Ox(Ox({},u),{},{fill:"none",stroke:u&&u.fill},l),{},{points:t});return e=r().isValidElement(i)?r().cloneElement(i,m):y()(i)?i(m):r().createElement(Yf,xx({},m,{type:c})),r().createElement(yt,{className:"recharts-scatter-line",key:"recharts-scatter-line"},e)}},{key:"render",value:function(){var t=this.props,e=t.hide,n=t.points,o=t.line,i=t.className,c=t.xAxis,u=t.yAxis,l=t.left,f=t.top,p=t.width,h=t.height,y=t.id,d=t.isAnimationActive;if(e||!n||!n.length)return null;var v=this.state.isAnimationFinished,m=a("recharts-scatter",i),b=c&&c.allowDataOverflow,g=u&&u.allowDataOverflow,x=b||g,w=s()(y)?this.id:y;return r().createElement(yt,{className:m,clipPath:x?"url(#clipPath-".concat(w,")"):null},b||g?r().createElement("defs",null,r().createElement("clipPath",{id:"clipPath-".concat(w)},r().createElement("rect",{x:b?l:l-p/2,y:g?f:f-h/2,width:b?p:2*p,height:g?h:2*h}))):null,o&&this.renderLine(),this.renderErrorBar(),r().createElement(yt,{key:"recharts-scatter-symbols"},this.renderSymbols()),(!d||v)&&rf.renderCallByParent(this.props,n))}}],i=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curPoints:t.points,prevPoints:e.curPoints}:t.points!==e.curPoints?{curPoints:t.points}:null}}],o&&jx(n.prototype,o),i&&jx(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,o,i}(e.PureComponent);function _x(t){return _x="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_x(t)}function Cx(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Lx(n.key),n)}}function Dx(t,e,r){return e=Nx(e),function(t,e){if(e&&("object"===_x(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Ix()?Reflect.construct(e,r||[],Nx(t).constructor):e.apply(t,r))}function Ix(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(Ix=function(){return!!t})()}function Nx(t){return Nx=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Nx(t)}function Rx(t,e){return Rx=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Rx(t,e)}function Bx(t,e,r){return(e=Lx(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Lx(t){var e=function(t,e){if("object"!=_x(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=_x(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==_x(e)?e:e+""}function zx(){return zx=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},zx.apply(this,arguments)}function Fx(t){var r=t.xAxisId,n=Qm(),o=tb(),i=Hm(r);return null==i?null:e.createElement(ug,zx({},i,{className:a("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:n,height:o},ticksGenerator:function(t){return ql(t,!0)}}))}kx(Mx,"displayName","Scatter"),kx(Mx,"defaultProps",{xAxisId:0,yAxisId:0,zAxisId:0,legendType:"circle",lineType:"joint",lineJointType:"linear",data:[],shape:"circle",hide:!1,isAnimationActive:!cr.isSsr,animationBegin:0,animationDuration:400,animationEasing:"linear"}),kx(Mx,"getComposedData",(function(t){var e=t.xAxis,r=t.yAxis,n=t.zAxis,o=t.item,i=t.displayedData,a=t.xAxisTicks,c=t.yAxisTicks,u=t.offset,l=o.props.tooltipType,f=Y(o.props.children,Tr),p=s()(e.dataKey)?o.props.dataKey:e.dataKey,h=s()(r.dataKey)?o.props.dataKey:r.dataKey,y=n&&n.dataKey,d=n?n.range:yx.defaultProps.range,v=d&&d[0],m=e.scale.bandwidth?e.scale.bandwidth():0,b=r.scale.bandwidth?r.scale.bandwidth():0,g=i.map((function(t,i){var u=Ll(t,p),d=Ll(t,h),g=!s()(y)&&Ll(t,y)||"-",x=[{name:s()(e.dataKey)?o.props.name:e.name||e.dataKey,unit:e.unit||"",value:u,payload:t,dataKey:p,type:l},{name:s()(r.dataKey)?o.props.name:r.name||r.dataKey,unit:r.unit||"",value:d,payload:t,dataKey:h,type:l}];"-"!==g&&x.push({name:n.name||n.dataKey,unit:n.unit||"",value:g,payload:t,dataKey:y,type:l});var w=rs({axis:e,ticks:a,bandSize:m,entry:t,index:i,dataKey:p}),O=rs({axis:r,ticks:c,bandSize:b,entry:t,index:i,dataKey:h}),j="-"!==g?n.scale(g):v,S=Math.sqrt(Math.max(j,0)/Math.PI);return Ox(Ox({},t),{},{cx:w,cy:O,x:w-S,y:O-S,xAxis:e,yAxis:r,zAxis:n,width:2*S,height:2*S,size:j,node:{x:u,y:d,z:g},tooltipPayload:x,tooltipPosition:{x:w,y:O},payload:t},f&&f[i]&&f[i].props)}));return Ox({points:g},u)}));var Ux=function(t){function r(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,r),Dx(this,r,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Rx(t,e)}(r,t),n=r,(o=[{key:"render",value:function(){return e.createElement(Fx,this.props)}}])&&Cx(n.prototype,o),i&&Cx(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,o,i}(e.Component);function Wx(t){return Wx="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Wx(t)}function $x(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Gx(n.key),n)}}function Kx(t,e,r){return e=Vx(e),function(t,e){if(e&&("object"===Wx(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,qx()?Reflect.construct(e,r||[],Vx(t).constructor):e.apply(t,r))}function qx(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(qx=function(){return!!t})()}function Vx(t){return Vx=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Vx(t)}function Xx(t,e){return Xx=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Xx(t,e)}function Hx(t,e,r){return(e=Gx(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Gx(t){var e=function(t,e){if("object"!=Wx(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Wx(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Wx(e)?e:e+""}function Yx(){return Yx=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Yx.apply(this,arguments)}Bx(Ux,"displayName","XAxis"),Bx(Ux,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});var Zx=function(t){var r=t.yAxisId,n=Qm(),o=tb(),i=Zm(r);return null==i?null:e.createElement(ug,Yx({},i,{className:a("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:n,height:o},ticksGenerator:function(t){return ql(t,!0)}}))},Jx=function(t){function r(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,r),Kx(this,r,arguments)}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Xx(t,e)}(r,t),n=r,(o=[{key:"render",value:function(){return e.createElement(Zx,this.props)}}])&&$x(n.prototype,o),i&&$x(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,o,i}(e.Component);function Qx(t){return function(t){if(Array.isArray(t))return tw(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return tw(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tw(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function tw(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}Hx(Jx,"displayName","YAxis"),Hx(Jx,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});var ew=function(t,e,r,n,o){var i=Y(t,db),a=Y(t,Eb),c=[].concat(Qx(i),Qx(a)),u=Y(t,zb),l="".concat(n,"Id"),s=n[0],f=e;if(c.length&&(f=c.reduce((function(t,e){if(e.props[l]===r&&Gv(e.props,"extendDomain")&&S(e.props[s])){var n=e.props[s];return[Math.min(t[0],n),Math.max(t[1],n)]}return t}),f)),u.length){var p="".concat(s,"1"),h="".concat(s,"2");f=u.reduce((function(t,e){if(e.props[l]===r&&Gv(e.props,"extendDomain")&&S(e.props[p])&&S(e.props[h])){var n=e.props[p],o=e.props[h];return[Math.min(t[0],n,o),Math.max(t[1],n,o)]}return t}),f)}return o&&o.length&&(f=o.reduce((function(t,e){return S(e)?[Math.min(t[0],e),Math.max(t[1],e)]:t}),f)),f},rw=o(9328),nw=new(o.n(rw)()),ow="recharts.syncMouseEvents";function iw(t){return iw="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},iw(t)}function aw(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,uw(n.key),n)}}function cw(t,e,r){return(e=uw(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function uw(t){var e=function(t,e){if("object"!=iw(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=iw(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==iw(e)?e:e+""}var lw=function(){return t=function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),cw(this,"activeIndex",0),cw(this,"coordinateList",[]),cw(this,"layout","horizontal")},(e=[{key:"setDetails",value:function(t){var e,r=t.coordinateList,n=void 0===r?null:r,o=t.container,i=void 0===o?null:o,a=t.layout,c=void 0===a?null:a,u=t.offset,l=void 0===u?null:u,s=t.mouseHandlerCallback,f=void 0===s?null:s;this.coordinateList=null!==(e=null!=n?n:this.coordinateList)&&void 0!==e?e:[],this.container=null!=i?i:this.container,this.layout=null!=c?c:this.layout,this.offset=null!=l?l:this.offset,this.mouseHandlerCallback=null!=f?f:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(t){if(0!==this.coordinateList.length)switch(t.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(t){this.activeIndex=t}},{key:"spoofMouse",value:function(){var t,e;if("horizontal"===this.layout&&0!==this.coordinateList.length){var r=this.container.getBoundingClientRect(),n=r.x,o=r.y,i=r.height,a=this.coordinateList[this.activeIndex].coordinate,c=(null===(t=window)||void 0===t?void 0:t.scrollX)||0,u=(null===(e=window)||void 0===e?void 0:e.scrollY)||0,l=n+a+c,s=o+this.offset.top+i/2+u;this.mouseHandlerCallback({pageX:l,pageY:s})}}}])&&aw(t.prototype,e),r&&aw(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e,r}();function sw(t){var e=t.cx,r=t.cy,n=t.radius,o=t.startAngle,i=t.endAngle;return{points:[xs(e,r,n,o),xs(e,r,n,i)],cx:e,cy:r,radius:n,startAngle:o,endAngle:i}}function fw(t,e,r){var n,o,i,a;if("horizontal"===t)i=n=e.x,o=r.top,a=r.top+r.height;else if("vertical"===t)a=o=e.y,n=r.left,i=r.left+r.width;else if(null!=e.cx&&null!=e.cy){if("centric"!==t)return sw(e);var c=e.cx,u=e.cy,l=e.innerRadius,s=e.outerRadius,f=e.angle,p=xs(c,u,l,f),h=xs(c,u,s,f);n=p.x,o=p.y,i=h.x,a=h.y}return[{x:n,y:o},{x:i,y:a}]}function pw(t){return pw="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},pw(t)}function hw(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function yw(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hw(Object(r),!0).forEach((function(e){dw(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hw(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function dw(t,e,r){var n;return n=function(t,e){if("object"!=pw(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=pw(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==pw(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function vw(t){var r,n,o,i=t.element,c=t.tooltipEventType,u=t.isActive,l=t.activeCoordinate,s=t.activePayload,f=t.offset,p=t.activeTooltipIndex,h=t.tooltipAxisBandSize,y=t.layout,d=t.chartName,v=null!==(r=i.props.cursor)&&void 0!==r?r:null===(n=i.type.defaultProps)||void 0===n?void 0:n.cursor;if(!i||!v||!u||!l||"ScatterChart"!==d&&"axis"!==c)return null;var m=Yf;if("ScatterChart"===d)o=l,m=iy;else if("BarChart"===d)o=function(t,e,r,n){var o=n/2;return{stroke:"none",fill:"#ccc",x:"horizontal"===t?e.x-o:r.left+.5,y:"horizontal"===t?r.top+.5:e.y-o,width:"horizontal"===t?n:r.width-1,height:"horizontal"===t?r.height-1:n}}(y,l,f,h),m=Uh;else if("radial"===y){var b=sw(l),g=b.cx,x=b.cy,w=b.radius;o={cx:g,cy:x,startAngle:b.startAngle,endAngle:b.endAngle,innerRadius:w,outerRadius:w},m=vf}else o={points:fw(y,l,f)},m=Yf;var O=yw(yw(yw(yw({stroke:"#ccc",pointerEvents:"none"},f),o),nt(v,!1)),{},{payload:s,payloadIndex:p,className:a("recharts-tooltip-cursor",v.className)});return(0,e.isValidElement)(v)?(0,e.cloneElement)(v,O):(0,e.createElement)(m,O)}var mw=["item"],bw=["children","className","width","height","style","compact","title","desc"];function gw(t){return gw="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},gw(t)}function xw(){return xw=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},xw.apply(this,arguments)}function ww(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||Tw(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ow(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function jw(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Iw(n.key),n)}}function Sw(t,e,r){return e=Pw(e),function(t,e){if(e&&("object"===gw(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Aw()?Reflect.construct(e,r||[],Pw(t).constructor):e.apply(t,r))}function Aw(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(Aw=function(){return!!t})()}function Pw(t){return Pw=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Pw(t)}function Ew(t,e){return Ew=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Ew(t,e)}function kw(t){return function(t){if(Array.isArray(t))return Mw(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||Tw(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Tw(t,e){if(t){if("string"==typeof t)return Mw(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Mw(t,e):void 0}}function Mw(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function _w(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Cw(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?_w(Object(r),!0).forEach((function(e){Dw(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):_w(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Dw(t,e,r){return(e=Iw(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Iw(t){var e=function(t,e){if("object"!=gw(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=gw(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==gw(e)?e:e+""}var Nw={xAxis:["bottom","top"],yAxis:["left","right"]},Rw={width:"100%",height:"100%"},Bw={x:0,y:0};function Lw(t){return t}var zw=function(t,e){var r=e.graphicalItems,n=e.dataStartIndex,o=e.dataEndIndex,i=(null!=r?r:[]).reduce((function(t,e){var r=e.props.data;return r&&r.length?[].concat(kw(t),kw(r)):t}),[]);return i.length>0?i:t&&t.length&&S(n)&&S(o)?t.slice(n,o+1):[]};function Fw(t){return"number"===t?[0,"auto"]:void 0}var Uw=function(t,e,r,n){var o=t.graphicalItems,i=t.tooltipAxis,a=zw(e,t);return r<0||!o||!o.length||r>=a.length?null:o.reduce((function(o,c){var u,l,s=null!==(u=c.props.data)&&void 0!==u?u:e;(s&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=r&&(s=s.slice(t.dataStartIndex,t.dataEndIndex+1)),i.dataKey&&!i.allowDuplicatedCategory)?l=_(void 0===s?a:s,i.dataKey,n):l=s&&s[r]||a[r];return l?[].concat(kw(o),[fs(c,l)]):o}),[])},Ww=function(t,e,r,n){var o=n||{x:t.chartX,y:t.chartY},i=function(t,e){return"horizontal"===e?t.x:"vertical"===e?t.y:"centric"===e?t.angle:t.radius}(o,r),a=t.orderedTooltipTicks,c=t.tooltipAxis,u=t.tooltipTicks,l=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0,i=-1,a=null!==(e=null==r?void 0:r.length)&&void 0!==e?e:0;if(a<=1)return 0;if(o&&"angleAxis"===o.axisType&&Math.abs(Math.abs(o.range[1]-o.range[0])-360)<=1e-6)for(var c=o.range,u=0;u<a;u++){var l=u>0?n[u-1].coordinate:n[a-1].coordinate,s=n[u].coordinate,f=u>=a-1?n[0].coordinate:n[u+1].coordinate,p=void 0;if(O(s-l)!==O(f-s)){var h=[];if(O(f-s)===O(c[1]-c[0])){p=f;var y=s+c[1]-c[0];h[0]=Math.min(y,(y+l)/2),h[1]=Math.max(y,(y+l)/2)}else{p=l;var d=f+c[1]-c[0];h[0]=Math.min(s,(d+s)/2),h[1]=Math.max(s,(d+s)/2)}var v=[Math.min(s,(p+s)/2),Math.max(s,(p+s)/2)];if(t>v[0]&&t<=v[1]||t>=h[0]&&t<=h[1]){i=n[u].index;break}}else{var m=Math.min(l,f),b=Math.max(l,f);if(t>(m+s)/2&&t<=(b+s)/2){i=n[u].index;break}}}else for(var g=0;g<a;g++)if(0===g&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g>0&&g<a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g===a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2){i=r[g].index;break}return i}(i,a,u,c);if(l>=0&&u){var s=u[l]&&u[l].value,f=Uw(t,e,l,s),p=function(t,e,r,n){var o=e.find((function(t){return t&&t.index===r}));if(o){if("horizontal"===t)return{x:o.coordinate,y:n.y};if("vertical"===t)return{x:n.x,y:o.coordinate};if("centric"===t){var i=o.coordinate,a=n.radius;return Cw(Cw(Cw({},n),xs(n.cx,n.cy,a,i)),{},{angle:i,radius:a})}var c=o.coordinate,u=n.angle;return Cw(Cw(Cw({},n),xs(n.cx,n.cy,c,u)),{},{angle:u,radius:c})}return Bw}(r,a,l,o);return{activeTooltipIndex:l,activeLabel:s,activePayload:f,activeCoordinate:p}}return null},$w=function(t,e){var r=e.axes,n=e.graphicalItems,o=e.axisType,i=e.axisIdKey,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.layout,f=t.children,p=t.stackOffset,h=$l(l,o);return r.reduce((function(e,r){var y,d=void 0!==r.type.defaultProps?Cw(Cw({},r.type.defaultProps),r.props):r.props,v=d.type,m=d.dataKey,b=d.allowDataOverflow,g=d.allowDuplicatedCategory,x=d.scale,w=d.ticks,O=d.includeHidden,j=d[i];if(e[j])return e;var A,P,E,k=zw(t.data,{graphicalItems:n.filter((function(t){var e;return(i in t.props?t.props[i]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[i])===j})),dataStartIndex:c,dataEndIndex:u}),T=k.length;(function(t,e,r){if("number"===r&&!0===e&&Array.isArray(t)){var n=null==t?void 0:t[0],o=null==t?void 0:t[1];if(n&&o&&S(n)&&S(o))return!0}return!1})(d.domain,b,v)&&(A=us(d.domain,null,b),!h||"number"!==v&&"auto"===x||(E=zl(k,m,"category")));var M=Fw(v);if(!A||0===A.length){var _,C=null!==(_=d.domain)&&void 0!==_?_:M;if(m){if(A=zl(k,m,v),"category"===v&&h){var D=function(t){if(!Array.isArray(t))return!1;for(var e=t.length,r={},n=0;n<e;n++){if(r[t[n]])return!0;r[t[n]]=!0}return!1}(A);g&&D?(P=A,A=kv()(0,T)):g||(A=ss(C,A,r).reduce((function(t,e){return t.indexOf(e)>=0?t:[].concat(kw(t),[e])}),[]))}else if("category"===v)A=g?A.filter((function(t){return""!==t&&!s()(t)})):ss(C,A,r).reduce((function(t,e){return t.indexOf(e)>=0||""===e||s()(e)?t:[].concat(kw(t),[e])}),[]);else if("number"===v){var I=function(t,e,r,n,o){var i=e.map((function(e){return Ul(t,e,r,o,n)})).filter((function(t){return!s()(t)}));return i&&i.length?i.reduce((function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]}),[1/0,-1/0]):null}(k,n.filter((function(t){var e,r,n=i in t.props?t.props[i]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[i],o="hide"in t.props?t.props.hide:null===(r=t.type.defaultProps)||void 0===r?void 0:r.hide;return n===j&&(O||!o)})),m,o,l);I&&(A=I)}!h||"number"!==v&&"auto"===x||(E=zl(k,m,"category"))}else A=h?kv()(0,T):a&&a[j]&&a[j].hasStack&&"number"===v?"expand"===p?[0,1]:is(a[j].stackGroups,c,u):Wl(k,n.filter((function(t){var e=i in t.props?t.props[i]:t.type.defaultProps[i],r="hide"in t.props?t.props.hide:t.type.defaultProps.hide;return e===j&&(O||!r)})),v,l,!0);if("number"===v)A=ew(f,A,j,o,w),C&&(A=us(C,A,b));else if("category"===v&&C){var N=C;A.every((function(t){return N.indexOf(t)>=0}))&&(A=N)}}return Cw(Cw({},e),{},Dw({},j,Cw(Cw({},d),{},{axisType:o,domain:A,categoricalDomain:E,duplicateDomain:P,originalDomain:null!==(y=d.domain)&&void 0!==y?y:M,isCategorical:h,layout:l})))}),{})},Kw=function(t,e){var r=e.axisType,n=void 0===r?"xAxis":r,o=e.AxisComp,i=e.graphicalItems,a=e.stackGroups,c=e.dataStartIndex,l=e.dataEndIndex,s=t.children,f="".concat(n,"Id"),p=Y(s,o),h={};return p&&p.length?h=$w(t,{axes:p,graphicalItems:i,axisType:n,axisIdKey:f,stackGroups:a,dataStartIndex:c,dataEndIndex:l}):i&&i.length&&(h=function(t,e){var r=e.graphicalItems,n=e.Axis,o=e.axisType,i=e.axisIdKey,a=e.stackGroups,c=e.dataStartIndex,l=e.dataEndIndex,s=t.layout,f=t.children,p=zw(t.data,{graphicalItems:r,dataStartIndex:c,dataEndIndex:l}),h=p.length,y=$l(s,o),d=-1;return r.reduce((function(t,e){var v,m=(void 0!==e.type.defaultProps?Cw(Cw({},e.type.defaultProps),e.props):e.props)[i],b=Fw("number");return t[m]?t:(d++,y?v=kv()(0,h):a&&a[m]&&a[m].hasStack?(v=is(a[m].stackGroups,c,l),v=ew(f,v,m,o)):(v=us(b,Wl(p,r.filter((function(t){var e,r,n=i in t.props?t.props[i]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[i],o="hide"in t.props?t.props.hide:null===(r=t.type.defaultProps)||void 0===r?void 0:r.hide;return n===m&&!o})),"number",s),n.defaultProps.allowDataOverflow),v=ew(f,v,m,o)),Cw(Cw({},t),{},Dw({},m,Cw(Cw({axisType:o},n.defaultProps),{},{hide:!0,orientation:u()(Nw,"".concat(o,".").concat(d%2),null),domain:v,originalDomain:b,isCategorical:y,layout:s}))))}),{})}(t,{Axis:o,graphicalItems:i,axisType:n,axisIdKey:f,stackGroups:a,dataStartIndex:c,dataEndIndex:l})),h},qw=function(t){var e=t.children,r=t.defaultShowTooltip,n=Z(e,Vv),o=0,i=0;return t.data&&0!==t.data.length&&(i=t.data.length-1),n&&n.props&&(n.props.startIndex>=0&&(o=n.props.startIndex),n.props.endIndex>=0&&(i=n.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:o,dataEndIndex:i,activeTooltipIndex:-1,isTooltipActive:Boolean(r)}},Vw=function(t){return"horizontal"===t?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===t?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===t?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},Xw=function(t,e){var r=t.props,n=(t.graphicalItems,t.xAxisMap),o=void 0===n?{}:n,i=t.yAxisMap,a=void 0===i?{}:i,c=r.width,l=r.height,s=r.children,f=r.margin||{},p=Z(s,Vv),h=Z(s,Ce),y=Object.keys(a).reduce((function(t,e){var r=a[e],n=r.orientation;return r.mirror||r.hide?t:Cw(Cw({},t),{},Dw({},n,t[n]+r.width))}),{left:f.left||0,right:f.right||0}),d=Object.keys(o).reduce((function(t,e){var r=o[e],n=r.orientation;return r.mirror||r.hide?t:Cw(Cw({},t),{},Dw({},n,u()(t,"".concat(n))+r.height))}),{top:f.top||0,bottom:f.bottom||0}),v=Cw(Cw({},d),y),m=v.bottom;p&&(v.bottom+=p.props.height||Vv.defaultProps.height),h&&e&&(v=function(t,e,r,n){var o=r.children,i=r.width,a=r.margin,c=i-(a.left||0)-(a.right||0),u=_l({children:o,legendWidth:c});if(u){var l=n||{},s=l.width,f=l.height,p=u.align,h=u.verticalAlign,y=u.layout;if(("vertical"===y||"horizontal"===y&&"middle"===h)&&"center"!==p&&S(t[p]))return Rl(Rl({},t),{},Bl({},p,t[p]+(s||0)));if(("horizontal"===y||"vertical"===y&&"center"===p)&&"middle"!==h&&S(t[h]))return Rl(Rl({},t),{},Bl({},h,t[h]+(f||0)))}return t}(v,0,r,e));var b=c-v.left-v.right,g=l-v.top-v.bottom;return Cw(Cw({brushBottom:m},v),{},{width:Math.max(b,0),height:Math.max(g,0)})},Hw=function(t,e){return"xAxis"===e?t[e].width:"yAxis"===e?t[e].height:void 0},Gw=function(t){var n=t.chartName,o=t.GraphicalChild,i=t.defaultTooltipEventType,c=void 0===i?"axis":i,l=t.validateTooltipEventTypes,f=void 0===l?["axis"]:l,p=t.axisComponents,h=t.legendContent,d=t.formatAxisMap,v=t.defaultProps,m=function(t,e){var r=e.graphicalItems,n=e.stackGroups,o=e.offset,i=e.updateId,a=e.dataStartIndex,c=e.dataEndIndex,u=t.barSize,l=t.layout,f=t.barGap,h=t.barCategoryGap,y=t.maxBarSize,d=Vw(l),v=d.numericAxisName,m=d.cateAxisName,b=function(t){return!(!t||!t.length)&&t.some((function(t){var e=V(t&&t.type);return e&&e.indexOf("Bar")>=0}))}(r),g=[];return r.forEach((function(r,d){var x=zw(t.data,{graphicalItems:[r],dataStartIndex:a,dataEndIndex:c}),w=void 0!==r.type.defaultProps?Cw(Cw({},r.type.defaultProps),r.props):r.props,O=w.dataKey,j=w.maxBarSize,S=w["".concat(v,"Id")],P=w["".concat(m,"Id")],E=p.reduce((function(t,r){var n=e["".concat(r.axisType,"Map")],o=w["".concat(r.axisType,"Id")];n&&n[o]||"zAxis"===r.axisType||pl(!1);var i=n[o];return Cw(Cw({},t),{},Dw(Dw({},r.axisType,i),"".concat(r.axisType,"Ticks"),ql(i)))}),{}),T=E[m],M=E["".concat(m,"Ticks")],_=n&&n[S]&&n[S].hasStack&&function(t,e){var r,n=(null!==(r=t.type)&&void 0!==r&&r.defaultProps?Rl(Rl({},t.type.defaultProps),t.props):t.props).stackId;if(A(n)){var o=e[n];if(o){var i=o.items.indexOf(t);return i>=0?o.stackedData[i]:null}}return null}(r,n[S].stackGroups),C=V(r.type).indexOf("Bar")>=0,D=ls(T,M),I=[],N=b&&function(t){var e=t.barSize,r=t.totalSize,n=t.stackGroups,o=void 0===n?{}:n;if(!o)return{};for(var i={},a=Object.keys(o),c=0,u=a.length;c<u;c++)for(var l=o[a[c]].stackGroups,f=Object.keys(l),p=0,h=f.length;p<h;p++){var y=l[f[p]],d=y.items,v=y.cateAxisId,m=d.filter((function(t){return V(t.type).indexOf("Bar")>=0}));if(m&&m.length){var b=m[0].type.defaultProps,g=void 0!==b?Rl(Rl({},b),m[0].props):m[0].props,x=g.barSize,w=g[v];i[w]||(i[w]=[]);var O=s()(x)?e:x;i[w].push({item:m[0],stackList:m.slice(1),barSize:s()(O)?void 0:k(O,r,0)})}}return i}({barSize:u,stackGroups:n,totalSize:Hw(E,m)});if(C){var R,B,L=s()(j)?y:j,z=null!==(R=null!==(B=ls(T,M,!0))&&void 0!==B?B:L)&&void 0!==R?R:0;I=function(t){var e=t.barGap,r=t.barCategoryGap,n=t.bandSize,o=t.sizeList,i=void 0===o?[]:o,a=t.maxBarSize,c=i.length;if(c<1)return null;var u,l=k(e,n,0,!0),s=[];if(i[0].barSize===+i[0].barSize){var f=!1,p=n/c,h=i.reduce((function(t,e){return t+e.barSize||0}),0);(h+=(c-1)*l)>=n&&(h-=(c-1)*l,l=0),h>=n&&p>0&&(f=!0,h=c*(p*=.9));var y={offset:((n-h)/2>>0)-l,size:0};u=i.reduce((function(t,e){var r={item:e.item,position:{offset:y.offset+y.size+l,size:f?p:e.barSize}},n=[].concat(Dl(t),[r]);return y=n[n.length-1].position,e.stackList&&e.stackList.length&&e.stackList.forEach((function(t){n.push({item:t,position:y})})),n}),s)}else{var d=k(r,n,0,!0);n-2*d-(c-1)*l<=0&&(l=0);var v=(n-2*d-(c-1)*l)/c;v>1&&(v>>=0);var m=a===+a?Math.min(v,a):v;u=i.reduce((function(t,e,r){var n=[].concat(Dl(t),[{item:e.item,position:{offset:d+(v+l)*r+(v-m)/2,size:m}}]);return e.stackList&&e.stackList.length&&e.stackList.forEach((function(t){n.push({item:t,position:n[n.length-1].position})})),n}),s)}return u}({barGap:f,barCategoryGap:h,bandSize:z!==D?z:D,sizeList:N[P],maxBarSize:L}),z!==D&&(I=I.map((function(t){return Cw(Cw({},t),{},{position:Cw(Cw({},t.position),{},{offset:t.position.offset-z/2})})})))}var F,U,W=r&&r.type&&r.type.getComposedData;W&&g.push({props:Cw(Cw({},W(Cw(Cw({},E),{},{displayedData:x,props:t,dataKey:O,item:r,bandSize:D,barPosition:I,offset:o,stackedData:_,layout:l,dataStartIndex:a,dataEndIndex:c}))),{},Dw(Dw(Dw({key:r.key||"item-".concat(d)},v,E[v]),m,E[m]),"animationId",i)),childIndex:(F=r,U=t.children,G(U).indexOf(F)),item:r})})),g},b=function(t,e){var r=t.props,i=t.dataStartIndex,a=t.dataEndIndex,c=t.updateId;if(!J({props:r}))return null;var u=r.children,l=r.layout,s=r.stackOffset,f=r.data,h=r.reverseStackOrder,y=Vw(l),v=y.numericAxisName,b=y.cateAxisName,g=Y(u,o),x=function(t,e,r,n,o,i){if(!t)return null;var a=(i?e.reverse():e).reduce((function(t,e){var o,i=null!==(o=e.type)&&void 0!==o&&o.defaultProps?Rl(Rl({},e.type.defaultProps),e.props):e.props,a=i.stackId;if(i.hide)return t;var c=i[r],u=t[c]||{hasStack:!1,stackGroups:{}};if(A(a)){var l=u.stackGroups[a]||{numericAxisId:r,cateAxisId:n,items:[]};l.items.push(e),u.hasStack=!0,u.stackGroups[a]=l}else u.stackGroups[E("_stackId_")]={numericAxisId:r,cateAxisId:n,items:[e]};return Rl(Rl({},t),{},Bl({},c,u))}),{});return Object.keys(a).reduce((function(e,i){var c=a[i];return c.hasStack&&(c.stackGroups=Object.keys(c.stackGroups).reduce((function(e,i){var a=c.stackGroups[i];return Rl(Rl({},e),{},Bl({},i,{numericAxisId:r,cateAxisId:n,items:a.items,stackedData:ts(t,a.items,o)}))}),{})),Rl(Rl({},e),{},Bl({},i,c))}),{})}(f,g,"".concat(v,"Id"),"".concat(b,"Id"),s,h),w=p.reduce((function(t,e){var n="".concat(e.axisType,"Map");return Cw(Cw({},t),{},Dw({},n,Kw(r,Cw(Cw({},e),{},{graphicalItems:g,stackGroups:e.axisType===v&&x,dataStartIndex:i,dataEndIndex:a}))))}),{}),O=Xw(Cw(Cw({},w),{},{props:r,graphicalItems:g}),null==e?void 0:e.legendBBox);Object.keys(w).forEach((function(t){w[t]=d(r,w[t],O,t.replace("Map",""),n)}));var j,S,P=w["".concat(b,"Map")],k=(j=T(P),{tooltipTicks:S=ql(j,!1,!0),orderedTooltipTicks:Ie()(S,(function(t){return t.coordinate})),tooltipAxis:j,tooltipAxisBandSize:ls(j,S)}),M=m(r,Cw(Cw({},w),{},{dataStartIndex:i,dataEndIndex:a,updateId:c,graphicalItems:g,stackGroups:x,offset:O}));return Cw(Cw({formattedGraphicalItems:M,graphicalItems:g,offset:O,stackGroups:x},k),w)},g=function(t){function o(t){var i,c,l;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,o),Dw(l=Sw(this,o,[t]),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),Dw(l,"accessibilityManager",new lw),Dw(l,"handleLegendBBoxUpdate",(function(t){if(t){var e=l.state,r=e.dataStartIndex,n=e.dataEndIndex,o=e.updateId;l.setState(Cw({legendBBox:t},b({props:l.props,dataStartIndex:r,dataEndIndex:n,updateId:o},Cw(Cw({},l.state),{},{legendBBox:t}))))}})),Dw(l,"handleReceiveSyncEvent",(function(t,e,r){if(l.props.syncId===t){if(r===l.eventEmitterSymbol&&"function"!=typeof l.props.syncMethod)return;l.applySyncEvent(e)}})),Dw(l,"handleBrushChange",(function(t){var e=t.startIndex,r=t.endIndex;if(e!==l.state.dataStartIndex||r!==l.state.dataEndIndex){var n=l.state.updateId;l.setState((function(){return Cw({dataStartIndex:e,dataEndIndex:r},b({props:l.props,dataStartIndex:e,dataEndIndex:r,updateId:n},l.state))})),l.triggerSyncEvent({dataStartIndex:e,dataEndIndex:r})}})),Dw(l,"handleMouseEnter",(function(t){var e=l.getMouseInfo(t);if(e){var r=Cw(Cw({},e),{},{isTooltipActive:!0});l.setState(r),l.triggerSyncEvent(r);var n=l.props.onMouseEnter;y()(n)&&n(r,t)}})),Dw(l,"triggeredAfterMouseMove",(function(t){var e=l.getMouseInfo(t),r=e?Cw(Cw({},e),{},{isTooltipActive:!0}):{isTooltipActive:!1};l.setState(r),l.triggerSyncEvent(r);var n=l.props.onMouseMove;y()(n)&&n(r,t)})),Dw(l,"handleItemMouseEnter",(function(t){l.setState((function(){return{isTooltipActive:!0,activeItem:t,activePayload:t.tooltipPayload,activeCoordinate:t.tooltipPosition||{x:t.cx,y:t.cy}}}))})),Dw(l,"handleItemMouseLeave",(function(){l.setState((function(){return{isTooltipActive:!1}}))})),Dw(l,"handleMouseMove",(function(t){t.persist(),l.throttleTriggeredAfterMouseMove(t)})),Dw(l,"handleMouseLeave",(function(t){l.throttleTriggeredAfterMouseMove.cancel();var e={isTooltipActive:!1};l.setState(e),l.triggerSyncEvent(e);var r=l.props.onMouseLeave;y()(r)&&r(e,t)})),Dw(l,"handleOuterEvent",(function(t){var e,r=function(t){var e=t&&t.type;return e&&q[e]?q[e]:null}(t),n=u()(l.props,"".concat(r));r&&y()(n)&&n(null!==(e=/.*touch.*/i.test(r)?l.getMouseInfo(t.changedTouches[0]):l.getMouseInfo(t))&&void 0!==e?e:{},t)})),Dw(l,"handleClick",(function(t){var e=l.getMouseInfo(t);if(e){var r=Cw(Cw({},e),{},{isTooltipActive:!0});l.setState(r),l.triggerSyncEvent(r);var n=l.props.onClick;y()(n)&&n(r,t)}})),Dw(l,"handleMouseDown",(function(t){var e=l.props.onMouseDown;y()(e)&&e(l.getMouseInfo(t),t)})),Dw(l,"handleMouseUp",(function(t){var e=l.props.onMouseUp;y()(e)&&e(l.getMouseInfo(t),t)})),Dw(l,"handleTouchMove",(function(t){null!=t.changedTouches&&t.changedTouches.length>0&&l.throttleTriggeredAfterMouseMove(t.changedTouches[0])})),Dw(l,"handleTouchStart",(function(t){null!=t.changedTouches&&t.changedTouches.length>0&&l.handleMouseDown(t.changedTouches[0])})),Dw(l,"handleTouchEnd",(function(t){null!=t.changedTouches&&t.changedTouches.length>0&&l.handleMouseUp(t.changedTouches[0])})),Dw(l,"handleDoubleClick",(function(t){var e=l.props.onDoubleClick;y()(e)&&e(l.getMouseInfo(t),t)})),Dw(l,"handleContextMenu",(function(t){var e=l.props.onContextMenu;y()(e)&&e(l.getMouseInfo(t),t)})),Dw(l,"triggerSyncEvent",(function(t){void 0!==l.props.syncId&&nw.emit(ow,l.props.syncId,t,l.eventEmitterSymbol)})),Dw(l,"applySyncEvent",(function(t){var e=l.props,r=e.layout,n=e.syncMethod,o=l.state.updateId,i=t.dataStartIndex,a=t.dataEndIndex;if(void 0!==t.dataStartIndex||void 0!==t.dataEndIndex)l.setState(Cw({dataStartIndex:i,dataEndIndex:a},b({props:l.props,dataStartIndex:i,dataEndIndex:a,updateId:o},l.state)));else if(void 0!==t.activeTooltipIndex){var c=t.chartX,u=t.chartY,s=t.activeTooltipIndex,f=l.state,p=f.offset,h=f.tooltipTicks;if(!p)return;if("function"==typeof n)s=n(h,t);else if("value"===n){s=-1;for(var y=0;y<h.length;y++)if(h[y].value===t.activeLabel){s=y;break}}var d=Cw(Cw({},p),{},{x:p.left,y:p.top}),v=Math.min(c,d.x+d.width),m=Math.min(u,d.y+d.height),g=h[s]&&h[s].value,x=Uw(l.state,l.props.data,s),w=h[s]?{x:"horizontal"===r?h[s].coordinate:v,y:"horizontal"===r?m:h[s].coordinate}:Bw;l.setState(Cw(Cw({},t),{},{activeLabel:g,activeCoordinate:w,activePayload:x,activeTooltipIndex:s}))}else l.setState(t)})),Dw(l,"renderCursor",(function(t){var e,o=l.state,i=o.isTooltipActive,a=o.activeCoordinate,c=o.activePayload,u=o.offset,s=o.activeTooltipIndex,f=o.tooltipAxisBandSize,p=l.getTooltipEventType(),h=null!==(e=t.props.active)&&void 0!==e?e:i,y=l.props.layout,d=t.key||"_recharts-cursor";return r().createElement(vw,{key:d,activeCoordinate:a,activePayload:c,activeTooltipIndex:s,chartName:n,element:t,isActive:h,layout:y,offset:u,tooltipAxisBandSize:f,tooltipEventType:p})})),Dw(l,"renderPolarAxis",(function(t,r,n){var o=u()(t,"type.axisType"),i=u()(l.state,"".concat(o,"Map")),c=t.type.defaultProps,s=void 0!==c?Cw(Cw({},c),t.props):t.props,f=i&&i[s["".concat(o,"Id")]];return(0,e.cloneElement)(t,Cw(Cw({},f),{},{className:a(o,f.className),key:t.key||"".concat(r,"-").concat(n),ticks:ql(f,!0)}))})),Dw(l,"renderPolarGrid",(function(t){var r=t.props,n=r.radialLines,o=r.polarAngles,i=r.polarRadius,a=l.state,c=a.radiusAxisMap,u=a.angleAxisMap,s=T(c),f=T(u),p=f.cx,h=f.cy,y=f.innerRadius,d=f.outerRadius;return(0,e.cloneElement)(t,{polarAngles:Array.isArray(o)?o:ql(f,!0).map((function(t){return t.coordinate})),polarRadius:Array.isArray(i)?i:ql(s,!0).map((function(t){return t.coordinate})),cx:p,cy:h,innerRadius:y,outerRadius:d,key:t.key||"polar-grid",radialLines:n})})),Dw(l,"renderLegend",(function(){var t=l.state.formattedGraphicalItems,r=l.props,n=r.children,o=r.width,i=r.height,a=l.props.margin||{},c=o-(a.left||0)-(a.right||0),u=_l({children:n,formattedGraphicalItems:t,legendWidth:c,legendContent:h});if(!u)return null;var s=u.item,f=Ow(u,mw);return(0,e.cloneElement)(s,Cw(Cw({},f),{},{chartWidth:o,chartHeight:i,margin:a,onBBoxUpdate:l.handleLegendBBoxUpdate}))})),Dw(l,"renderTooltip",(function(){var t,r=l.props,n=r.children,o=r.accessibilityLayer,i=Z(n,gr);if(!i)return null;var a=l.state,c=a.isTooltipActive,u=a.activeCoordinate,s=a.activePayload,f=a.activeLabel,p=a.offset,h=null!==(t=i.props.active)&&void 0!==t?t:c;return(0,e.cloneElement)(i,{viewBox:Cw(Cw({},p),{},{x:p.left,y:p.top}),active:h,label:f,payload:h?s:[],coordinate:u,accessibilityLayer:o})})),Dw(l,"renderBrush",(function(t){var r=l.props,n=r.margin,o=r.data,i=l.state,a=i.offset,c=i.dataStartIndex,u=i.dataEndIndex,s=i.updateId;return(0,e.cloneElement)(t,{key:t.key||"_recharts-brush",onChange:Xl(l.handleBrushChange,t.props.onChange),data:o,x:S(t.props.x)?t.props.x:a.left,y:S(t.props.y)?t.props.y:a.top+a.height+a.brushBottom-(n.bottom||0),width:S(t.props.width)?t.props.width:a.width,startIndex:c,endIndex:u,updateId:"brush-".concat(s)})})),Dw(l,"renderReferenceElement",(function(t,r,n){if(!t)return null;var o=l.clipPathId,i=l.state,a=i.xAxisMap,c=i.yAxisMap,u=i.offset,s=t.type.defaultProps||{},f=t.props,p=f.xAxisId,h=void 0===p?s.xAxisId:p,y=f.yAxisId,d=void 0===y?s.yAxisId:y;return(0,e.cloneElement)(t,{key:t.key||"".concat(r,"-").concat(n),xAxis:a[h],yAxis:c[d],viewBox:{x:u.left,y:u.top,width:u.width,height:u.height},clipPathId:o})})),Dw(l,"renderActivePoints",(function(t){var e=t.item,r=t.activePoint,n=t.basePoint,i=t.childIndex,a=t.isRange,c=[],u=e.props.key,l=void 0!==e.item.type.defaultProps?Cw(Cw({},e.item.type.defaultProps),e.item.props):e.item.props,s=l.activeDot,f=Cw(Cw({index:i,dataKey:l.dataKey,cx:r.x,cy:r.y,r:4,fill:Fl(e.item),strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value},nt(s,!1)),z(s));return c.push(o.renderActiveDot(s,f,"".concat(u,"-activePoint-").concat(i))),n?c.push(o.renderActiveDot(s,Cw(Cw({},f),{},{cx:n.x,cy:n.y}),"".concat(u,"-basePoint-").concat(i))):a&&c.push(null),c})),Dw(l,"renderGraphicChild",(function(t,r,n){var o=l.filterFormatItem(t,r,n);if(!o)return null;var i=l.getTooltipEventType(),a=l.state,c=a.isTooltipActive,u=a.tooltipAxis,f=a.activeTooltipIndex,p=a.activeLabel,h=Z(l.props.children,gr),y=o.props,d=y.points,v=y.isRange,m=y.baseLine,b=void 0!==o.item.type.defaultProps?Cw(Cw({},o.item.type.defaultProps),o.item.props):o.item.props,g=b.activeDot,x=b.hide,w=b.activeBar,O=b.activeShape,j=Boolean(!x&&c&&h&&(g||w||O)),S={};"axis"!==i&&h&&"click"===h.props.trigger?S={onClick:Xl(l.handleItemMouseEnter,t.props.onClick)}:"axis"!==i&&(S={onMouseLeave:Xl(l.handleItemMouseLeave,t.props.onMouseLeave),onMouseEnter:Xl(l.handleItemMouseEnter,t.props.onMouseEnter)});var A=(0,e.cloneElement)(t,Cw(Cw({},o.props),S));if(j){if(!(f>=0)){var P,E=(null!==(P=l.getItemByXY(l.state.activeCoordinate))&&void 0!==P?P:{graphicalItem:A}).graphicalItem,k=E.item,T=void 0===k?t:k,M=E.childIndex,C=Cw(Cw(Cw({},o.props),S),{},{activeIndex:M});return[(0,e.cloneElement)(T,C),null,null]}var D,I;if(u.dataKey&&!u.allowDuplicatedCategory){var N="function"==typeof u.dataKey?function(t){return"function"==typeof u.dataKey?u.dataKey(t.payload):null}:"payload.".concat(u.dataKey.toString());D=_(d,N,p),I=v&&m&&_(m,N,p)}else D=null==d?void 0:d[f],I=v&&m&&m[f];if(O||w){var R=void 0!==t.props.activeIndex?t.props.activeIndex:f;return[(0,e.cloneElement)(t,Cw(Cw(Cw({},o.props),S),{},{activeIndex:R})),null,null]}if(!s()(D))return[A].concat(kw(l.renderActivePoints({item:o,activePoint:D,basePoint:I,childIndex:f,isRange:v})))}return v?[A,null,null]:[A,null]})),Dw(l,"renderCustomized",(function(t,r,n){return(0,e.cloneElement)(t,Cw(Cw({key:"recharts-customized-".concat(n)},l.props),l.state))})),Dw(l,"renderMap",{CartesianGrid:{handler:Lw,once:!0},ReferenceArea:{handler:l.renderReferenceElement},ReferenceLine:{handler:Lw},ReferenceDot:{handler:l.renderReferenceElement},XAxis:{handler:Lw},YAxis:{handler:Lw},Brush:{handler:l.renderBrush,once:!0},Bar:{handler:l.renderGraphicChild},Line:{handler:l.renderGraphicChild},Area:{handler:l.renderGraphicChild},Radar:{handler:l.renderGraphicChild},RadialBar:{handler:l.renderGraphicChild},Scatter:{handler:l.renderGraphicChild},Pie:{handler:l.renderGraphicChild},Funnel:{handler:l.renderGraphicChild},Tooltip:{handler:l.renderCursor,once:!0},PolarGrid:{handler:l.renderPolarGrid,once:!0},PolarAngleAxis:{handler:l.renderPolarAxis},PolarRadiusAxis:{handler:l.renderPolarAxis},Customized:{handler:l.renderCustomized}}),l.clipPathId="".concat(null!==(i=t.id)&&void 0!==i?i:E("recharts"),"-clip"),l.throttleTriggeredAfterMouseMove=wr()(l.triggeredAfterMouseMove,null!==(c=t.throttleDelay)&&void 0!==c?c:1e3/60),l.state={},l}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Ew(t,e)}(o,t),i=o,l=[{key:"componentDidMount",value:function(){var t,e;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!==(t=this.props.margin.left)&&void 0!==t?t:0,top:null!==(e=this.props.margin.top)&&void 0!==e?e:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var t=this.props,e=t.children,r=t.data,n=t.height,o=t.layout,i=Z(e,gr);if(i){var a=i.props.defaultIndex;if(!("number"!=typeof a||a<0||a>this.state.tooltipTicks.length-1)){var c=this.state.tooltipTicks[a]&&this.state.tooltipTicks[a].value,u=Uw(this.state,r,a,c),l=this.state.tooltipTicks[a].coordinate,s=(this.state.offset.top+n)/2,f="horizontal"===o?{x:l,y:s}:{y:l,x:s},p=this.state.formattedGraphicalItems.find((function(t){return"Scatter"===t.item.type.name}));p&&(f=Cw(Cw({},f),p.props.points[a].tooltipPosition),u=p.props.points[a].tooltipPayload);var h={activeTooltipIndex:a,isTooltipActive:!0,activeLabel:c,activePayload:u,activeCoordinate:f};this.setState(h),this.renderCursor(i),this.accessibilityManager.setIndex(a)}}}},{key:"getSnapshotBeforeUpdate",value:function(t,e){return this.props.accessibilityLayer?(this.state.tooltipTicks!==e.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==t.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==t.margin&&this.accessibilityManager.setDetails({offset:{left:null!==(r=this.props.margin.left)&&void 0!==r?r:0,top:null!==(n=this.props.margin.top)&&void 0!==n?n:0}}),null):null;var r,n}},{key:"componentDidUpdate",value:function(t){ot([Z(t.children,gr)],[Z(this.props.children,gr)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var t=Z(this.props.children,gr);if(t&&"boolean"==typeof t.props.shared){var e=t.props.shared?"axis":"item";return f.indexOf(e)>=0?e:c}return c}},{key:"getMouseInfo",value:function(t){if(!this.container)return null;var e,r=this.container,n=r.getBoundingClientRect(),o={top:(e=n).top+window.scrollY-document.documentElement.clientTop,left:e.left+window.scrollX-document.documentElement.clientLeft},i={chartX:Math.round(t.pageX-o.left),chartY:Math.round(t.pageY-o.top)},a=n.width/r.offsetWidth||1,c=this.inRange(i.chartX,i.chartY,a);if(!c)return null;var u=this.state,l=u.xAxisMap,s=u.yAxisMap,f=this.getTooltipEventType(),p=Ww(this.state,this.props.data,this.props.layout,c);if("axis"!==f&&l&&s){var h=T(l).scale,y=T(s).scale,d=h&&h.invert?h.invert(i.chartX):null,v=y&&y.invert?y.invert(i.chartY):null;return Cw(Cw({},i),{},{xValue:d,yValue:v},p)}return p?Cw(Cw({},i),p):null}},{key:"inRange",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this.props.layout,o=t/r,i=e/r;if("horizontal"===n||"vertical"===n){var a=this.state.offset;return o>=a.left&&o<=a.left+a.width&&i>=a.top&&i<=a.top+a.height?{x:o,y:i}:null}var c=this.state,u=c.angleAxisMap,l=c.radiusAxisMap;if(u&&l){var s=T(u);return As({x:o,y:i},s)}return null}},{key:"parseEventsOfWrapper",value:function(){var t=this.props.children,e=this.getTooltipEventType(),r=Z(t,gr),n={};return r&&"axis"===e&&(n="click"===r.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu}),Cw(Cw({},z(this.props,this.handleOuterEvent)),n)}},{key:"addListener",value:function(){nw.on(ow,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){nw.removeListener(ow,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(t,e,r){for(var n=this.state.formattedGraphicalItems,o=0,i=n.length;o<i;o++){var a=n[o];if(a.item===t||a.props.key===t.key||e===V(a.item.type)&&r===a.childIndex)return a}return null}},{key:"renderClipPath",value:function(){var t=this.clipPathId,e=this.state.offset,n=e.left,o=e.top,i=e.height,a=e.width;return r().createElement("defs",null,r().createElement("clipPath",{id:t},r().createElement("rect",{x:n,y:o,height:i,width:a})))}},{key:"getXScales",value:function(){var t=this.state.xAxisMap;return t?Object.entries(t).reduce((function(t,e){var r=ww(e,2),n=r[0],o=r[1];return Cw(Cw({},t),{},Dw({},n,o.scale))}),{}):null}},{key:"getYScales",value:function(){var t=this.state.yAxisMap;return t?Object.entries(t).reduce((function(t,e){var r=ww(e,2),n=r[0],o=r[1];return Cw(Cw({},t),{},Dw({},n,o.scale))}),{}):null}},{key:"getXScaleByAxisId",value:function(t){var e;return null===(e=this.state.xAxisMap)||void 0===e||null===(e=e[t])||void 0===e?void 0:e.scale}},{key:"getYScaleByAxisId",value:function(t){var e;return null===(e=this.state.yAxisMap)||void 0===e||null===(e=e[t])||void 0===e?void 0:e.scale}},{key:"getItemByXY",value:function(t){var e=this.state,r=e.formattedGraphicalItems,n=e.activeItem;if(r&&r.length)for(var o=0,i=r.length;o<i;o++){var a=r[o],c=a.props,u=a.item,l=void 0!==u.type.defaultProps?Cw(Cw({},u.type.defaultProps),u.props):u.props,s=V(u.type);if("Bar"===s){var f=(c.data||[]).find((function(e){return zh(t,e)}));if(f)return{graphicalItem:a,payload:f}}else if("RadialBar"===s){var p=(c.data||[]).find((function(e){return As(t,e)}));if(p)return{graphicalItem:a,payload:p}}else if(jd(a,n)||Sd(a,n)||Ad(a,n)){var h=Td({graphicalItem:a,activeTooltipItem:n,itemData:l.data}),y=void 0===l.activeIndex?h:l.activeIndex;return{graphicalItem:Cw(Cw({},a),{},{childIndex:y}),payload:Ad(a,n)?l.data[h]:a.props.data[h]}}}return null}},{key:"render",value:function(){var t=this;if(!J(this))return null;var e,n,o=this.props,i=o.children,c=o.className,u=o.width,l=o.height,s=o.style,f=o.compact,p=o.title,h=o.desc,y=Ow(o,bw),d=nt(y,!1);if(f)return r().createElement(Xm,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},r().createElement(st,xw({},d,{width:u,height:l,title:p,desc:h}),this.renderClipPath(),at(i,this.renderMap)));this.props.accessibilityLayer&&(d.tabIndex=null!==(e=this.props.tabIndex)&&void 0!==e?e:0,d.role=null!==(n=this.props.role)&&void 0!==n?n:"application",d.onKeyDown=function(e){t.accessibilityManager.keyboardEvent(e)},d.onFocus=function(){t.accessibilityManager.focus()});var v=this.parseEventsOfWrapper();return r().createElement(Xm,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},r().createElement("div",xw({className:a("recharts-wrapper",c),style:Cw({position:"relative",cursor:"default",width:u,height:l},s)},v,{ref:function(e){t.container=e}}),r().createElement(st,xw({},d,{width:u,height:l,title:p,desc:h,style:Rw}),this.renderClipPath(),at(i,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}],l&&jw(i.prototype,l),p&&jw(i,p),Object.defineProperty(i,"prototype",{writable:!1}),i;var i,l,p}(e.Component);Dw(g,"displayName",n),Dw(g,"defaultProps",Cw({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},v)),Dw(g,"getDerivedStateFromProps",(function(t,e){var r=t.dataKey,n=t.data,o=t.children,i=t.width,a=t.height,c=t.layout,u=t.stackOffset,l=t.margin,f=e.dataStartIndex,p=e.dataEndIndex;if(void 0===e.updateId){var h=qw(t);return Cw(Cw(Cw({},h),{},{updateId:0},b(Cw(Cw({props:t},h),{},{updateId:0}),e)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:c,prevStackOffset:u,prevMargin:l,prevChildren:o})}if(r!==e.prevDataKey||n!==e.prevData||i!==e.prevWidth||a!==e.prevHeight||c!==e.prevLayout||u!==e.prevStackOffset||!D(l,e.prevMargin)){var y=qw(t),d={chartX:e.chartX,chartY:e.chartY,isTooltipActive:e.isTooltipActive},v=Cw(Cw({},Ww(e,n,c)),{},{updateId:e.updateId+1}),m=Cw(Cw(Cw({},y),d),v);return Cw(Cw(Cw({},m),b(Cw({props:t},m),e)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:c,prevStackOffset:u,prevMargin:l,prevChildren:o})}if(!ot(o,e.prevChildren)){var g,x,w,O,j=Z(o,Vv),S=j&&null!==(g=null===(x=j.props)||void 0===x?void 0:x.startIndex)&&void 0!==g?g:f,A=j&&null!==(w=null===(O=j.props)||void 0===O?void 0:O.endIndex)&&void 0!==w?w:p,P=S!==f||A!==p,E=!s()(n)&&!P?e.updateId:e.updateId+1;return Cw(Cw({updateId:E},b(Cw(Cw({props:t},e),{},{updateId:E,dataStartIndex:S,dataEndIndex:A}),e)),{},{prevChildren:o,dataStartIndex:S,dataEndIndex:A})}return null})),Dw(g,"renderActiveDot",(function(t,n,o){var i;return i=(0,e.isValidElement)(t)?(0,e.cloneElement)(t,n):y()(t)?t(n):r().createElement(Zh,n),r().createElement(yt,{className:"recharts-active-dot",key:o},i)}));var x=(0,e.forwardRef)((function(t,e){return r().createElement(g,xw({},t,{ref:e}))}));return x.displayName=g.displayName,x},Yw=Gw({chartName:"LineChart",GraphicalChild:$g,axisComponents:[{axisType:"xAxis",AxisComp:Ux},{axisType:"yAxis",AxisComp:Jx}],formatAxisMap:_m}),Zw=Gw({chartName:"BarChart",GraphicalChild:Sm,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:Ux},{axisType:"yAxis",AxisComp:Jx}],formatAxisMap:_m}),Jw=Gw({chartName:"PieChart",GraphicalChild:Ud,validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",legendContent:"children",axisComponents:[{axisType:"angleAxis",AxisComp:Zy},{axisType:"radiusAxis",AxisComp:By}],formatAxisMap:Os,defaultProps:{layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}}),Qw=o(1860),tO=o.n(Qw),eO=["#1890FF","#66B5FF","#41D9C7","#2FC25B","#6EDB8F","#9AE65C","#FACC14","#E6965C","#57AD71","#223273","#738AE6","#7564CC","#8543E0","#A877ED","#5C8EE6","#13C2C2","#70E0E0","#5CA3E6","#3436C7","#8082FF","#DD81E6","#F04864","#FA7D92","#D598D9"],rO=["width","height","className","style","children","type"];function nO(t){return nO="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},nO(t)}function oO(){return oO=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},oO.apply(this,arguments)}function iO(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function aO(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,yO(n.key),n)}}function cO(t,e,r){return e=lO(e),function(t,e){if(e&&("object"===nO(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,uO()?Reflect.construct(e,r||[],lO(t).constructor):e.apply(t,r))}function uO(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(uO=function(){return!!t})()}function lO(t){return lO=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},lO(t)}function sO(t,e){return sO=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},sO(t,e)}function fO(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function pO(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fO(Object(r),!0).forEach((function(e){hO(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fO(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function hO(t,e,r){return(e=yO(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function yO(t){var e=function(t,e){if("object"!=nO(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nO(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==nO(e)?e:e+""}var dO="value",vO=function t(e){var r,n=e.depth,o=e.node,i=e.index,a=e.valueKey,c=o.children,u=n+1,l=c&&c.length?c.map((function(e,r){return t({depth:u,node:e,index:r,valueKey:a})})):null;return r=c&&c.length?l.reduce((function(t,e){return t+e[dO]}),0):g()(o[a])||o[a]<=0?0:o[a],pO(pO({},o),{},hO(hO(hO({children:l},dO,r),"depth",n),"index",i))},mO=function(t,e,r){var n=e*e,o=t.area*t.area,i=t.reduce((function(t,e){return{min:Math.min(t.min,e.area),max:Math.max(t.max,e.area)}}),{min:1/0,max:0}),a=i.min,c=i.max;return o?Math.max(n*c*r/o,o/(n*a*r)):1/0},bO=function(t,e,r,n){return e===r.width?function(t,e,r,n){var o=e?Math.round(t.area/e):0;(n||o>r.height)&&(o=r.height);for(var i,a=r.x,c=0,u=t.length;c<u;c++)(i=t[c]).x=a,i.y=r.y,i.height=o,i.width=Math.min(o?Math.round(i.area/o):0,r.x+r.width-a),a+=i.width;return i.width+=r.x+r.width-a,pO(pO({},r),{},{y:r.y+o,height:r.height-o})}(t,e,r,n):function(t,e,r,n){var o=e?Math.round(t.area/e):0;(n||o>r.width)&&(o=r.width);for(var i,a=r.y,c=0,u=t.length;c<u;c++)(i=t[c]).x=r.x,i.y=a,i.width=o,i.height=Math.min(o?Math.round(i.area/o):0,r.y+r.height-a),a+=i.height;return i&&(i.height+=r.y+r.height-a),pO(pO({},r),{},{x:r.x+o,width:r.width-o})}(t,e,r,n)},gO=function t(e,r){var n=e.children;if(n&&n.length){var o,i,a=function(t){return{x:t.x,y:t.y,width:t.width,height:t.height}}(e),c=[],u=1/0,l=Math.min(a.width,a.height),s=function(t,e){var r=e<0?0:e;return t.map((function(t){var e=t[dO]*r;return pO(pO({},t),{},{area:g()(e)||e<=0?0:e})}))}(n,a.width*a.height/e[dO]),f=s.slice();for(c.area=0;f.length>0;)c.push(o=f[0]),c.area+=o.area,(i=mO(c,l,r))<=u?(f.shift(),u=i):(c.area-=c.pop().area,a=bO(c,l,a,!1),l=Math.min(a.width,a.height),c.length=c.area=0,u=1/0);return c.length&&(a=bO(c,l,a,!0),c.length=c.area=0),pO(pO({},e),{},{children:s.map((function(e){return t(e,r)}))})}return e},xO={isTooltipActive:!1,isAnimationFinished:!1,activeNode:null,formatRoot:null,currentRoot:null,nestIndex:[]},wO=function(t){function e(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return hO(t=cO(this,e,[].concat(n)),"state",pO({},xO)),hO(t,"handleAnimationEnd",(function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),y()(e)&&e()})),hO(t,"handleAnimationStart",(function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),y()(e)&&e()})),t}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&sO(t,e)}(e,t),n=e,i=[{key:"getDerivedStateFromProps",value:function(t,e){if(t.data!==e.prevData||t.type!==e.prevType||t.width!==e.prevWidth||t.height!==e.prevHeight||t.dataKey!==e.prevDataKey||t.aspectRatio!==e.prevAspectRatio){var r=vO({depth:0,node:{children:t.data,x:0,y:0,width:t.width,height:t.height},index:0,valueKey:t.dataKey}),n=gO(r,t.aspectRatio);return pO(pO({},e),{},{formatRoot:n,currentRoot:r,nestIndex:[r],prevAspectRatio:t.aspectRatio,prevData:t.data,prevWidth:t.width,prevHeight:t.height,prevDataKey:t.dataKey,prevType:t.type})}return null}},{key:"renderContentItem",value:function(t,e,n,o){if(r().isValidElement(t))return r().cloneElement(t,e);if(y()(t))return t(e);var i=e.x,a=e.y,c=e.width,u=e.height,l=e.index,s=null;c>10&&u>10&&e.children&&"nest"===n&&(s=r().createElement(Gh,{points:[{x:i+2,y:a+u/2},{x:i+6,y:a+u/2+3},{x:i+2,y:a+u/2+6}]}));var f=null,p=Br(e.name);c>20&&u>20&&p.width<c&&p.height<u&&(f=r().createElement("text",{x:i+8,y:a+u/2+7,fontSize:14},e.name));var h=o||eO;return r().createElement("g",null,r().createElement(Uh,oO({fill:e.depth<2?h[l%h.length]:"rgba(255,255,255,0)",stroke:"#fff"},tO()(e,"children"),{role:"img"})),s,f)}}],(o=[{key:"handleMouseEnter",value:function(t,e){e.persist();var r=this.props,n=r.onMouseEnter;Z(r.children,gr)?this.setState({isTooltipActive:!0,activeNode:t},(function(){n&&n(t,e)})):n&&n(t,e)}},{key:"handleMouseLeave",value:function(t,e){e.persist();var r=this.props,n=r.onMouseLeave;Z(r.children,gr)?this.setState({isTooltipActive:!1,activeNode:null},(function(){n&&n(t,e)})):n&&n(t,e)}},{key:"handleClick",value:function(t){var e=this.props,r=e.onClick;if("nest"===e.type&&t.children){var n=this.props,o=n.width,i=n.height,a=n.dataKey,c=n.aspectRatio,u=vO({depth:0,node:pO(pO({},t),{},{x:0,y:0,width:o,height:i}),index:0,valueKey:a}),l=gO(u,c),s=this.state.nestIndex;s.push(t),this.setState({formatRoot:l,currentRoot:u,nestIndex:s})}r&&r(t)}},{key:"handleNestIndex",value:function(t,e){var r=this.state.nestIndex,n=this.props,o=n.width,i=n.height,a=n.dataKey,c=n.aspectRatio,u=vO({depth:0,node:pO(pO({},t),{},{x:0,y:0,width:o,height:i}),index:0,valueKey:a}),l=gO(u,c);r=r.slice(0,e+1),this.setState({formatRoot:l,currentRoot:t,nestIndex:r})}},{key:"renderItem",value:function(t,e,n){var o=this,i=this.props,a=i.isAnimationActive,c=i.animationBegin,u=i.animationDuration,l=i.animationEasing,s=i.isUpdateAnimationActive,f=i.type,p=i.animationId,h=i.colorPanel,y=this.state.isAnimationFinished,d=e.width,v=e.height,m=e.x,b=e.y,g=e.depth,x=parseInt("".concat((2*Math.random()-1)*d),10),w={};return(n||"nest"===f)&&(w={onMouseEnter:this.handleMouseEnter.bind(this,e),onMouseLeave:this.handleMouseLeave.bind(this,e),onClick:this.handleClick.bind(this,e)}),a?r().createElement(Mh,{begin:c,duration:u,isActive:a,easing:l,key:"treemap-".concat(p),from:{x:m,y:b,width:d,height:v},to:{x:m,y:b,width:d,height:v},onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},(function(n){var i=n.x,p=n.y,d=n.width,v=n.height;return r().createElement(Mh,{from:"translate(".concat(x,"px, ").concat(x,"px)"),to:"translate(0, 0)",attributeName:"transform",begin:c,easing:l,isActive:a,duration:u},r().createElement(yt,w,g>2&&!y?null:o.constructor.renderContentItem(t,pO(pO({},e),{},{isAnimationActive:a,isUpdateAnimationActive:!s,width:d,height:v,x:i,y:p}),f,h)))})):r().createElement(yt,w,this.constructor.renderContentItem(t,pO(pO({},e),{},{isAnimationActive:!1,isUpdateAnimationActive:!1,width:d,height:v,x:m,y:b}),f,h))}},{key:"renderNode",value:function(t,e){var n=this,o=this.props,i=o.content,a=o.type,c=pO(pO(pO({},nt(this.props,!1)),e),{},{root:t}),u=!e.children||!e.children.length;return!(this.state.currentRoot.children||[]).filter((function(t){return t.depth===e.depth&&t.name===e.name})).length&&t.depth&&"nest"===a?null:r().createElement(yt,{key:"recharts-treemap-node-".concat(c.x,"-").concat(c.y,"-").concat(c.name),className:"recharts-treemap-depth-".concat(e.depth)},this.renderItem(i,c,u),e.children&&e.children.length?e.children.map((function(t){return n.renderNode(e,t)})):null)}},{key:"renderAllNodes",value:function(){var t=this.state.formatRoot;return t?this.renderNode(t,t):null}},{key:"renderTooltip",value:function(){var t=this.props,e=t.children,n=t.nameKey,o=Z(e,gr);if(!o)return null;var i=this.props,a=i.width,c=i.height,u=this.state,l=u.isTooltipActive,s=u.activeNode,f={x:0,y:0,width:a,height:c},p=s?{x:s.x+s.width/2,y:s.y+s.height/2}:null,h=l&&s?[{payload:s,name:Ll(s,n,""),value:Ll(s,dO)}]:[];return r().cloneElement(o,{viewBox:f,active:l,coordinate:p,label:"",payload:h})}},{key:"renderNestIndex",value:function(){var t=this,e=this.props,n=e.nameKey,o=e.nestIndexContent,i=this.state.nestIndex;return r().createElement("div",{className:"recharts-treemap-nest-index-wrapper",style:{marginTop:"8px",textAlign:"center"}},i.map((function(e,i){var a=u()(e,n,"root"),c=null;return r().isValidElement(o)&&(c=r().cloneElement(o,e,i)),c=y()(o)?o(e,i):a,r().createElement("div",{onClick:t.handleNestIndex.bind(t,e,i),key:"nest-index-".concat(E()),className:"recharts-treemap-nest-index-box",style:{cursor:"pointer",display:"inline-block",padding:"0 7px",background:"#000",color:"#fff",marginRight:"3px"}},c)})))}},{key:"render",value:function(){if(!J(this))return null;var t=this.props,e=t.width,n=t.height,o=t.className,i=t.style,c=t.children,u=t.type,l=iO(t,rO),s=nt(l,!1);return r().createElement("div",{className:a("recharts-wrapper",o),style:pO(pO({},i),{},{position:"relative",cursor:"default",width:e,height:n}),role:"region"},r().createElement(st,oO({},s,{width:e,height:"nest"===u?n-30:n}),this.renderAllNodes(),rt(c)),this.renderTooltip(),"nest"===u&&this.renderNestIndex())}}])&&aO(n.prototype,o),i&&aO(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,o,i}(e.PureComponent);hO(wO,"displayName","Treemap"),hO(wO,"defaultProps",{aspectRatio:.5*(1+Math.sqrt(5)),dataKey:"value",type:"flat",isAnimationActive:!cr.isSsr,isUpdateAnimationActive:!cr.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"linear"});var OO=o(6356),jO=o.n(OO),SO=["width","height","className","style","children"],AO=["sourceX","sourceY","sourceControlX","targetX","targetY","targetControlX","linkWidth"];function PO(t){return PO="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},PO(t)}function EO(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function kO(){return kO=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},kO.apply(this,arguments)}function TO(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,BO(n.key),n)}}function MO(t,e,r){return e=CO(e),function(t,e){if(e&&("object"===PO(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,_O()?Reflect.construct(e,r||[],CO(t).constructor):e.apply(t,r))}function _O(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(_O=function(){return!!t})()}function CO(t){return CO=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},CO(t)}function DO(t,e){return DO=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},DO(t,e)}function IO(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function NO(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?IO(Object(r),!0).forEach((function(e){RO(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):IO(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function RO(t,e,r){return(e=BO(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function BO(t){var e=function(t,e){if("object"!=PO(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=PO(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==PO(e)?e:e+""}var LO={x:0,y:0},zO=function(t){return t.y+t.dy/2},FO=function(t){return t&&t.value||0},UO=function(t,e){return e.reduce((function(e,r){return e+FO(t[r])}),0)},WO=function(t,e,r){return r.reduce((function(r,n){var o=e[n],i=t[o.source];return r+zO(i)*FO(e[n])}),0)},$O=function(t,e,r){return r.reduce((function(r,n){var o=e[n],i=t[o.target];return r+zO(i)*FO(e[n])}),0)},KO=function(t,e){return t.y-e.y},qO=function t(e,r){for(var n=r.targetNodes,o=0,i=n.length;o<i;o++){var a=e[n[o]];a&&(a.depth=Math.max(r.depth+1,a.depth),t(e,a))}},VO=function(t,e,r){for(var n=!(arguments.length>3&&void 0!==arguments[3])||arguments[3],o=0,i=t.length;o<i;o++){var a=t[o],c=a.length;n&&a.sort(KO);for(var u=0,l=0;l<c;l++){var s=a[l],f=u-s.y;f>0&&(s.y+=f),u=s.y+s.dy+r}u=e+r;for(var p=c-1;p>=0;p--){var h=a[p],y=h.y+h.dy+r-u;if(!(y>0))break;h.y-=y,u=h.y}}},XO=function(t,e,r,n){for(var o=0,i=e.length;o<i;o++)for(var a=e[o],c=0,u=a.length;c<u;c++){var l=a[c];if(l.sourceLinks.length){var s=UO(r,l.sourceLinks),f=WO(t,r,l.sourceLinks)/s;l.y+=(f-zO(l))*n}}},HO=function(t,e,r,n){for(var o=e.length-1;o>=0;o--)for(var i=e[o],a=0,c=i.length;a<c;a++){var u=i[a];if(u.targetLinks.length){var l=UO(r,u.targetLinks),s=$O(t,r,u.targetLinks)/l;u.y+=(s-zO(u))*n}}},GO=function(t){var e=t.data,r=t.width,n=t.height,o=t.iterations,i=t.nodeWidth,a=t.nodePadding,c=t.sort,u=e.links,l=function(t,e,r){for(var n=t.nodes,o=t.links,i=n.map((function(t,e){var r=function(t,e){for(var r=[],n=[],o=[],i=[],a=0,c=t.length;a<c;a++){var u=t[a];u.source===e&&(o.push(u.target),i.push(a)),u.target===e&&(r.push(u.source),n.push(a))}return{sourceNodes:r,sourceLinks:n,targetLinks:i,targetNodes:o}}(o,e);return NO(NO(NO({},t),r),{},{value:Math.max(UO(o,r.sourceLinks),UO(o,r.targetLinks)),depth:0})})),a=0,c=i.length;a<c;a++){var u=i[a];u.sourceNodes.length||qO(i,u)}var l=xy()(i,(function(t){return t.depth})).depth;if(l>=1)for(var s=(e-r)/l,f=0,p=i.length;f<p;f++){var h=i[f];h.targetNodes.length||(h.depth=l),h.x=h.depth*s,h.dx=r}return{tree:i,maxDepth:l}}(e,r,i),s=l.tree,f=function(t){for(var e=[],r=0,n=t.length;r<n;r++){var o=t[r];e[o.depth]||(e[o.depth]=[]),e[o.depth].push(o)}return e}(s),p=function(t,e,r,n){for(var o=_u()(t.map((function(t){return(e-(t.length-1)*r)/jO()(t,FO)}))),i=0,a=t.length;i<a;i++)for(var c=0,u=t[i].length;c<u;c++){var l=t[i][c];l.y=c,l.dy=l.value*o}return n.map((function(t){return NO(NO({},t),{},{dy:FO(t)*o})}))}(f,n,a,u);VO(f,n,a,c);for(var h=1,y=1;y<=o;y++)HO(s,f,p,h*=.99),VO(f,n,a,c),XO(s,f,p,h),VO(f,n,a,c);return function(t,e){for(var r=0,n=t.length;r<n;r++){var o=t[r],i=0,a=0;o.targetLinks.sort((function(r,n){return t[e[r].target].y-t[e[n].target].y})),o.sourceLinks.sort((function(r,n){return t[e[r].source].y-t[e[n].source].y}));for(var c=0,u=o.targetLinks.length;c<u;c++){var l=e[o.targetLinks[c]];l&&(l.sy=i,i+=l.dy)}for(var s=0,f=o.sourceLinks.length;s<f;s++){var p=e[o.sourceLinks[s]];p&&(p.ty=a,a+=p.dy)}}}(s,p),{nodes:s,links:p}},YO=function(t){function e(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return RO(t=MO(this,e,[].concat(n)),"state",{activeElement:null,activeElementType:null,isTooltipActive:!1,nodes:[],links:[]}),t}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&DO(t,e)}(e,t),n=e,o=[{key:"handleMouseEnter",value:function(t,e,r){var n=this.props,o=n.onMouseEnter,i=Z(n.children,gr);i?this.setState((function(r){return"hover"===i.props.trigger?NO(NO({},r),{},{activeElement:t,activeElementType:e,isTooltipActive:!0}):r}),(function(){o&&o(t,e,r)})):o&&o(t,e,r)}},{key:"handleMouseLeave",value:function(t,e,r){var n=this.props,o=n.onMouseLeave,i=Z(n.children,gr);i?this.setState((function(t){return"hover"===i.props.trigger?NO(NO({},t),{},{activeElement:void 0,activeElementType:void 0,isTooltipActive:!1}):t}),(function(){o&&o(t,e,r)})):o&&o(t,e,r)}},{key:"handleClick",value:function(t,e,r){var n=this.props,o=n.onClick,i=Z(n.children,gr);i&&"click"===i.props.trigger&&(this.state.isTooltipActive?this.setState((function(t){return NO(NO({},t),{},{activeElement:void 0,activeElementType:void 0,isTooltipActive:!1})})):this.setState((function(r){return NO(NO({},r),{},{activeElement:t,activeElementType:e,isTooltipActive:!0})}))),o&&o(t,e,r)}},{key:"renderLinks",value:function(t,e){var n=this,o=this.props,i=o.linkCurvature,a=o.link,c=o.margin,l=u()(c,"top")||0,s=u()(c,"left")||0;return r().createElement(yt,{className:"recharts-sankey-links",key:"recharts-sankey-links"},t.map((function(t,o){var c=t.sy,u=t.ty,f=t.dy,p=e[t.source],h=e[t.target],y=p.x+p.dx+s,d=h.x+s,v=function(t,e){var r=+t,n=e-r;return function(t){return r+n*t}}(y,d),m=v(i),b=v(1-i),g=NO({sourceX:y,targetX:d,sourceY:p.y+c+f/2+l,targetY:h.y+u+f/2+l,sourceControlX:m,targetControlX:b,sourceRelativeY:c,targetRelativeY:u,linkWidth:f,index:o,payload:NO(NO({},t),{},{source:p,target:h})},nt(a,!1)),x={onMouseEnter:n.handleMouseEnter.bind(n,g,"link"),onMouseLeave:n.handleMouseLeave.bind(n,g,"link"),onClick:n.handleClick.bind(n,g,"link")};return r().createElement(yt,kO({key:"link-".concat(t.source,"-").concat(t.target,"-").concat(t.value)},x),n.constructor.renderLinkItem(a,g))})))}},{key:"renderNodes",value:function(t){var e=this,n=this.props,o=n.node,i=n.margin,a=u()(i,"top")||0,c=u()(i,"left")||0;return r().createElement(yt,{className:"recharts-sankey-nodes",key:"recharts-sankey-nodes"},t.map((function(t,n){var i=t.x,u=t.y,l=t.dx,s=t.dy,f=NO(NO({},nt(o,!1)),{},{x:i+c,y:u+a,width:l,height:s,index:n,payload:t}),p={onMouseEnter:e.handleMouseEnter.bind(e,f,"node"),onMouseLeave:e.handleMouseLeave.bind(e,f,"node"),onClick:e.handleClick.bind(e,f,"node")};return r().createElement(yt,kO({key:"node-".concat(t.x,"-").concat(t.y,"-").concat(t.value)},p),e.constructor.renderNodeItem(o,f))})))}},{key:"renderTooltip",value:function(){var t=this.props,e=t.children,n=t.width,o=t.height,i=t.nameKey,a=Z(e,gr);if(!a)return null;var c,u=this.state,l=u.isTooltipActive,s=u.activeElement,f=u.activeElementType,p={x:0,y:0,width:n,height:o},h=s?(c=s,"node"===f?{x:c.x+c.width/2,y:c.y+c.height/2}:{x:(c.sourceX+c.targetX)/2,y:(c.sourceY+c.targetY)/2}):LO,y=s?function(t,e,r){var n=t.payload;if("node"===e)return[{payload:t,name:Ll(n,r,""),value:Ll(n,"value")}];if(n.source&&n.target){var o=Ll(n.source,r,""),i=Ll(n.target,r,"");return[{payload:t,name:"".concat(o," - ").concat(i),value:Ll(n,"value")}]}return[]}(s,f,i):[];return r().cloneElement(a,{viewBox:p,active:l,coordinate:h,label:"",payload:y})}},{key:"render",value:function(){if(!J(this))return null;var t=this.props,e=t.width,n=t.height,o=t.className,i=t.style,c=t.children,u=EO(t,SO),l=this.state,s=l.links,f=l.nodes,p=nt(u,!1);return r().createElement("div",{className:a("recharts-wrapper",o),style:NO(NO({},i),{},{position:"relative",cursor:"default",width:e,height:n}),role:"region"},r().createElement(st,kO({},p,{width:e,height:n}),rt(c),this.renderLinks(s,f),this.renderNodes(f)),this.renderTooltip())}}],i=[{key:"getDerivedStateFromProps",value:function(t,e){var r=t.data,n=t.width,o=t.height,i=t.margin,a=t.iterations,c=t.nodeWidth,u=t.nodePadding,l=t.sort;if(r!==e.prevData||n!==e.prevWidth||o!==e.prevHeight||!D(i,e.prevMargin)||a!==e.prevIterations||c!==e.prevNodeWidth||u!==e.prevNodePadding||l!==e.sort){var s=n-(i&&i.left||0)-(i&&i.right||0),f=o-(i&&i.top||0)-(i&&i.bottom||0),p=GO({data:r,width:s,height:f,iterations:a,nodeWidth:c,nodePadding:u,sort:l}),h=p.links,y=p.nodes;return NO(NO({},e),{},{nodes:y,links:h,prevData:r,prevWidth:a,prevHeight:o,prevMargin:i,prevNodePadding:u,prevNodeWidth:c,prevIterations:a,prevSort:l})}return null}},{key:"renderLinkItem",value:function(t,e){if(r().isValidElement(t))return r().cloneElement(t,e);if(y()(t))return t(e);var n=e.sourceX,o=e.sourceY,i=e.sourceControlX,a=e.targetX,c=e.targetY,u=e.targetControlX,l=e.linkWidth,s=EO(e,AO);return r().createElement("path",kO({className:"recharts-sankey-link",d:"\n          M".concat(n,",").concat(o,"\n          C").concat(i,",").concat(o," ").concat(u,",").concat(c," ").concat(a,",").concat(c,"\n        "),fill:"none",stroke:"#333",strokeWidth:l,strokeOpacity:"0.2"},nt(s,!1)))}},{key:"renderNodeItem",value:function(t,e){return r().isValidElement(t)?r().cloneElement(t,e):y()(t)?t(e):r().createElement(Uh,kO({className:"recharts-sankey-node",fill:"#0088fe",fillOpacity:"0.8"},nt(e,!1),{role:"img"}))}}],o&&TO(n.prototype,o),i&&TO(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,o,i}(e.PureComponent);RO(YO,"displayName","Sankey"),RO(YO,"defaultProps",{nameKey:"name",dataKey:"value",nodePadding:10,nodeWidth:10,linkCurvature:.5,iterations:32,margin:{top:5,right:5,bottom:5,left:5},sort:!0});var ZO=Gw({chartName:"RadarChart",GraphicalChild:nv,axisComponents:[{axisType:"angleAxis",AxisComp:Zy},{axisType:"radiusAxis",AxisComp:By}],formatAxisMap:Os,defaultProps:{layout:"centric",startAngle:90,endAngle:-270,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}}),JO=Gw({chartName:"ScatterChart",GraphicalChild:Mx,defaultTooltipEventType:"item",validateTooltipEventTypes:["item"],axisComponents:[{axisType:"xAxis",AxisComp:Ux},{axisType:"yAxis",AxisComp:Jx},{axisType:"zAxis",AxisComp:yx}],formatAxisMap:_m}),QO=Gw({chartName:"AreaChart",GraphicalChild:ix,axisComponents:[{axisType:"xAxis",AxisComp:Ux},{axisType:"yAxis",AxisComp:Jx}],formatAxisMap:_m}),tj=Gw({chartName:"RadialBarChart",GraphicalChild:Pv,legendContent:"children",defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"angleAxis",AxisComp:Zy},{axisType:"radiusAxis",AxisComp:By}],formatAxisMap:Os,defaultProps:{layout:"radial",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}}),ej=Gw({chartName:"ComposedChart",GraphicalChild:[$g,ix,Sm,Mx],axisComponents:[{axisType:"xAxis",AxisComp:Ux},{axisType:"yAxis",AxisComp:Jx},{axisType:"zAxis",AxisComp:yx}],formatAxisMap:_m});function rj(){return rj=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},rj.apply(this,arguments)}function nj(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||ij(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function oj(t){return function(t){if(Array.isArray(t))return aj(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||ij(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ij(t,e){if(t){if("string"==typeof t)return aj(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?aj(t,e):void 0}}function aj(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var cj={fontWeight:"bold",paintOrder:"stroke fill",fontSize:".75rem",stroke:"#FFF",fill:"black",pointerEvents:"none"};function uj(t){if(!t.children||0===t.children.length)return 1;var e=t.children.map((function(t){return uj(t)}));return 1+Math.max.apply(Math,oj(e))}var lj,sj=function(t){var n=t.className,o=t.data,i=t.children,c=t.width,u=t.height,l=t.padding,s=void 0===l?2:l,f=t.dataKey,p=void 0===f?"value":f,h=t.ringPadding,y=void 0===h?2:h,d=t.innerRadius,v=void 0===d?50:d,m=t.fill,b=void 0===m?"#333":m,g=t.stroke,x=void 0===g?"#FFF":g,w=t.textOptions,O=void 0===w?cj:w,j=t.outerRadius,S=void 0===j?Math.min(c,u)/2:j,A=t.cx,P=void 0===A?c/2:A,E=t.cy,k=void 0===E?u/2:E,T=t.startAngle,M=void 0===T?0:T,_=t.endAngle,C=void 0===_?360:_,D=t.onClick,I=t.onMouseEnter,N=t.onMouseLeave,R=nj((0,e.useState)(!1),2),B=R[0],L=R[1],z=nj((0,e.useState)(null),2),F=z[0],U=z[1],W=fi([0,o[p]],[0,C]),$=(S-v)/uj(o),K=[],q=new Map([]);function V(t,e){I&&I(t,e),U(t),L(!0)}function X(t,e){N&&N(t,e),U(null),L(!1)}function H(t){D&&D(t)}!function t(e,n){var o=n.radius,i=n.innerR,a=n.initialAngle,c=n.childColor,u=a;e&&e.forEach((function(e){var n,a,l=W(e[p]),f=u,h=null!==(n=null!==(a=null==e?void 0:e.fill)&&void 0!==a?a:c)&&void 0!==n?n:b,d=xs(0,0,i+o/2,-(f+l-l/2)),v=d.x,m=d.y;u+=l,K.push(r().createElement("g",{"aria-label":e.name,tabIndex:0},r().createElement(vf,{onClick:function(){return H(e)},onMouseEnter:function(t){return V(e,t)},onMouseLeave:function(t){return X(e,t)},fill:h,stroke:x,strokeWidth:s,startAngle:f,endAngle:f+l,innerRadius:i,outerRadius:i+o,cx:P,cy:k}),r().createElement(hn,rj({},O,{alignmentBaseline:"middle",textAnchor:"middle",x:v+P,y:k-m}),e[p])));var g=xs(P,k,i+o/2,f),w=g.x,j=g.y;return q.set(e.name,{x:w,y:j}),t(e.children,{radius:o,innerR:i+o+y,initialAngle:f,childColor:h})}))}(o.children,{radius:$,innerR:v,initialAngle:M});var G=a("recharts-sunburst",n);return r().createElement("div",{className:a("recharts-wrapper",n),style:{position:"relative",width:c,height:u},role:"region"},r().createElement(st,{width:c,height:u},i,r().createElement(yt,{className:G},K)),function(){var t=Z([i],gr);if(!t||!F)return null;var e={x:0,y:0,width:c,height:u};return r().cloneElement(t,{viewBox:e,coordinate:q.get(F.name),payload:[F],active:B})}())};function fj(t){return fj="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},fj(t)}function pj(){return pj=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},pj.apply(this,arguments)}function hj(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function yj(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hj(Object(r),!0).forEach((function(e){dj(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hj(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function dj(t,e,r){var n;return n=function(t,e){if("object"!=fj(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=fj(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==fj(n)?n:n+"")in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function vj(t,e){var r="".concat(e.x||t.x),n=parseInt(r,10),o="".concat(e.y||t.y),i=parseInt(o,10),a="".concat((null==e?void 0:e.height)||(null==t?void 0:t.height)),c=parseInt(a,10);return yj(yj(yj({},e),wd(t)),{},{height:c,x:n,y:i})}function mj(t){return r().createElement(Od,pj({shapeType:"trapezoid",propTransformer:vj},t))}function bj(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return gj(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return gj(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function gj(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function xj(t){return xj="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},xj(t)}function wj(){return wj=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},wj.apply(this,arguments)}function Oj(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function jj(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Oj(Object(r),!0).forEach((function(e){Tj(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Oj(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Sj(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Mj(n.key),n)}}function Aj(t,e,r){return e=Ej(e),function(t,e){if(e&&("object"===xj(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Pj()?Reflect.construct(e,r||[],Ej(t).constructor):e.apply(t,r))}function Pj(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(Pj=function(){return!!t})()}function Ej(t){return Ej=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Ej(t)}function kj(t,e){return kj=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},kj(t,e)}function Tj(t,e,r){return(e=Mj(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Mj(t){var e=function(t,e){if("object"!=xj(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=xj(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==xj(e)?e:e+""}var _j=function(t){function e(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return Tj(t=Aj(this,e,[].concat(n)),"state",{isAnimationFinished:!1}),Tj(t,"handleAnimationEnd",(function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),y()(e)&&e()})),Tj(t,"handleAnimationStart",(function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),y()(e)&&e()})),t}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&kj(t,e)}(e,t),n=e,i=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curTrapezoids:t.trapezoids,prevTrapezoids:e.curTrapezoids}:t.trapezoids!==e.curTrapezoids?{curTrapezoids:t.trapezoids}:null}}],(o=[{key:"isActiveIndex",value:function(t){var e=this.props.activeIndex;return Array.isArray(e)?-1!==e.indexOf(t):t===e}},{key:"renderTrapezoidsStatically",value:function(t){var e=this,n=this.props,o=n.shape,i=n.activeShape;return t.map((function(t,n){var a=e.isActiveIndex(n)?i:o,c=jj(jj({},t),{},{isActive:e.isActiveIndex(n),stroke:t.stroke});return r().createElement(yt,wj({className:"recharts-funnel-trapezoid"},F(e.props,t,n),{key:"trapezoid-".concat(null==t?void 0:t.x,"-").concat(null==t?void 0:t.y,"-").concat(null==t?void 0:t.name,"-").concat(null==t?void 0:t.value),role:"img"}),r().createElement(mj,wj({option:a},c)))}))}},{key:"renderTrapezoidsWithAnimation",value:function(){var t=this,e=this.props,n=e.trapezoids,o=e.isAnimationActive,i=e.animationBegin,a=e.animationDuration,c=e.animationEasing,u=e.animationId,l=this.state.prevTrapezoids;return r().createElement(Mh,{begin:i,duration:a,isActive:o,easing:c,from:{t:0},to:{t:1},key:"funnel-".concat(u),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},(function(e){var o=e.t,i=n.map((function(t,e){var r=l&&l[e];if(r){var n=M(r.x,t.x),i=M(r.y,t.y),a=M(r.upperWidth,t.upperWidth),c=M(r.lowerWidth,t.lowerWidth),u=M(r.height,t.height);return jj(jj({},t),{},{x:n(o),y:i(o),upperWidth:a(o),lowerWidth:c(o),height:u(o)})}var s=M(t.x+t.upperWidth/2,t.x),f=M(t.y+t.height/2,t.y),p=M(0,t.upperWidth),h=M(0,t.lowerWidth),y=M(0,t.height);return jj(jj({},t),{},{x:s(o),y:f(o),upperWidth:p(o),lowerWidth:h(o),height:y(o)})}));return r().createElement(yt,null,t.renderTrapezoidsStatically(i))}))}},{key:"renderTrapezoids",value:function(){var t=this.props,e=t.trapezoids,r=t.isAnimationActive,n=this.state.prevTrapezoids;return!(r&&e&&e.length)||n&&Nu()(n,e)?this.renderTrapezoidsStatically(e):this.renderTrapezoidsWithAnimation()}},{key:"render",value:function(){var t=this.props,e=t.hide,n=t.trapezoids,o=t.className,i=t.isAnimationActive,c=this.state.isAnimationFinished;if(e||!n||!n.length)return null;var u=a("recharts-trapezoids",o);return r().createElement(yt,{className:u},this.renderTrapezoids(),(!i||c)&&rf.renderCallByParent(this.props,n))}}])&&Sj(n.prototype,o),i&&Sj(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,o,i}(e.PureComponent);lj=_j,Tj(_j,"displayName","Funnel"),Tj(_j,"defaultProps",{stroke:"#fff",fill:"#808080",legendType:"rect",labelLine:!0,hide:!1,isAnimationActive:!cr.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",lastShapeType:"triangle"}),Tj(_j,"getRealFunnelData",(function(t){var e=t.props,r=e.data,n=e.children,o=nt(t.props,!1),i=Y(n,Tr);return r&&r.length?r.map((function(t,e){return jj(jj(jj({payload:t},o),t),i&&i[e]&&i[e].props)})):i&&i.length?i.map((function(t){return jj(jj({},o),t.props)})):[]})),Tj(_j,"getRealWidthHeight",(function(t,e){var r=t.props.width,n=e.width,o=e.height,i=e.left,a=e.right,c=e.top,u=e.bottom,l=o,s=n;return w()(r)?s=r:p()(r)&&(s=s*parseFloat(r)/100),{realWidth:s-i-a-50,realHeight:l-u-c,offsetX:(n-s)/2,offsetY:(o-l)/2}})),Tj(_j,"getComposedData",(function(t){var e=t.item,r=t.offset,n=lj.getRealFunnelData(e),o=e.props,i=o.dataKey,a=o.nameKey,c=o.tooltipType,u=o.lastShapeType,l=o.reversed,s=r.left,f=r.top,p=lj.getRealWidthHeight(e,r),h=p.realHeight,y=p.realWidth,d=p.offsetX,v=p.offsetY,m=Math.max.apply(null,n.map((function(t){return Ll(t,i,0)}))),b=n.length,g=h/b,x={x:r.left,y:r.top,width:r.width,height:r.height},w=n.map((function(t,e){var r,o=Ll(t,i,0),l=Ll(t,a,e),p=o;if(e!==b-1)(r=Ll(n[e+1],i,0))instanceof Array&&(r=bj(r,1)[0]);else if(o instanceof Array&&2===o.length){var h=bj(o,2);p=h[0],r=h[1]}else r="rectangle"===u?p:0;var w=(m-p)*y/(2*m)+f+25+d,O=g*e+s+v,j=p/m*y,S=r/m*y,A=[{name:l,value:p,payload:t,dataKey:i,type:c}],P={x:w+j/2,y:O+g/2};return jj(jj({x:w,y:O,width:Math.max(j,S),upperWidth:j,lowerWidth:S,height:g,name:l,val:p,tooltipPayload:A,tooltipPosition:P},tO()(t,"width")),{},{payload:t,parentViewBox:x,labelViewBox:{x:w+(j-S)/4,y:O,width:Math.abs(j-S)/2+Math.min(j,S),height:g}})}));return l&&(w=w.map((function(t,e){var r=t.y-e*g+(b-1-e)*g;return jj(jj({},t),{},{upperWidth:t.lowerWidth,lowerWidth:t.upperWidth,x:t.x-(t.lowerWidth-t.upperWidth)/2,y:t.y-e*g+(b-1-e)*g,tooltipPosition:jj(jj({},t.tooltipPosition),{},{y:r+g/2}),labelViewBox:jj(jj({},t.labelViewBox),{},{y:r})})}))),{trapezoids:w,data:n}}));var Cj=Gw({chartName:"FunnelChart",GraphicalChild:_j,validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",axisComponents:[],defaultProps:{layout:"centric"}})})(),i})()));
//# sourceMappingURL=Recharts.js.map