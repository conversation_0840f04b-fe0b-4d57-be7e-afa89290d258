import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { 
  Mail, 
  MapPin, 
  Clock, 
  Calendar,
  User,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react'

const EmployeeCard = ({ employee, schedule }) => {
  const getStatusInfo = () => {
    if (!schedule) {
      return {
        status: 'Sin horario',
        color: 'gray',
        icon: AlertCircle,
        badge: 'badge-info'
      }
    }

    switch (schedule.status) {
      case 'WORK':
        return {
          status: 'Trabajando',
          color: 'green',
          icon: CheckCircle,
          badge: 'badge-success',
          time: schedule.schedule_text && schedule.schedule_text.includes('-')
            ? schedule.schedule_text
            : null
        }
      case 'VACATION':
        return {
          status: 'Vacaciones',
          color: 'yellow',
          icon: Calendar,
          badge: 'badge-warning'
        }
      case 'SICK_LEAVE':
        return {
          status: 'Baja médica',
          color: 'red',
          icon: XCircle,
          badge: 'badge-error'
        }
      case 'HOLIDAY':
        return {
          status: 'Festivo',
          color: 'blue',
          icon: Calendar,
          badge: 'badge-info'
        }
      case 'OTHER_ABSENCE':
        return {
          status: 'Ausencia',
          color: 'orange',
          icon: AlertCircle,
          badge: 'badge-warning'
        }
      default:
        return {
          status: 'Desconocido',
          color: 'gray',
          icon: AlertCircle,
          badge: 'badge-info'
        }
    }
  }

  const statusInfo = getStatusInfo()
  const StatusIcon = statusInfo.icon

  const getDepartmentColor = (department) => {
    const colors = {
      'IBERIA': 'bg-red-100 text-red-800',
      'ITALIA': 'bg-green-100 text-green-800',
      'FRANCIA': 'bg-blue-100 text-blue-800',
      'CROSS OPERACIONES': 'bg-purple-100 text-purple-800',
      'IT SUPPORT': 'bg-yellow-100 text-yellow-800',
      'DATA ANALYST': 'bg-cyan-100 text-cyan-800',
      'KB': 'bg-lime-100 text-lime-800',
      'TRAINERS': 'bg-orange-100 text-orange-800',
      'HR': 'bg-pink-100 text-pink-800',
      'ACTIVE CALL': 'bg-indigo-100 text-indigo-800',
    }
    return colors[department] || 'bg-gray-100 text-gray-800'
  }

  const getCountryFlag = (countryCode) => {
    const flags = {
      'ES': '🇪🇸',
      'IT': '🇮🇹',
      'FR': '🇫🇷',
      'DE': '🇩🇪',
      'BR': '🇧🇷',
    }
    return flags[countryCode] || '🌍'
  }

  return (
    <div className="card hover:shadow-md transition-shadow duration-200">
      <div className="card-content">
        {/* Header con avatar y estado */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center">
            <div className="h-12 w-12 bg-gradient-to-r from-primary-500 to-primary-600 rounded-full flex items-center justify-center">
              <span className="text-white font-semibold text-lg">
                {employee.name.charAt(0).toUpperCase()}
              </span>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-semibold text-gray-900">
                {employee.full_name}
              </h3>
              <p className="text-xs text-gray-500 flex items-center">
                <span className="mr-1">{getCountryFlag(employee.country_code)}</span>
                {employee.employee_id}
              </p>
            </div>
          </div>
          
          <div className="flex items-center">
            <StatusIcon className={`h-5 w-5 text-${statusInfo.color}-500`} />
          </div>
        </div>

        {/* Información del empleado */}
        <div className="space-y-2 mb-4">
          <div className="flex items-center text-sm text-gray-600">
            <Mail className="h-4 w-4 mr-2 text-gray-400" />
            <span className="truncate">{employee.email}</span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className={`badge ${getDepartmentColor(employee.department)}`}>
              {employee.department}
            </span>
          </div>
        </div>

        {/* Estado actual */}
        <div className="border-t pt-3">
          <div className="flex items-center justify-between">
            <div>
              <span className={`badge ${statusInfo.badge}`}>
                {statusInfo.status}
              </span>
              {statusInfo.time && (
                <div className="flex items-center mt-1 text-xs text-gray-500">
                  <Clock className="h-3 w-3 mr-1" />
                  {statusInfo.time}
                </div>
              )}
            </div>
            
            <Link
              to={`/employee/${employee.id}`}
              className="text-primary-600 hover:text-primary-700 text-sm font-medium"
            >
              Ver detalles
            </Link>
          </div>
          
          {schedule?.schedule_text && schedule.status !== 'WORK' && (
            <div className="mt-2 text-xs text-gray-500 bg-gray-50 rounded p-2">
              {schedule.schedule_text}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default EmployeeCard
