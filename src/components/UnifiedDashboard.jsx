import React, { useState, useEffect } from 'react'
import { 
  Users, Building2, Home, Wifi, Clock, Globe, 
  Coffee, Monitor, MapPin, RefreshCw, Filter,
  Calendar, Mail, Phone, Video
} from 'lucide-react'

const UnifiedDashboard = ({ employees, departments, schedules, onRefresh }) => {
  const [currentTime, setCurrentTime] = useState(new Date())
  const [selectedFilter, setSelectedFilter] = useState('all')
  const [refreshing, setRefreshing] = useState(false)

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)
    return () => clearInterval(timer)
  }, [])

  const handleRefresh = async () => {
    setRefreshing(true)
    await onRefresh()
    setTimeout(() => setRefreshing(false), 1000)
  }

  // Estadísticas principales
  const stats = {
    total: employees.length,
    office: employees.filter(emp => emp.work_location === 'OFFICE').length,
    remote: employees.filter(emp => emp.work_location === 'REMOTE').length,
    hybrid: employees.filter(emp => emp.work_location === 'HYBRID').length,
    online: employees.filter(emp => emp.current_status === 'ONLINE').length,
    break: employees.filter(emp => emp.current_status === 'BREAK').length,
    vacation: schedules.filter(s => s.status === 'VACATION').length
  }

  // Filtrar empleados
  const filteredEmployees = selectedFilter === 'all' 
    ? employees 
    : selectedFilter === 'office' 
    ? employees.filter(emp => emp.work_location === 'OFFICE')
    : selectedFilter === 'remote'
    ? employees.filter(emp => emp.work_location === 'REMOTE')
    : selectedFilter === 'hybrid'
    ? employees.filter(emp => emp.work_location === 'HYBRID')
    : employees.filter(emp => emp.department === selectedFilter)

  const getCountryFlag = (countryCode) => {
    const flags = {
      'ES': '🇪🇸', 'IT': '🇮🇹', 'FR': '🇫🇷', 'DE': '🇩🇪', 'BR': '🇧🇷'
    }
    return flags[countryCode] || '🌍'
  }

  const getLocalTime = (timezone) => {
    try {
      return new Date().toLocaleTimeString('es-ES', {
        timeZone: timezone || 'Europe/Madrid',
        hour: '2-digit',
        minute: '2-digit'
      })
    } catch {
      return currentTime.toLocaleTimeString('es-ES', {
        hour: '2-digit',
        minute: '2-digit'
      })
    }
  }

  const getStatusColor = (status, location) => {
    if (status === 'BREAK') return 'bg-yellow-500'
    if (status === 'ONLINE') {
      if (location === 'OFFICE') return 'bg-blue-500'
      if (location === 'REMOTE') return 'bg-green-500'
      if (location === 'HYBRID') return 'bg-purple-500'
    }
    return 'bg-gray-500'
  }

  const getLocationIcon = (location) => {
    switch (location) {
      case 'OFFICE': return Building2
      case 'REMOTE': return Home
      case 'HYBRID': return Wifi
      default: return MapPin
    }
  }

  return (
    <div className="min-h-screen p-6">
      {/* Header Principal */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-4xl font-bold text-gray-900 mb-2">
              MSX Super Team
            </h1>
            <p className="text-xl text-gray-600">
              Dashboard Centralizado • {currentTime.toLocaleDateString('es-ES', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}
            </p>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="text-right">
              <div className="text-3xl font-bold text-gray-900">
                {currentTime.toLocaleTimeString('es-ES')}
              </div>
              <div className="flex items-center text-green-600">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-2"></div>
                <span className="text-sm font-medium">En vivo</span>
              </div>
            </div>
            
            <button
              onClick={handleRefresh}
              className={`p-3 bg-white rounded-full shadow-lg hover:shadow-xl transition-all ${
                refreshing ? 'animate-spin' : ''
              }`}
            >
              <RefreshCw className="h-6 w-6 text-blue-600" />
            </button>
          </div>
        </div>
      </div>

      {/* Estadísticas Principales */}
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4 mb-8">
        <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-500 mb-1">Total</p>
              <p className="text-3xl font-bold text-gray-900">{stats.total}</p>
            </div>
            <Users className="h-8 w-8 text-gray-600" />
          </div>
        </div>

        <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl p-6 shadow-lg text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-blue-100 text-sm mb-1">Oficina</p>
              <p className="text-3xl font-bold">{stats.office}</p>
            </div>
            <Building2 className="h-8 w-8 text-blue-200" />
          </div>
        </div>

        <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-2xl p-6 shadow-lg text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-green-100 text-sm mb-1">Remoto</p>
              <p className="text-3xl font-bold">{stats.remote}</p>
            </div>
            <Home className="h-8 w-8 text-green-200" />
          </div>
        </div>

        <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-2xl p-6 shadow-lg text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-purple-100 text-sm mb-1">Híbrido</p>
              <p className="text-3xl font-bold">{stats.hybrid}</p>
            </div>
            <Wifi className="h-8 w-8 text-purple-200" />
          </div>
        </div>

        <div className="bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-2xl p-6 shadow-lg text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-emerald-100 text-sm mb-1">Online</p>
              <p className="text-3xl font-bold">{stats.online}</p>
            </div>
            <Monitor className="h-8 w-8 text-emerald-200" />
          </div>
        </div>

        <div className="bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-2xl p-6 shadow-lg text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-yellow-100 text-sm mb-1">Descanso</p>
              <p className="text-3xl font-bold">{stats.break}</p>
            </div>
            <Coffee className="h-8 w-8 text-yellow-200" />
          </div>
        </div>

        <div className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-2xl p-6 shadow-lg text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-orange-100 text-sm mb-1">Vacaciones</p>
              <p className="text-3xl font-bold">{stats.vacation}</p>
            </div>
            <Calendar className="h-8 w-8 text-orange-200" />
          </div>
        </div>
      </div>

      {/* Filtros */}
      <div className="mb-6">
        <div className="flex flex-wrap gap-3">
          <button
            onClick={() => setSelectedFilter('all')}
            className={`px-6 py-3 rounded-xl font-medium transition-all ${
              selectedFilter === 'all'
                ? 'bg-gray-900 text-white shadow-lg'
                : 'bg-white text-gray-700 hover:bg-gray-50 shadow'
            }`}
          >
            <Filter className="h-4 w-4 inline mr-2" />
            Todos ({stats.total})
          </button>
          
          <button
            onClick={() => setSelectedFilter('office')}
            className={`px-6 py-3 rounded-xl font-medium transition-all ${
              selectedFilter === 'office'
                ? 'bg-blue-600 text-white shadow-lg'
                : 'bg-white text-gray-700 hover:bg-blue-50 shadow'
            }`}
          >
            <Building2 className="h-4 w-4 inline mr-2" />
            Oficina ({stats.office})
          </button>
          
          <button
            onClick={() => setSelectedFilter('remote')}
            className={`px-6 py-3 rounded-xl font-medium transition-all ${
              selectedFilter === 'remote'
                ? 'bg-green-600 text-white shadow-lg'
                : 'bg-white text-gray-700 hover:bg-green-50 shadow'
            }`}
          >
            <Home className="h-4 w-4 inline mr-2" />
            Remoto ({stats.remote})
          </button>
          
          <button
            onClick={() => setSelectedFilter('hybrid')}
            className={`px-6 py-3 rounded-xl font-medium transition-all ${
              selectedFilter === 'hybrid'
                ? 'bg-purple-600 text-white shadow-lg'
                : 'bg-white text-gray-700 hover:bg-purple-50 shadow'
            }`}
          >
            <Wifi className="h-4 w-4 inline mr-2" />
            Híbrido ({stats.hybrid})
          </button>
        </div>
      </div>

      {/* Grid de Empleados */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6">
        {filteredEmployees.map((employee) => {
          const LocationIcon = getLocationIcon(employee.work_location)
          const schedule = schedules.find(s => s.employee_id === employee.id)
          
          return (
            <div
              key={employee.id}
              className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 hover:scale-105"
            >
              {/* Header con avatar y estado */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center">
                  <div className="relative">
                    <div 
                      className="h-14 w-14 rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg"
                      style={{ backgroundColor: employee.avatar_color || '#6B7280' }}
                    >
                      {employee.name.charAt(0)}
                    </div>
                    <div 
                      className={`absolute -bottom-1 -right-1 w-5 h-5 rounded-full border-2 border-white ${
                        getStatusColor(employee.current_status, employee.work_location)
                      }`}
                    ></div>
                  </div>
                  <div className="ml-3">
                    <h3 className="font-semibold text-gray-900 text-lg">
                      {employee.name}
                    </h3>
                    <p className="text-sm text-gray-500">
                      {employee.department}
                    </p>
                  </div>
                </div>
              </div>

              {/* Información principal */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <LocationIcon className="h-4 w-4 text-gray-500 mr-2" />
                    <span className="text-sm text-gray-600">
                      {employee.work_location === 'OFFICE' ? 'Oficina' :
                       employee.work_location === 'REMOTE' ? 'Remoto' : 'Híbrido'}
                    </span>
                  </div>
                  <span className="text-sm font-medium text-gray-900">
                    {getCountryFlag(employee.country_code)} {employee.country_code}
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Clock className="h-4 w-4 text-gray-500 mr-2" />
                    <span className="text-sm text-gray-600">Hora local</span>
                  </div>
                  <span className="text-sm font-medium text-gray-900">
                    {getLocalTime(employee.timezone)}
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Estado</span>
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                    employee.current_status === 'ONLINE' ? 'bg-green-100 text-green-800' :
                    employee.current_status === 'BREAK' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {employee.current_status === 'ONLINE' ? 'Online' :
                     employee.current_status === 'BREAK' ? 'Descanso' : 'Offline'}
                  </span>
                </div>

                {schedule && schedule.status === 'VACATION' && (
                  <div className="bg-blue-50 rounded-lg p-3 border border-blue-200">
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 text-blue-600 mr-2" />
                      <span className="text-sm font-medium text-blue-800">
                        De vacaciones
                      </span>
                    </div>
                  </div>
                )}
              </div>

              {/* Acciones */}
              <div className="flex justify-between items-center mt-4 pt-4 border-t border-gray-100">
                <div className="flex space-x-2">
                  <button className="p-2 bg-blue-100 text-blue-600 rounded-lg hover:bg-blue-200 transition-colors">
                    <Mail className="h-4 w-4" />
                  </button>
                  <button className="p-2 bg-green-100 text-green-600 rounded-lg hover:bg-green-200 transition-colors">
                    <Phone className="h-4 w-4" />
                  </button>
                  <button className="p-2 bg-purple-100 text-purple-600 rounded-lg hover:bg-purple-200 transition-colors">
                    <Video className="h-4 w-4" />
                  </button>
                </div>
                
                <span className="text-xs text-gray-400">
                  {employee.employee_id}
                </span>
              </div>
            </div>
          )
        })}
      </div>

      {filteredEmployees.length === 0 && (
        <div className="text-center py-16">
          <Users className="h-20 w-20 text-gray-300 mx-auto mb-4" />
          <p className="text-xl text-gray-500">No hay empleados para mostrar</p>
        </div>
      )}
    </div>
  )
}

export default UnifiedDashboard
