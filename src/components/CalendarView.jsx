import React, { useState, useEffect } from 'react'
import { ChevronLeft, ChevronRight, Calendar, Filter } from 'lucide-react'
import { api } from '../lib/supabase'

const CalendarView = ({ employees, departments }) => {
  const [currentDate, setCurrentDate] = useState(new Date())
  const [schedules, setSchedules] = useState([])
  const [loading, setLoading] = useState(false)
  const [selectedDepartment, setSelectedDepartment] = useState('all')

  useEffect(() => {
    loadMonthSchedules()
  }, [currentDate])

  const loadMonthSchedules = async () => {
    try {
      setLoading(true)
      const startOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1)
      const endOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0)
      
      const startDate = startOfMonth.toISOString().split('T')[0]
      const endDate = endOfMonth.toISOString().split('T')[0]
      
      const schedulesData = await api.getSchedules(startDate, endDate)
      setSchedules(schedulesData)
    } catch (error) {
      console.error('Error loading schedules:', error)
    } finally {
      setLoading(false)
    }
  }

  const getDaysInMonth = () => {
    const year = currentDate.getFullYear()
    const month = currentDate.getMonth()
    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    const daysInMonth = lastDay.getDate()
    const startingDayOfWeek = firstDay.getDay()

    const days = []
    
    // Días del mes anterior para completar la primera semana
    for (let i = startingDayOfWeek - 1; i >= 0; i--) {
      const prevDate = new Date(year, month, -i)
      days.push({ date: prevDate, isCurrentMonth: false })
    }
    
    // Días del mes actual
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month, day)
      days.push({ date, isCurrentMonth: true })
    }
    
    // Días del mes siguiente para completar la última semana
    const remainingDays = 42 - days.length // 6 semanas * 7 días
    for (let day = 1; day <= remainingDays; day++) {
      const nextDate = new Date(year, month + 1, day)
      days.push({ date: nextDate, isCurrentMonth: false })
    }
    
    return days
  }

  const getSchedulesForDate = (date) => {
    const dateStr = date.toISOString().split('T')[0]
    return schedules.filter(schedule => schedule.schedule_date === dateStr)
  }

  const filteredEmployees = selectedDepartment === 'all' 
    ? employees 
    : employees.filter(emp => emp.department === selectedDepartment)

  const getStatusColor = (status) => {
    switch (status) {
      case 'WORK': return 'bg-green-500'
      case 'VACATION': return 'bg-yellow-500'
      case 'SICK_LEAVE': return 'bg-red-500'
      case 'HOLIDAY': return 'bg-blue-500'
      case 'OTHER_ABSENCE': return 'bg-orange-500'
      default: return 'bg-gray-500'
    }
  }

  const navigateMonth = (direction) => {
    const newDate = new Date(currentDate)
    newDate.setMonth(currentDate.getMonth() + direction)
    setCurrentDate(newDate)
  }

  const days = getDaysInMonth()
  const monthNames = [
    'Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio',
    'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'
  ]
  const dayNames = ['Dom', 'Lun', 'Mar', 'Mié', 'Jue', 'Vie', 'Sáb']

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <Calendar className="h-6 w-6 mr-2" />
            Calendario de Horarios
          </h1>
          <p className="text-gray-600">
            Vista mensual de horarios y ausencias
          </p>
        </div>
        
        {/* Filtro por departamento */}
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-gray-500" />
            <select
              value={selectedDepartment}
              onChange={(e) => setSelectedDepartment(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="all">Todos los Departamentos</option>
              {departments.map(dept => (
                <option key={dept.id} value={dept.name}>
                  {dept.name}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Navegación del calendario */}
      <div className="card">
        <div className="card-content">
          <div className="flex justify-between items-center mb-6">
            <button
              onClick={() => navigateMonth(-1)}
              className="btn btn-secondary"
            >
              <ChevronLeft className="h-4 w-4" />
            </button>
            
            <h2 className="text-xl font-semibold text-gray-900">
              {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
            </h2>
            
            <button
              onClick={() => navigateMonth(1)}
              className="btn btn-secondary"
            >
              <ChevronRight className="h-4 w-4" />
            </button>
          </div>

          {/* Días de la semana */}
          <div className="grid grid-cols-7 gap-1 mb-2">
            {dayNames.map(day => (
              <div key={day} className="p-2 text-center text-sm font-medium text-gray-500">
                {day}
              </div>
            ))}
          </div>

          {/* Calendario */}
          <div className="grid grid-cols-7 gap-1">
            {days.map((day, index) => {
              const daySchedules = getSchedulesForDate(day.date)
              const isToday = day.date.toDateString() === new Date().toDateString()
              
              return (
                <div
                  key={index}
                  className={`
                    min-h-[120px] p-2 border border-gray-200 
                    ${day.isCurrentMonth ? 'bg-white' : 'bg-gray-50'}
                    ${isToday ? 'ring-2 ring-primary-500' : ''}
                  `}
                >
                  <div className={`
                    text-sm font-medium mb-2
                    ${day.isCurrentMonth ? 'text-gray-900' : 'text-gray-400'}
                    ${isToday ? 'text-primary-600' : ''}
                  `}>
                    {day.date.getDate()}
                  </div>
                  
                  <div className="space-y-1">
                    {daySchedules
                      .filter(schedule =>
                        selectedDepartment === 'all' ||
                        filteredEmployees.some(emp => emp.id === schedule.employee_id)
                      )
                      .slice(0, 3)
                      .map((schedule, idx) => {
                        const employee = employees.find(emp => emp.id === schedule.employee_id)
                        return (
                          <div
                            key={idx}
                            className={`
                              text-xs p-1 rounded text-white truncate
                              ${getStatusColor(schedule.status)}
                            `}
                            title={`${employee?.full_name} - ${schedule.status}`}
                          >
                            {employee?.name}
                          </div>
                        )
                      })}
                    
                    {daySchedules.length > 3 && (
                      <div className="text-xs text-gray-500 text-center">
                        +{daySchedules.length - 3} más
                      </div>
                    )}
                  </div>
                </div>
              )
            })}
          </div>

          {/* Leyenda */}
          <div className="mt-6 flex flex-wrap gap-4 text-sm">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-green-500 rounded mr-2"></div>
              <span>Trabajando</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-yellow-500 rounded mr-2"></div>
              <span>Vacaciones</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-red-500 rounded mr-2"></div>
              <span>Baja médica</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-blue-500 rounded mr-2"></div>
              <span>Festivo</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-orange-500 rounded mr-2"></div>
              <span>Otras ausencias</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CalendarView
