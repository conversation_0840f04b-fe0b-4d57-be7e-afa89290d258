import React from 'react'
import { Link } from 'react-router-dom'
import { 
  Mail, MapPin, Clock, Calendar, Home, Building2, Wifi,
  CheckCircle, AlertCircle, Coffee, Video, Phone, UserX, Monitor
} from 'lucide-react'

const EmployeeLocationCard = ({ employee, schedule }) => {
  const getLocationInfo = () => {
    switch (employee.work_location) {
      case 'OFFICE':
        return {
          icon: Building2,
          label: 'Oficina',
          color: 'blue',
          bgColor: 'bg-blue-50 border-blue-200',
          textColor: 'text-blue-700',
          iconColor: 'text-blue-600'
        }
      case 'REMOTE':
        return {
          icon: Home,
          label: '<PERSON>moto',
          color: 'green',
          bgColor: 'bg-green-50 border-green-200',
          textColor: 'text-green-700',
          iconColor: 'text-green-600'
        }
      case 'HYBRID':
        return {
          icon: Wifi,
          label: 'Híbrido',
          color: 'purple',
          bgColor: 'bg-purple-50 border-purple-200',
          textColor: 'text-purple-700',
          iconColor: 'text-purple-600'
        }
      default:
        return {
          icon: MapPin,
          label: 'No definido',
          color: 'gray',
          bgColor: 'bg-gray-50 border-gray-200',
          textColor: 'text-gray-700',
          iconColor: 'text-gray-600'
        }
    }
  }

  const getStatusInfo = () => {
    switch (employee.current_status) {
      case 'AVAILABLE':
        return {
          icon: CheckCircle,
          label: 'Disponible',
          color: 'green',
          dotColor: 'bg-green-500',
          badge: 'bg-green-100 text-green-800'
        }
      case 'BUSY':
        return {
          icon: AlertCircle,
          label: 'Ocupado',
          color: 'red',
          dotColor: 'bg-red-500',
          badge: 'bg-red-100 text-red-800'
        }
      case 'MEETING':
        return {
          icon: Video,
          label: 'En reunión',
          color: 'orange',
          dotColor: 'bg-orange-500',
          badge: 'bg-orange-100 text-orange-800'
        }
      case 'BREAK':
        return {
          icon: Coffee,
          label: 'En descanso',
          color: 'yellow',
          dotColor: 'bg-yellow-500',
          badge: 'bg-yellow-100 text-yellow-800'
        }
      case 'OFFLINE':
        return {
          icon: UserX,
          label: 'Desconectado',
          color: 'gray',
          dotColor: 'bg-gray-500',
          badge: 'bg-gray-100 text-gray-800'
        }
      default:
        return {
          icon: AlertCircle,
          label: 'Desconocido',
          color: 'gray',
          dotColor: 'bg-gray-500',
          badge: 'bg-gray-100 text-gray-800'
        }
    }
  }

  const getScheduleInfo = () => {
    if (!schedule) return null
    
    switch (schedule.status) {
      case 'WORK':
        return {
          label: 'Trabajando',
          color: 'green',
          badge: 'bg-green-100 text-green-800',
          time: schedule.schedule_text && schedule.schedule_text.includes('-')
            ? schedule.schedule_text
            : null
        }
      case 'VACATION':
        return {
          label: 'Vacaciones',
          color: 'blue',
          badge: 'bg-blue-100 text-blue-800'
        }
      case 'SICK_LEAVE':
        return {
          label: 'Baja médica',
          color: 'red',
          badge: 'bg-red-100 text-red-800'
        }
      default:
        return {
          label: schedule.status,
          color: 'gray',
          badge: 'bg-gray-100 text-gray-800'
        }
    }
  }

  const locationInfo = getLocationInfo()
  const statusInfo = getStatusInfo()
  const scheduleInfo = getScheduleInfo()
  const LocationIcon = locationInfo.icon
  const StatusIcon = statusInfo.icon

  const getCountryFlag = (countryCode) => {
    const flags = {
      'ES': '🇪🇸',
      'IT': '🇮🇹',
      'FR': '🇫🇷',
      'DE': '🇩🇪',
      'BR': '🇧🇷',
    }
    return flags[countryCode] || '🌍'
  }

  const getCurrentTime = () => {
    try {
      return new Date().toLocaleTimeString('es-ES', {
        timeZone: employee.timezone || 'Europe/Madrid',
        hour: '2-digit',
        minute: '2-digit'
      })
    } catch {
      return new Date().toLocaleTimeString('es-ES', {
        hour: '2-digit',
        minute: '2-digit'
      })
    }
  }

  return (
    <div className={`card hover:shadow-lg transition-all duration-300 border-2 ${locationInfo.bgColor} hover:scale-105`}>
      <div className="card-content relative">
        {/* Status indicator */}
        <div className="absolute top-4 right-4 flex items-center space-x-2">
          <div className={`w-3 h-3 rounded-full ${statusInfo.dotColor} animate-pulse`}></div>
          <span className={`badge ${statusInfo.badge} text-xs`}>
            {statusInfo.label}
          </span>
        </div>

        {/* Avatar y información principal */}
        <div className="flex items-start space-x-4 mb-4">
          <div 
            className="h-16 w-16 rounded-full flex items-center justify-center text-white font-bold text-xl shadow-lg"
            style={{ backgroundColor: employee.avatar_color || '#3B82F6' }}
          >
            {employee.name.charAt(0).toUpperCase()}
          </div>
          
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-semibold text-gray-900 truncate">
              {employee.name}
            </h3>
            <p className="text-sm text-gray-600 flex items-center">
              <span className="mr-1">{getCountryFlag(employee.country_code)}</span>
              {employee.department}
            </p>
            <p className="text-xs text-gray-500 mt-1">
              {employee.employee_id}
            </p>
          </div>
        </div>

        {/* Ubicación de trabajo */}
        <div className={`flex items-center p-3 rounded-lg mb-3 ${locationInfo.bgColor}`}>
          <LocationIcon className={`h-5 w-5 mr-3 ${locationInfo.iconColor}`} />
          <div className="flex-1">
            <p className={`font-medium ${locationInfo.textColor}`}>
              {locationInfo.label}
            </p>
            <p className="text-xs text-gray-500">
              Hora local: {getCurrentTime()}
            </p>
          </div>
        </div>

        {/* Estado actual y horario */}
        <div className="space-y-2 mb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <StatusIcon className={`h-4 w-4 mr-2 text-${statusInfo.color}-500`} />
              <span className="text-sm font-medium text-gray-700">
                Estado actual
              </span>
            </div>
            <span className={`badge ${statusInfo.badge}`}>
              {statusInfo.label}
            </span>
          </div>

          {scheduleInfo && (
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                <span className="text-sm font-medium text-gray-700">
                  Horario hoy
                </span>
              </div>
              <span className={`badge ${scheduleInfo.badge}`}>
                {scheduleInfo.label}
              </span>
            </div>
          )}

          {scheduleInfo?.time && (
            <div className="flex items-center text-sm text-gray-600 bg-gray-50 rounded p-2">
              <Clock className="h-3 w-3 mr-2" />
              {scheduleInfo.time}
            </div>
          )}
        </div>

        {/* Información de contacto */}
        <div className="border-t pt-3">
          <div className="flex items-center text-sm text-gray-600 mb-2">
            <Mail className="h-4 w-4 mr-2 text-gray-400" />
            <span className="truncate">{employee.email}</span>
          </div>
          
          <div className="flex justify-between items-center">
            <div className="flex space-x-2">
              <button className="p-2 bg-blue-100 text-blue-600 rounded-full hover:bg-blue-200 transition-colors">
                <Mail className="h-4 w-4" />
              </button>
              <button className="p-2 bg-green-100 text-green-600 rounded-full hover:bg-green-200 transition-colors">
                <Phone className="h-4 w-4" />
              </button>
              <button className="p-2 bg-purple-100 text-purple-600 rounded-full hover:bg-purple-200 transition-colors">
                <Video className="h-4 w-4" />
              </button>
            </div>
            
            <Link
              to={`/employee/${employee.id}`}
              className="text-blue-600 hover:text-blue-700 text-sm font-medium flex items-center"
            >
              <Monitor className="h-4 w-4 mr-1" />
              Ver perfil
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}

export default EmployeeLocationCard
