import React, { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom'
import { 
  ArrowLeft, 
  Mail, 
  MapPin, 
  Calendar, 
  Clock,
  User,
  Building2,
  Phone,
  Edit
} from 'lucide-react'
import { api } from '../lib/supabase'

const EmployeeView = ({ employees }) => {
  const { id } = useParams()
  const [employee, setEmployee] = useState(null)
  const [schedules, setSchedules] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const foundEmployee = employees.find(emp => emp.id === id)
    setEmployee(foundEmployee)
    
    if (foundEmployee) {
      loadEmployeeSchedules(foundEmployee.employee_id)
    }
  }, [id, employees])

  const loadEmployeeSchedules = async (employeeId) => {
    try {
      setLoading(true)
      // Cargar horarios del último mes
      const endDate = new Date()
      const startDate = new Date()
      startDate.setMonth(startDate.getMonth() - 1)
      
      const schedulesData = await api.getEmployeeSchedules(
        employeeId,
        startDate.toISOString().split('T')[0],
        endDate.toISOString().split('T')[0]
      )
      setSchedules(schedulesData)
    } catch (error) {
      console.error('Error loading employee schedules:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusInfo = (status) => {
    switch (status) {
      case 'WORK':
        return { label: 'Trabajando', color: 'green', bgColor: 'bg-green-100 text-green-800' }
      case 'VACATION':
        return { label: 'Vacaciones', color: 'yellow', bgColor: 'bg-yellow-100 text-yellow-800' }
      case 'SICK_LEAVE':
        return { label: 'Baja médica', color: 'red', bgColor: 'bg-red-100 text-red-800' }
      case 'HOLIDAY':
        return { label: 'Festivo', color: 'blue', bgColor: 'bg-blue-100 text-blue-800' }
      case 'OTHER_ABSENCE':
        return { label: 'Otras ausencias', color: 'orange', bgColor: 'bg-orange-100 text-orange-800' }
      default:
        return { label: 'Desconocido', color: 'gray', bgColor: 'bg-gray-100 text-gray-800' }
    }
  }

  const getCountryFlag = (countryCode) => {
    const flags = {
      'ES': '🇪🇸',
      'IT': '🇮🇹',
      'FR': '🇫🇷',
      'DE': '🇩🇪',
      'BR': '🇧🇷',
    }
    return flags[countryCode] || '🌍'
  }

  const getDepartmentColor = (department) => {
    const colors = {
      'IBERIA': 'bg-red-100 text-red-800',
      'ITALIA': 'bg-green-100 text-green-800',
      'FRANCIA': 'bg-blue-100 text-blue-800',
      'CROSS OPERACIONES': 'bg-purple-100 text-purple-800',
      'IT SUPPORT': 'bg-yellow-100 text-yellow-800',
      'DATA ANALYST': 'bg-cyan-100 text-cyan-800',
      'KB': 'bg-lime-100 text-lime-800',
      'TRAINERS': 'bg-orange-100 text-orange-800',
      'HR': 'bg-pink-100 text-pink-800',
      'ACTIVE CALL': 'bg-indigo-100 text-indigo-800',
    }
    return colors[department] || 'bg-gray-100 text-gray-800'
  }

  if (!employee) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-lg text-gray-600">Empleado no encontrado</p>
          <Link to="/" className="btn btn-primary mt-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Volver al Dashboard
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Link to="/" className="btn btn-secondary mr-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Volver
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Perfil del Empleado
            </h1>
            <p className="text-gray-600">
              Información detallada y horarios
            </p>
          </div>
        </div>
        
        <button className="btn btn-primary">
          <Edit className="h-4 w-4 mr-2" />
          Editar
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Información del empleado */}
        <div className="lg:col-span-1">
          <div className="card">
            <div className="card-content">
              {/* Avatar y nombre */}
              <div className="text-center mb-6">
                <div className="h-24 w-24 bg-gradient-to-r from-primary-500 to-primary-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white font-bold text-2xl">
                    {employee.name.charAt(0).toUpperCase()}
                  </span>
                </div>
                <h2 className="text-xl font-bold text-gray-900">
                  {employee.full_name}
                </h2>
                <p className="text-gray-500 flex items-center justify-center mt-1">
                  <span className="mr-2">{getCountryFlag(employee.country_code)}</span>
                  {employee.employee_id}
                </p>
              </div>

              {/* Información de contacto */}
              <div className="space-y-4">
                <div className="flex items-center">
                  <Mail className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <p className="text-sm text-gray-500">Email</p>
                    <p className="text-sm font-medium text-gray-900">
                      {employee.email}
                    </p>
                  </div>
                </div>

                <div className="flex items-center">
                  <Building2 className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <p className="text-sm text-gray-500">Departamento</p>
                    <span className={`badge ${getDepartmentColor(employee.department)}`}>
                      {employee.department}
                    </span>
                  </div>
                </div>

                <div className="flex items-center">
                  <MapPin className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <p className="text-sm text-gray-500">País</p>
                    <p className="text-sm font-medium text-gray-900">
                      {getCountryFlag(employee.country_code)} {employee.country_code}
                    </p>
                  </div>
                </div>

                <div className="flex items-center">
                  <User className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <p className="text-sm text-gray-500">Estado</p>
                    <span className="badge badge-success">
                      {employee.active ? 'Activo' : 'Inactivo'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Horarios recientes */}
        <div className="lg:col-span-2">
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <Calendar className="h-5 w-5 mr-2" />
                Horarios Recientes
              </h3>
              <p className="text-sm text-gray-500">
                Últimos 30 días
              </p>
            </div>
            <div className="card-content">
              {loading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
                  <p className="mt-2 text-sm text-gray-500">Cargando horarios...</p>
                </div>
              ) : schedules.length === 0 ? (
                <div className="text-center py-8">
                  <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No hay horarios registrados</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {schedules
                    .sort((a, b) => new Date(b.schedule_date) - new Date(a.schedule_date))
                    .slice(0, 10)
                    .map((schedule) => {
                      const statusInfo = getStatusInfo(schedule.status)
                      const date = new Date(schedule.schedule_date)

                      return (
                        <div key={schedule.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div className="flex items-center">
                            <div className="text-sm">
                              <p className="font-medium text-gray-900">
                                {date.toLocaleDateString('es-ES', {
                                  weekday: 'long',
                                  year: 'numeric',
                                  month: 'long',
                                  day: 'numeric'
                                })}
                              </p>
                              {schedule.schedule_text && schedule.schedule_text.includes('-') && (
                                <p className="text-gray-500 flex items-center mt-1">
                                  <Clock className="h-3 w-3 mr-1" />
                                  {schedule.schedule_text}
                                </p>
                              )}
                            </div>
                          </div>

                          <div className="text-right">
                            <span className={`badge ${statusInfo.bgColor}`}>
                              {statusInfo.label}
                            </span>
                            {schedule.schedule_text && schedule.status !== 'WORK' && (
                              <p className="text-xs text-gray-500 mt-1 max-w-xs truncate">
                                {schedule.schedule_text}
                              </p>
                            )}
                          </div>
                        </div>
                      )
                    })}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default EmployeeView
