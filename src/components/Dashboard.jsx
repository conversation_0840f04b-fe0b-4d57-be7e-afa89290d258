import React, { useState, useEffect } from 'react'
import { Users, Calendar, Clock, TrendingUp, Building2, UserCheck } from 'lucide-react'
import EmployeeCard from './EmployeeCard'
import DepartmentStats from './DepartmentStats'
import { api } from '../lib/supabase'

const Dashboard = ({ employees, departments, selectedDepartment }) => {
  const [schedules, setSchedules] = useState([])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    loadTodaySchedules()
  }, [])

  const loadTodaySchedules = async () => {
    try {
      setLoading(true)
      const today = new Date().toISOString().split('T')[0]
      const schedulesData = await api.getSchedules(today, today)
      setSchedules(schedulesData)
    } catch (error) {
      console.error('Error loading schedules:', error)
    } finally {
      setLoading(false)
    }
  }

  // Calcular estadísticas
  const stats = {
    totalEmployees: employees.length,
    activeToday: schedules.filter(s => s.status === 'WORK').length,
    onVacation: schedules.filter(s => s.status === 'VACATION').length,
    absent: schedules.filter(s => s.status === 'SICK_LEAVE' || s.status === 'OTHER_ABSENCE').length,
  }

  const departmentStats = departments.map(dept => {
    const deptEmployees = employees.filter(emp => emp.department === dept.name)
    const deptSchedules = schedules.filter(s =>
      deptEmployees.some(emp => emp.id === s.employee_id)
    )

    return {
      ...dept,
      employeeCount: deptEmployees.length,
      activeToday: deptSchedules.filter(s => s.status === 'WORK').length,
      onVacation: deptSchedules.filter(s => s.status === 'VACATION').length,
    }
  })

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600">
            {selectedDepartment === 'all' 
              ? 'Vista general de todos los departamentos'
              : `Departamento: ${selectedDepartment}`
            }
          </p>
        </div>
        <div className="text-right">
          <p className="text-sm text-gray-500">Última actualización</p>
          <p className="text-sm font-medium text-gray-900">
            {new Date().toLocaleTimeString('es-ES')}
          </p>
        </div>
      </div>

      {/* Estadísticas principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="card">
          <div className="card-content">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Users className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Empleados</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalEmployees}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-content">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <UserCheck className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Activos Hoy</p>
                <p className="text-2xl font-bold text-gray-900">{stats.activeToday}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-content">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Calendar className="h-8 w-8 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">De Vacaciones</p>
                <p className="text-2xl font-bold text-gray-900">{stats.onVacation}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-content">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Clock className="h-8 w-8 text-red-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Ausentes</p>
                <p className="text-2xl font-bold text-gray-900">{stats.absent}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Estadísticas por departamento */}
      {selectedDepartment === 'all' && (
        <div className="card">
          <div className="card-header">
            <h2 className="text-lg font-semibold text-gray-900 flex items-center">
              <Building2 className="h-5 w-5 mr-2" />
              Estadísticas por Departamento
            </h2>
          </div>
          <div className="card-content">
            <DepartmentStats departments={departmentStats} />
          </div>
        </div>
      )}

      {/* Lista de empleados */}
      <div className="card">
        <div className="card-header">
          <h2 className="text-lg font-semibold text-gray-900">
            Empleados {selectedDepartment !== 'all' && `- ${selectedDepartment}`}
          </h2>
          <p className="text-sm text-gray-500">
            {employees.length} empleado{employees.length !== 1 ? 's' : ''} encontrado{employees.length !== 1 ? 's' : ''}
          </p>
        </div>
        <div className="card-content">
          {employees.length === 0 ? (
            <div className="text-center py-12">
              <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No hay empleados para mostrar</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {employees.map((employee) => (
                <EmployeeCard
                  key={employee.id}
                  employee={employee}
                  schedule={schedules.find(s => s.employee_id === employee.id)}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default Dashboard
