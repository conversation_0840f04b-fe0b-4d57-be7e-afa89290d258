import React, { useState, useEffect } from 'react'
import {
  Users, Calendar, Clock, TrendingUp, Building2, UserCheck,
  Home, Wifi, Monitor, MapPin, Coffee, Video, Phone, UserX
} from 'lucide-react'
import EmployeeLocationCard from './EmployeeLocationCard'
import LocationOverview from './LocationOverview'
import LiveStatusBoard from './LiveStatusBoard'
import { api } from '../lib/supabase'

const Dashboard = ({ employees, departments, selectedDepartment }) => {
  const [schedules, setSchedules] = useState([])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    loadTodaySchedules()
  }, [])

  const loadTodaySchedules = async () => {
    try {
      setLoading(true)
      const today = new Date().toISOString().split('T')[0]
      const schedulesData = await api.getSchedules(today, today)
      setSchedules(schedulesData)
    } catch (error) {
      console.error('Error loading schedules:', error)
    } finally {
      setLoading(false)
    }
  }

  // Calcular estadísticas por ubicación y estado
  const locationStats = {
    office: employees.filter(emp => emp.work_location === 'OFFICE').length,
    remote: employees.filter(emp => emp.work_location === 'REMOTE').length,
    hybrid: employees.filter(emp => emp.work_location === 'HYBRID').length,
  }

  const statusStats = {
    available: employees.filter(emp => emp.current_status === 'AVAILABLE').length,
    busy: employees.filter(emp => emp.current_status === 'BUSY').length,
    meeting: employees.filter(emp => emp.current_status === 'MEETING').length,
    break: employees.filter(emp => emp.current_status === 'BREAK').length,
    offline: employees.filter(emp => emp.current_status === 'OFFLINE').length,
  }

  const workingToday = schedules.filter(s => s.status === 'WORK').length
  const onVacation = schedules.filter(s => s.status === 'VACATION').length

  return (
    <div className="space-y-6">
      {/* Header con tiempo real */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <Monitor className="h-8 w-8 mr-3 text-blue-600" />
            MSX Team Live Dashboard
          </h1>
          <p className="text-gray-600 mt-1">
            Control visual en tiempo real de ubicaciones y estados del equipo
          </p>
        </div>
        <div className="text-right">
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span>En vivo</span>
          </div>
          <p className="text-lg font-semibold text-gray-900">
            {new Date().toLocaleTimeString('es-ES')}
          </p>
        </div>
      </div>

      {/* Estadísticas de ubicación */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
        <div className="card bg-gradient-to-r from-blue-500 to-blue-600 text-white">
          <div className="card-content">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-100 text-sm">En Oficina</p>
                <p className="text-3xl font-bold">{locationStats.office}</p>
              </div>
              <Building2 className="h-10 w-10 text-blue-200" />
            </div>
          </div>
        </div>

        <div className="card bg-gradient-to-r from-green-500 to-green-600 text-white">
          <div className="card-content">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-100 text-sm">Remoto</p>
                <p className="text-3xl font-bold">{locationStats.remote}</p>
              </div>
              <Home className="h-10 w-10 text-green-200" />
            </div>
          </div>
        </div>

        <div className="card bg-gradient-to-r from-purple-500 to-purple-600 text-white">
          <div className="card-content">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-100 text-sm">Híbrido</p>
                <p className="text-3xl font-bold">{locationStats.hybrid}</p>
              </div>
              <Wifi className="h-10 w-10 text-purple-200" />
            </div>
          </div>
        </div>

        <div className="card bg-gradient-to-r from-yellow-500 to-yellow-600 text-white">
          <div className="card-content">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-yellow-100 text-sm">Trabajando</p>
                <p className="text-3xl font-bold">{workingToday}</p>
              </div>
              <UserCheck className="h-10 w-10 text-yellow-200" />
            </div>
          </div>
        </div>

        <div className="card bg-gradient-to-r from-orange-500 to-orange-600 text-white">
          <div className="card-content">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-100 text-sm">Vacaciones</p>
                <p className="text-3xl font-bold">{onVacation}</p>
              </div>
              <Calendar className="h-10 w-10 text-orange-200" />
            </div>
          </div>
        </div>
      </div>

      {/* Panel de estado en vivo */}
      <LiveStatusBoard employees={employees} statusStats={statusStats} />

      {/* Vista por ubicaciones */}
      <LocationOverview employees={employees} selectedDepartment={selectedDepartment} />

      {/* Grid de empleados con diseño visual mejorado */}
      <div className="card">
        <div className="card-header">
          <h2 className="text-xl font-semibold text-gray-900 flex items-center">
            <Users className="h-6 w-6 mr-2" />
            Equipo MSX
            {selectedDepartment !== 'all' && (
              <span className="ml-2 px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
                {selectedDepartment}
              </span>
            )}
          </h2>
          <p className="text-gray-600">
            {employees.length} miembro{employees.length !== 1 ? 's' : ''} del equipo
          </p>
        </div>
        <div className="card-content">
          {employees.length === 0 ? (
            <div className="text-center py-12">
              <Users className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500 text-lg">No hay empleados para mostrar</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {employees.map((employee) => (
                <EmployeeLocationCard
                  key={employee.id}
                  employee={employee}
                  schedule={schedules.find(s => s.employee_id === employee.id)}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default Dashboard
