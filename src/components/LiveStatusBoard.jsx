import React from 'react'
import { CheckCircle, AlertCircle, Coffee, Video, UserX, Activity } from 'lucide-react'

const LiveStatusBoard = ({ employees, statusStats }) => {
  const statusConfig = {
    AVAILABLE: {
      icon: CheckCircle,
      label: 'Disponibles',
      color: 'green',
      bgColor: 'bg-green-500',
      lightBg: 'bg-green-50',
      textColor: 'text-green-700'
    },
    BUSY: {
      icon: AlertCircle,
      label: 'Ocupados',
      color: 'red',
      bgColor: 'bg-red-500',
      lightBg: 'bg-red-50',
      textColor: 'text-red-700'
    },
    MEETING: {
      icon: Video,
      label: 'En reunión',
      color: 'orange',
      bgColor: 'bg-orange-500',
      lightBg: 'bg-orange-50',
      textColor: 'text-orange-700'
    },
    BREAK: {
      icon: Coffee,
      label: 'En descanso',
      color: 'yellow',
      bgColor: 'bg-yellow-500',
      lightBg: 'bg-yellow-50',
      textColor: 'text-yellow-700'
    },
    OFFLINE: {
      icon: UserX,
      label: 'Desconectados',
      color: 'gray',
      bgColor: 'bg-gray-500',
      lightBg: 'bg-gray-50',
      textColor: 'text-gray-700'
    }
  }

  const getEmployeesByStatus = (status) => {
    return employees.filter(emp => emp.current_status === status)
  }

  return (
    <div className="card">
      <div className="card-header">
        <h2 className="text-xl font-semibold text-gray-900 flex items-center">
          <Activity className="h-6 w-6 mr-2 text-blue-600" />
          Panel de Estado en Vivo
          <div className="ml-3 flex items-center">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-2"></div>
            <span className="text-sm text-gray-500">Actualización en tiempo real</span>
          </div>
        </h2>
      </div>
      
      <div className="card-content">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          {Object.entries(statusConfig).map(([status, config]) => {
            const Icon = config.icon
            const count = statusStats[status.toLowerCase()] || 0
            const employeesInStatus = getEmployeesByStatus(status)
            
            return (
              <div key={status} className={`${config.lightBg} rounded-lg p-4 border-l-4 border-${config.color}-500`}>
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center">
                    <Icon className={`h-5 w-5 mr-2 text-${config.color}-600`} />
                    <span className={`font-medium ${config.textColor}`}>
                      {config.label}
                    </span>
                  </div>
                  <span className={`text-2xl font-bold ${config.textColor}`}>
                    {count}
                  </span>
                </div>
                
                {/* Lista de empleados en este estado */}
                <div className="space-y-1 max-h-32 overflow-y-auto">
                  {employeesInStatus.slice(0, 5).map((employee) => (
                    <div key={employee.id} className="flex items-center text-sm">
                      <div 
                        className="w-6 h-6 rounded-full flex items-center justify-center text-white text-xs mr-2"
                        style={{ backgroundColor: employee.avatar_color || '#6B7280' }}
                      >
                        {employee.name.charAt(0)}
                      </div>
                      <span className="text-gray-700 truncate">
                        {employee.name}
                      </span>
                    </div>
                  ))}
                  
                  {employeesInStatus.length > 5 && (
                    <div className="text-xs text-gray-500 mt-1">
                      +{employeesInStatus.length - 5} más
                    </div>
                  )}
                  
                  {employeesInStatus.length === 0 && (
                    <div className="text-xs text-gray-400 italic">
                      Ningún empleado
                    </div>
                  )}
                </div>
              </div>
            )
          })}
        </div>
        
        {/* Barra de progreso visual */}
        <div className="mt-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">
              Distribución del equipo
            </span>
            <span className="text-sm text-gray-500">
              {employees.length} total
            </span>
          </div>
          
          <div className="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
            <div className="h-full flex">
              {Object.entries(statusStats).map(([status, count], index) => {
                const percentage = employees.length > 0 ? (count / employees.length) * 100 : 0
                const config = statusConfig[status.toUpperCase()]
                
                if (percentage === 0) return null
                
                return (
                  <div
                    key={status}
                    className={`${config?.bgColor || 'bg-gray-400'} transition-all duration-500`}
                    style={{ width: `${percentage}%` }}
                    title={`${config?.label || status}: ${count} (${percentage.toFixed(1)}%)`}
                  />
                )
              })}
            </div>
          </div>
          
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>0%</span>
            <span>50%</span>
            <span>100%</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default LiveStatusBoard
