import React, { useState } from 'react'
import { Building2, Home, Wifi, MapPin, Users, Clock, Globe } from 'lucide-react'

const LocationOverview = ({ employees, selectedDepartment }) => {
  const [selectedLocation, setSelectedLocation] = useState('all')

  const locationConfig = {
    OFFICE: {
      icon: Building2,
      label: 'Oficina',
      color: 'blue',
      bgColor: 'bg-blue-500',
      lightBg: 'bg-blue-50',
      borderColor: 'border-blue-200',
      textColor: 'text-blue-700',
      description: 'Trabajando desde las oficinas de MSX'
    },
    REMOTE: {
      icon: Home,
      label: 'Trabajo Remoto',
      color: 'green',
      bgColor: 'bg-green-500',
      lightBg: 'bg-green-50',
      borderColor: 'border-green-200',
      textColor: 'text-green-700',
      description: 'Trabajando desde casa u otra ubicación'
    },
    HYBRID: {
      icon: Wifi,
      label: 'Modalidad Híbrida',
      color: 'purple',
      bgColor: 'bg-purple-500',
      lightBg: 'bg-purple-50',
      borderColor: 'border-purple-200',
      textColor: 'text-purple-700',
      description: 'Combinando oficina y trabajo remoto'
    }
  }

  const getEmployeesByLocation = (location) => {
    let filtered = employees
    
    if (selectedDepartment !== 'all') {
      filtered = filtered.filter(emp => emp.department === selectedDepartment)
    }
    
    if (location !== 'all') {
      filtered = filtered.filter(emp => emp.work_location === location)
    }
    
    return filtered
  }

  const getLocationStats = () => {
    const stats = {}
    Object.keys(locationConfig).forEach(location => {
      stats[location] = getEmployeesByLocation(location).length
    })
    return stats
  }

  const locationStats = getLocationStats()
  const filteredEmployees = getEmployeesByLocation(selectedLocation)

  const getCountryFlag = (countryCode) => {
    const flags = {
      'ES': '🇪🇸',
      'IT': '🇮🇹',
      'FR': '🇫🇷',
      'DE': '🇩🇪',
      'BR': '🇧🇷',
    }
    return flags[countryCode] || '🌍'
  }

  const getCurrentTime = (timezone) => {
    try {
      return new Date().toLocaleTimeString('es-ES', {
        timeZone: timezone || 'Europe/Madrid',
        hour: '2-digit',
        minute: '2-digit'
      })
    } catch {
      return new Date().toLocaleTimeString('es-ES', {
        hour: '2-digit',
        minute: '2-digit'
      })
    }
  }

  return (
    <div className="card">
      <div className="card-header">
        <h2 className="text-xl font-semibold text-gray-900 flex items-center">
          <MapPin className="h-6 w-6 mr-2 text-blue-600" />
          Vista por Ubicaciones de Trabajo
        </h2>
        <p className="text-gray-600">
          Distribución del equipo por modalidad de trabajo
        </p>
      </div>
      
      <div className="card-content">
        {/* Selector de ubicación */}
        <div className="flex flex-wrap gap-3 mb-6">
          <button
            onClick={() => setSelectedLocation('all')}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              selectedLocation === 'all'
                ? 'bg-gray-900 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            <Users className="h-4 w-4 inline mr-2" />
            Todas las ubicaciones ({employees.length})
          </button>
          
          {Object.entries(locationConfig).map(([location, config]) => {
            const Icon = config.icon
            const count = locationStats[location]
            
            return (
              <button
                key={location}
                onClick={() => setSelectedLocation(location)}
                className={`px-4 py-2 rounded-lg font-medium transition-colors flex items-center ${
                  selectedLocation === location
                    ? `${config.bgColor} text-white`
                    : `${config.lightBg} ${config.textColor} hover:${config.bgColor} hover:text-white`
                }`}
              >
                <Icon className="h-4 w-4 mr-2" />
                {config.label} ({count})
              </button>
            )
          })}
        </div>

        {/* Información de la ubicación seleccionada */}
        {selectedLocation !== 'all' && (
          <div className={`${locationConfig[selectedLocation].lightBg} ${locationConfig[selectedLocation].borderColor} border rounded-lg p-4 mb-6`}>
            <div className="flex items-center mb-2">
              {React.createElement(locationConfig[selectedLocation].icon, {
                className: `h-5 w-5 mr-2 ${locationConfig[selectedLocation].textColor}`
              })}
              <h3 className={`font-semibold ${locationConfig[selectedLocation].textColor}`}>
                {locationConfig[selectedLocation].label}
              </h3>
            </div>
            <p className="text-sm text-gray-600">
              {locationConfig[selectedLocation].description}
            </p>
          </div>
        )}

        {/* Grid de empleados por ubicación */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {filteredEmployees.map((employee) => {
            const locationInfo = locationConfig[employee.work_location]
            const LocationIcon = locationInfo?.icon || MapPin
            
            return (
              <div
                key={employee.id}
                className={`${locationInfo?.lightBg || 'bg-gray-50'} ${locationInfo?.borderColor || 'border-gray-200'} border rounded-lg p-4 hover:shadow-md transition-shadow`}
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center">
                    <div 
                      className="h-10 w-10 rounded-full flex items-center justify-center text-white font-semibold text-sm mr-3"
                      style={{ backgroundColor: employee.avatar_color || '#6B7280' }}
                    >
                      {employee.name.charAt(0)}
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 text-sm">
                        {employee.name}
                      </h4>
                      <p className="text-xs text-gray-500">
                        {employee.department}
                      </p>
                    </div>
                  </div>
                  
                  <LocationIcon className={`h-5 w-5 ${locationInfo?.textColor || 'text-gray-500'}`} />
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-gray-500">Ubicación:</span>
                    <span className={`font-medium ${locationInfo?.textColor || 'text-gray-700'}`}>
                      {locationInfo?.label || 'No definido'}
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-gray-500">País:</span>
                    <span className="font-medium text-gray-700">
                      {getCountryFlag(employee.country_code)} {employee.country_code}
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-gray-500">Hora local:</span>
                    <span className="font-medium text-gray-700 flex items-center">
                      <Clock className="h-3 w-3 mr-1" />
                      {getCurrentTime(employee.timezone)}
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-gray-500">Estado:</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      employee.current_status === 'AVAILABLE' ? 'bg-green-100 text-green-800' :
                      employee.current_status === 'BUSY' ? 'bg-red-100 text-red-800' :
                      employee.current_status === 'MEETING' ? 'bg-orange-100 text-orange-800' :
                      employee.current_status === 'BREAK' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {employee.current_status === 'AVAILABLE' ? 'Disponible' :
                       employee.current_status === 'BUSY' ? 'Ocupado' :
                       employee.current_status === 'MEETING' ? 'Reunión' :
                       employee.current_status === 'BREAK' ? 'Descanso' :
                       'Offline'}
                    </span>
                  </div>
                </div>
              </div>
            )
          })}
        </div>
        
        {filteredEmployees.length === 0 && (
          <div className="text-center py-12">
            <MapPin className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500 text-lg">
              No hay empleados en esta ubicación
            </p>
          </div>
        )}
      </div>
    </div>
  )
}

export default LocationOverview
