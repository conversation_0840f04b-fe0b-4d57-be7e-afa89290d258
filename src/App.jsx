import React, { useState, useEffect } from 'react'
import UnifiedDashboard from './components/UnifiedDashboard'
import { api } from './lib/supabase'

function App() {
  const [employees, setEmployees] = useState([])
  const [departments, setDepartments] = useState([])
  const [schedules, setSchedules] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadData()
    // Actualizar datos cada 30 segundos para simular tiempo real
    const interval = setInterval(loadData, 30000)
    return () => clearInterval(interval)
  }, [])

  const loadData = async () => {
    try {
      setLoading(true)
      const [employeesData, departmentsData, schedulesData] = await Promise.all([
        api.getEmployees(),
        api.getDepartments(),
        api.getSchedules(
          new Date().toISOString().split('T')[0],
          new Date().toISOString().split('T')[0]
        )
      ])
      setEmployees(employeesData)
      setDepartments(departmentsData)
      setSchedules(schedulesData)
    } catch (error) {
      console.error('Error loading data:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="relative">
            <div className="animate-spin rounded-full h-32 w-32 border-4 border-blue-200 border-t-blue-600 mx-auto"></div>
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-2xl font-bold text-blue-600">MSX</div>
            </div>
          </div>
          <p className="mt-6 text-xl text-gray-700 font-medium">Cargando MSX Super Team...</p>
          <p className="mt-2 text-gray-500">Dashboard centralizado</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <UnifiedDashboard
        employees={employees}
        departments={departments}
        schedules={schedules}
        onRefresh={loadData}
      />
    </div>
  )
}

export default App
