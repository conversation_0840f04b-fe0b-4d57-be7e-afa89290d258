import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://iexhexcpzjndpgobexnh.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlleGhleGNwempuZHBnb2JleG5oIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwMjI4MTksImV4cCI6MjA2NTU5ODgxOX0.eH_Ii5SEVkjFyt01hJyyj73Lm2ST5puLLE3CSGxWrHM'

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Funciones helper para la API
export const api = {
  // Obtener todos los departamentos
  async getDepartments() {
    const { data, error } = await supabase
      .from('departments')
      .select('*')
      .eq('active', true)
      .order('name')

    if (error) throw error
    return data
  },

  // Obtener todos los empleados
  async getEmployees() {
    const { data, error } = await supabase
      .from('employees')
      .select('*')
      .eq('active', true)
      .order('full_name')

    if (error) throw error
    return data
  },

  // Obtener empleados por departamento
  async getEmployeesByDepartment(department) {
    const { data, error } = await supabase
      .from('employees')
      .select('*')
      .eq('department', department)
      .eq('active', true)
      .order('full_name')

    if (error) throw error
    return data
  },

  // Obtener horarios por rango de fechas
  async getSchedules(startDate, endDate) {
    const { data, error } = await supabase
      .from('schedules')
      .select(`
        *,
        employees!inner(*)
      `)
      .gte('schedule_date', startDate)
      .lte('schedule_date', endDate)
      .order('schedule_date')

    if (error) throw error
    return data
  },

  // Obtener horarios de un empleado específico
  async getEmployeeSchedules(employeeId, startDate, endDate) {
    const { data, error } = await supabase
      .from('schedules')
      .select('*')
      .eq('employee_id', employeeId)
      .gte('schedule_date', startDate)
      .lte('schedule_date', endDate)
      .order('schedule_date')

    if (error) throw error
    return data
  },

  // Crear o actualizar horario
  async upsertSchedule(schedule) {
    const { data, error } = await supabase
      .from('schedules')
      .upsert(schedule)
      .select()

    if (error) throw error
    return data
  },

  // Eliminar horario
  async deleteSchedule(id) {
    const { error } = await supabase
      .from('schedules')
      .delete()
      .eq('id', id)

    if (error) throw error
  }
}